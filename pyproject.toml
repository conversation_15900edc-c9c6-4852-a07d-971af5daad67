[tool.poetry]
name = "zillionare"
version = "0.1.0"
description = ""
authors = ["aaron yang <<EMAIL>>"]
readme = "README.md"
# 移除了 package-mode = false

[[tool.poetry.source]]
name = "ali"
url = "https://mirrors.aliyun.com/pypi/simple"
default = true  # 标记为默认源

[tool.poetry.dependencies]
python = "^3.12"
mkdocs-material = "^9.6.14"
mkdocs-include-markdown-plugin = "^7.1.5"
mkdocstrings = "^0.29.1"
mkdocs-autorefs = "^1.4.2"
livereload = "^2.7.1"
mike = "^2.1.3"
mkdocs-awesome-pages-plugin = "^2.10.1"
markdown = "^3.8"
python-frontmatter = "^1.1.0"
mkdocs-redirects = "^1.2.2"
pyyaml = "^6.0.2"
nbformat = "^5.10.4"
arrow = "^1.3.0"
black = "^25.1.0"
fire = "^0.7.0"
loguru = "^0.7.3"
traitlets = "^5.14.3"
mkdocs = "^1.6.1"
fastjsonschema = "^2.21.1"
jsonschema = "^4.24.0"
attrs = "^25.3.0"
referencing = "^0.36.2"
rpds-py = "^0.25.1"
jsonschema-specifications = "^2025.4.1"
python-slugify = "^8.0.4"
notedown = "^1.5.1"
nbclient = "^0.10.2"
jupyter-client = "^8.6.3"
zmq = "^0.0.0"
jupyter-core = "^5.8.1"
jupyterlab-pygments = "^0.3.0"
pygments = "^2.19.1"
nbconvert = "6.5.4"
lxml = {extras = ["html-clean"], version = "^5.4.0"}
lxml-html-clean = "^0.4.2"
typing-extensions = "^4.13.2"
setuptools = "^80.9.0"
mergedeep = "^1.3.4"
watchdog = "^6.0.0"
pymdown-extensions = "^10.15"
bracex = "^2.5.post1"
babel = "^2.17.0"
paginate = "^0.5.7"
backrefs = "^5.8"
urllib3 = "^2.4.0"
mkdocs-rss-plugin = "^1.17.3"
click = "^8.2.1"
pathspec = "^0.12.1"
gitpython = "^3.1.44"
mypy-extensions = "^1.1.0"
ghp-import = "^2.1.0"
requests = "^2.32.3"
statsmodels = "^0.14.4"
scipy = "^1.15.3"
matplotlib = "^3.10.3"
plotly = "^6.1.2"
polars = "^1.30.0"
seaborn = "^0.13.2"
numpy = "2.3.0"
pandas = "^2.3.0"
alphalens-reloaded = "^0.4.6"
# News crawler dependencies
feedparser = "^6.0.10"
beautifulsoup4 = "^4.12.2"
markdownify = "^0.11.6"
openai = "^1.3.0"
python-dateutil = "^2.8.2"
html2text = "^2020.1.16"
schedule = "^1.2.2"

[build-system]
requires = ["setuptools>=61.0"]  # 改用setuptools后端
build-backend = "setuptools.build_meta"
