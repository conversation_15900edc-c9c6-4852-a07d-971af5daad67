---
title: 因子分析与机器学习
subtitle: 因子分析 ⚔ 回测
layout: cover
clicks: 16
welcome_audio: downfall.mp3
---

<style>
.the-end-wrapper {
    position:absolute;
    background: rgb(0,0,0, 0.6);
    height:100%;
    width:100%;
    top:-20vh;
    left:0;
    perspective: 300px;
}

.the-end {
    transform: rotateX(60deg);
    overflow-y: hidden;
    padding: 10% 0;
}

.scrollable {
    p {
        color: white;
        font-size: 4vw;
    }
    animation: the-end-motion 5s linear;
    width: 100%;
    height: 400px;
    transform: translateY(-400px);
}

@keyframes the-end-motion {
    0% {
        transform: translateY(200px);
    }

    100% {
        transform: translateY(-400px);
    }
}

</style>

<div v-motion :click-6="{ x: 400, y: -50, scale: 0.5 }" :duration="2000">
<v-clicks>

### :satellite: 回测的特点
#### 模拟交易细节（佣金、滑点、资产流动性）
#### 贴近实盘
#### 可以实现更复杂的策略
#### 实施难度更高、耗时更长

</v-clicks>
</div>

<div v-motion 
    :click-6="{y: -200, scale: 1}" 
    :click-15="{x: 400, y: -100, scale: 0.5}"
    :duration="500" 
    :enter="{scale: 0.5}">
<v-clicks>

###  🙋‍♀️ 因子分析能回答哪些问题？
#### 因子是否有预测性？
#### 因子的预测能力有多强？
#### 该因子是否对整个股票池中都有效？
#### 该因子是否对所有行业都有效？
#### 资产做多、做空如何分配头寸？
#### 持仓时间？应该每天、还是每周调仓？
#### 不同持有期间的换手率多高？
#### 该因子的绝对收益、风险调整收益多高？
</v-clicks>
</div>

<div v-motion 
    :click-15="{x:-200, y: -750, scale: 0.5}"
    :duration="500"
    :enter="{scale: 0}">
<v-click>
    <div style='text-align:center;margin-bottom:1rem'>
    <img src="https://images.jieyu.ai/images/2024/02/量化交易策略的基本结构.jpg">
    <span style='font-style:italic;font-size:2rem'>《打开量化投资的黑箱》</span>
    </div>
</v-click>
</div>


<Cast :at=16 :dur=20 left="30%" w="60%">
<div style="text-align:center;">
<div style="padding:100px 20px;background: url('https://images.jieyu.ai/images/hot/black-gold.jpg') repeat-y center top / contain">
《大富翁.量化二十四课》

### 视频

<video src="https://images.jieyu.ai/images/hot/video-sample.mp4" preload autoplay loop></video>

### Notebook文稿和代码

![75%](https://images.jieyu.ai/images/hot/course-screenshot.jpg)

### 1对1指导

量化投研系统专家、大富翁架构师、《Python高效编程实践指南》作者一对一指导。

<div style="width:100%">
<img src="https://images.jieyu.ai/images/hot/book-cover.jpg" style="float:left;width:200px">

<img src="https://images.jieyu.ai/images/hot/me.png" style="width:200px">
</div>


✥ 超翔实内容：文字稿约40万字节。

✥ 快速上手：环境基于Jupyter Lab构建，在线使用，无须安装和拷贝数据。

✥ 昂贵的商业数据：超过30亿条分钟级行情数据

✥ 贴近实战：提供真实数据回测环境

✥ 豪华环境：192核CPU和256GB内存（学员共享）


</div>

</div>
</Cast>

<Audio src="https://images.jieyu.ai/images/sounds/aggressive-hit-logo.mp3" 
        :at=16
        :delay=3 />

<!--
欢迎来到量化风云频道！做每个量化人的视频读物，每天1分钟，轻松学量化

今天我们介绍的知识点是，因子分析与回测的区别

[click]
量化是就是建立策略模型，并通过大量的历史数据来评估模型的优劣。评估方法主要有两种，一种是因子检验，另一种就是回测。
这两个概念您可能之前都有听说过，那么，两者究竟有何区别，在什么场合下选择因子分析，什么场合下选择回测呢？

我们先来看回测。回测有以下特点:

[click]

一、在中低频量化交易中，回测几乎可以模拟一切交易细节。

[click]

二、因此，它非常贴近实盘

[click]

三、也可以用来实现非常复杂的策略

[click]

四、但是，与因子分析相比，它的实施难度高、开发时间会更长一些

[click]

那么，因子分析是干什么的，它能回答哪些问题呢？

[click]

因子分析能回答这些问题，比如， 因子是否有预测性？

[click]

因子的预测能力有多强？我们通过一些t检验指标来判断。

[click]

因子是否对整个股票池都有效？我们常常看到有人问，我在做回测时，如何实现多支股票的回测？这实际上是一个投资组合的策略回测问题，在回测中可能实现起来需要一定的技巧，但在因子检验中，现有的工具已经自动处理了，不用我们写一行代码。

[click]

对看重风险的人来说，鸡蛋最好不要放进同一个篮子中。现代金融理论之父，马科维茨就认为，资产配置多元化是投资的唯一免费午餐。出于风险平衡的考虑，我们可能不希望所有的头寸都分配到同一个行业上。这一点，使用因子检验框架也很容易做到。

[click]

对中性策略来讲，我们会特别关心如何给多头和空头分配头寸。这也是因子检验中可以实现的功能。

[click]

如果我们进行每天调仓、或者每周调仓，因子收益会不会有什么不同？

[click]

如果使用每天调仓、或者每周调仓，两者的换手率是如何呢？

[click]

当然，我们可能更关心的，还是因子的绝对收益率和调整收益率。毕竟，爱拼才会赢，我们就是要赢。

[click]

最后，我们通过这张图来了解一下因子分析与回测的位置。这张图来自《打开量化投资的黑箱》，这本书是量化入门的畅销书，很多人正是通过这本书，认识了量化，跨入了量化的大门。

[click]

这里的阿尔法模型，就是因子。投资组合构建模型，则大致对应着回测策略。

为什么阿尔法模型就是因子？其中的由来，我们以后再介绍。

[click]

好啦，今天的话说就到这里了，感谢收听。关注我，做您的量化视频读物，轻松学量化。
-->
