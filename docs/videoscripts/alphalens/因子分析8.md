---
title: 未卜先知！quantile分层重叠，是Bug还是Feature？
subtitle: quantile分层出现重叠，这是Bug吗？
seq: FA01-008 因子分析与机器学习
slug: factor-analysis-8
date: 2024-07-29
category: factors
tags:
  - 因子分析
  - Alphalens
  - 未来数据
lineNumbers: true
aspectRatio: 3/4
---

---
clicks: 42
---

<style scoped>
.shadows {
    filter: drop-shadow(0 5px 20px rgba(0,0,0,0.8));
}
.bg {
  width: 100%;
  height: 100%;
  background-size: 90% 100%;
  background-position: center;
  position: absolute;
}
.step-0-bg {
    background-image: url('https://images.jieyu.ai/images/2024/07/why-alphalens-quantiles-overlap.jpg')
}
.step-1-bg {
  background-image: url('https://images.jieyu.ai/images/2024/07/cut-by-quantile-small.png');
}
</style>

<Device kind="iphone-14-pro" class="abs top-10% left-30% shadows"
        :enter="{rotate: '45deg',opacity: 1}"
        :click-3="{rotate: '90deg',opacity: 0}"
        >
<div class="bg step-0-bg"/>
</Device>


<Device kind="iphone-14-pro" class="abs top-10% left-30% shadows"
        :enter="{opacity:0, rotate: '45deg', scale:1}"
        :click-3="{opacity:1, rotate: '90deg'}"
        :click-43="{scale:0}">

<div class="bg step-1-bg"/>

</Device>

<v-drag v-click=[5,6] class="abs w-100px h-80px" pos="269,470,620,76">

<Box></Box>
</v-drag>

<v-drag v-click=[6,7] class="abs w-100px h-80px" pos="531,458,198,97">

<Ellipse></Ellipse>
</v-drag>

<v-drag v-click=[7,8] class="abs w-100px h-80px" pos="263,527,653,73">

<Box/>
</v-drag>

<v-drag v-click=[10,11] class="abs w-100px h-80px" pos="346,521,193,71">

<Box/>
</v-drag>

<SoarText v-click=[0,43] class="abs top-150px w-80% h-80% left-50px" colorMix="None">

```md

有同学问
为什么上一期视频中
by quantiles分层出现重叠
他所说的分层重叠
就是指这里
第一组的最大值
是78.8
按理说第二组
应该从78.8开始
但实际上
却是从10.5开始
这里发生了
overlapping
是不是Alphalens出bug了

其实
这正是我们在做量化分析时
能用框架
就要尽可能用框架的道理
Alphalens在这里
正是规避了一个常见的错误，即：
未来数据
当我们基于过去
1000天的数据统计
来给RSI因子
按quantile分层时
当然就不会
出现重叠现象
但是
这样却引入了未来数据
即在第1天回测时
分层数据使用了此后
999天的数据
这是一个让你略微想一想
就马上能明白的道理
但是如果我们自己来实现分层回测
纷繁复杂的场景和条件分支
就很容易让我们迷失在
自己关注的细节中
从而不知不觉让未来数据溜了进来
这也是很多人
回测买地球
实盘亏成狗

```
</SoarText>

---
clicks: 1
---

<div v-motion class="flex flex-col justify-center items-center"
    :enter="{scale:0,y:400}"
    :click-1="{scale:1, transition: { duration: 2000 }}">
<QtBrand class="w-250px" />
<div class="text-8xl mt-20px" style="text-shadow: 2px 5px 10px rgba(0,0,0,0.5)">THE END</div>
</div>

<!--

有同学问，为什么上一期视频中，by quantiles分层出现重叠？是不是Alphalens出bug了？

事实上，看上去不合理的结果，却正是Alphalens的优秀之外：它在这里规避了一个可能引入未来数据的错误。

Alphalens在进行分层时，是在每日因子值内部进行的分层，而不是把所有因子值放在一起，然后再进行分层。

由于每一日的因子值分布不一样，所以按quantile进行分层，就容易出现overlapping的情况，并且，从因子预测的角度上看，也可能降低了因子有效性。

如果将所有期的因子值放在一起进行统计再by quantile分层，这样看起来结果会更make sense，但却不小心引入了未来数据。

定律一：凡是使用统计函数的地方，都有可能引入未来数据。
定律二：凡是可能出错的地方，最终一定会出错。

你可能之前接触过因子分析。但很有可能从来没有意识到会有这么多坑。关注QuanTide 量化风云，持续为你更新。

-->
