---
title: Day06 因子分析与机器学习
subtitle: get_clean_factor_and_forward_returns的其它参数
seq: FA01-006 因子分析与机器学习策略
slug: factor-analysis-6
date: 2024-07-26
category:
  - 因子分析
tags:
  - 因子分析
  - Alphalens
lineNumbers: true
layout: cover-wechat
---

---
clicks: 20
---


<div class="abs top-10%" style="transform: scale(0.9)">

<h2 v-click="1">缺失值</h2>
<h3 v-click="1">max_loss</h3>
<h3 v-click="4">filter_zscore</h3>
<h2 v-click="5">收益计算</h2>
<h3 v-click="5"> periods</h3>
<h3 v-click="7"> cumulative_returns</h3>
<h2 v-click="8"> 中性化</h2>
<h3 v-click="8"> groupby</h3>
<h3 v-click="11"> groupby_labels</h3>
<h3 v-click="12"> binning_by_group</h3>
</div>

<div class="text-2xl;" style="transform: translateX(200px) translateY(-50px)"
    v-motion
    :enter="{scale:0.5}"
    :click-3="{scale:0}"
    :click-4="{scale:0.5}"
    :click-6="{scale:0}"
    :click-7="{scale:0.5}"
    :click-9="{scale:0}"
    :click-11="{scale:0.5}">

<v-drag pos="40,550,98,98">

<Ellipse/>
</v-drag>


<v-drag pos="180,400,120,80">

<Box light="0%" > 分层</Box>
</v-drag>

<v-drag pos="180,510,120,80">

<Box :hue1=1 :hue2=1 :hue3=1 > 缺失(极)值</Box>
</v-drag>

<v-drag pos="180,620,120,80">

<Box :hue1=1 :hue2=1 :hue3=1 > 收益计算</Box>
</v-drag>

<v-drag pos="180,730,120,80">

<Box :hue1=1 :hue2=1 :hue3=1 > 中性化</Box>
</v-drag>

<v-drag pos="620,280,220,80">

<Box :hue1=1 :hue2=1 :hue3=1 > zeroaware</Box>
</v-drag>

<v-drag pos="620,370,220,80">

<Box light="10%"  > quantiles</Box>
</v-drag>

<v-drag pos="620,460,220,80">

<Box light="0%"  > bins</Box>
</v-drag>

<v-drag pos="620,660,220,80">

<Box :hue1=1 :hue2=1 :hue3=1 > groupby</Box>
</v-drag>

<v-drag pos="620,750,220,80">

<Box :hue1=1 :hue2=1 :hue3=1 > groupby_labels</Box>
</v-drag>

<v-drag pos="620,840,220,80">

<Box :hue1=1 :hue2=1 :hue3=1 > binning_by_group</Box>
</v-drag>


<v-drag pos="350,370,230,80">

<Box :hue1=1 :hue2=1 :hue3=1 > max_loss</Box>
</v-drag>

<v-drag pos="350,480,230,80">

<Box :hue1=1 :hue2=1 :hue3=1 > filter_zscore</Box>
</v-drag>


<v-drag pos="350,640,230,80">

<Box :hue1=1 :hue2=1 :hue3=1 > periods</Box>
</v-drag>

<v-drag pos="350,750,230,80">

<Box :hue1=1 :hue2=1 :hue3=1 > cumulative_returns</Box>
</v-drag>

<!-- 2 -->

<v-drag pos="180,510,120,80" v-click=[2,4]>

<Box > 缺失(极)值</Box>
</v-drag>


<v-drag pos="350,370,230,80" v-click=[2,3]>

<Box > max_loss</Box>
</v-drag>

<!-- 3 -->

<v-drag pos="350,480,230,80" v-click=[4,5]>

<Box > filter_zscore</Box>
</v-drag>

<!-- 4 -->
<v-drag pos="180,620,120,80" v-click=[5,8]>

<Box> 收益计算</Box>
</v-drag>

<v-drag pos="350,640,230,80" v-click=[5,6]>

<Box> periods</Box>
</v-drag>

<v-drag pos="350,750,230,80" v-click=[7,8]>

<Box> cumulative_returns</Box>
</v-drag>

<v-drag pos="180,730,120,80" v-click=[8,13]>

<Box> 中性化</Box>
</v-drag>

<v-drag pos="620,660,220,80" v-click=[8,9]>

<Box> groupby</Box>
</v-drag>

<v-drag pos="620,750,220,80" v-click=[11,12]>

<Box> groupby_labels</Box>
</v-drag>

<v-drag pos="620,840,220,80" v-click=[12,13]>

<Box> binning_by_group</Box>
</v-drag>
</div>

<!-- right -->

<div class="abs w-40% h-full left-50% top-30%" v-click=[3,4]>

Dropped <span v-mark.highlight.red="3">23.8%</span> entries from factor data: <span v-mark.highlight.red="3">22.4%</span> in forward returns computation 
and <span v-mark.highlight.red="3">1.3%</span> in binning phase (set max_loss=0 to see potentially suppressed 
Exceptions).<br><br>
max_loss is <span v-mark.highlight.red="3">35.0%</span>, not exceeded: OK!

</div>

<div class="abs w-50% h-full left-50% top-30%" v-click=[6,7]>

![](https://images.jieyu.ai/images/2024/07/alphalens-merged-data.jpg)
</div>

<div class="abs w-50% h-full left-50% top-30%" v-click=[9,11]>

![](https://images.jieyu.ai/images/2024/07/factors-datastructure-with-sector.jpg)
</div>

<div class="abs w-40% h-full left-50% top-55%" v-click=[10,11]>

## GICS - 明晟
## ICB - 富时罗素
## 申万
</div>

<div class="abs w-40% h-full left-50% top-25%" v-click=[13,14]>

![](https://images.jieyu.ai/images/2024/07/gcfafr-in-factor-analysis.png)
</div>

<!--

前面的课程中，我们介绍了 get_clean_factor_and_forward_returns的 factor, prices, quantiles和bins参数。这一期视频，我们一次性讲完所有其它的参数。

[click]

首先是缺值值处理相关的两个参数， max_loss和filter_zscore。

[click]
因子数据不可避免的会产生缺失值。而且，在因子预处理过程中，也可能产生缺失值。比如，当我们将原始价格数据，通过pivot转换成Alphalens需要的价格数据DataFrame时，个别股票缺失某一天的交易数据，就会导致整行记录被污染，从而这一行数据也变得不可用。

因此，在Alphalens对数据进行预处理后，会产生多少条缺失数据，这是事前人工难以估计的。但是，损失越多，因子分析的质量就越难保证。所以，我们通过max_loss这个参数来告诉Alphalens，如果损失大于max_loss，预处理就失败了，就不要进行进一步分析了。

[click]

那无论在预处理中，max_loss有没有突破，Alphalens总会输出这样一段日志，以告诉我们，在各个阶段，抛弃了多少条数据，最后的结果是否适合进一步分析。

这条日志就告诉我们，这次预处理中，抛弃了23.8%的记录，其中有1.3%是在分层时丢掉的。但总的来说，没有超过35%，可以继续进行分析。

[click]

filter_zscore, 这是去极值的一个参数。默认是当前值超过标准差的20倍时，就会被drop掉。这个值看上去很大，但在处理财务数据时，很多数据往往会与标准差有这么大的偏差。

[click]

peroids 这个参数比较简单。它用来指定Alphalens将计算多长周期的收益。默认是1，5和10。Alphalens默认的周期是天，但它也确实支持日内和更长的周期。

[click]

表格中的1D， 5D和10D正是periods参数给出的。多说一句，我们在前面的视频中，做月线低波动因子，尽管我们算的是1月、5月、10月的收益，但显示的仍然是1D、5D和10D，而不是1M,5M和10M。Alphalens就没有Month这个单位，它推荐使用22D来代替1月，不过这种做法本身也值得商榷。因为这会造成收益并不是真正意义上的月收益。大家知道，由于基金、期权结算等等原因，月末在交易上是有特殊影响的，我们计算月收益，一定要按每月收盘日来计算。

Alphalens为了能在全球市场上通用，因此它是没有自己的日历的，日历数据是从我们给的因子数据推导出来的。所以，这点上大家也要多留意，有可能导致个别场合下因子分析出错。不知道市面上有没有完全准确无误的因子分析框架，如果有的话，欢迎分享。

[click]

这个参数更加微妙。如果你正在做的是日频或者日频以上的粒度的因子分析，可以不用考虑这个参数。使用默认值就好。关于这个参数及相关功能，Alphalens的开发人员自己也争论了很久。可以认为，它主要影响日内频度的因子分析，以及在和pyfolio联合使用的场合。

感兴趣的同学可以自己扒一下github上alphalens的仓库中，第374号issue，看看他们在争论什么。

[click]

为了实现中性化，我们需要给Alphalens提供行业分类数据，即每一个ticker，它属于哪一个sector/industry。一般我们会在factor表中增加一列。

[click]

在右边的表格中，存在一列，名为sector,它的值就是对应各个ticker。比如APPL，这是苹果公司，它的行业分类就是311。

[click]

行业分类有若干个国际标准。最常用的有GICS，这是明晟开发的；ICB，这个是富时罗素开发的。在国内，我们有证监会分类体系，但一般可能用得更多的是申万一级分类等等。

这些数据，都应该通过你的数据源来获取。

[click]

groupby_labels参数比较简单。一般来说，通过groupby传递给Alphalens的数据，都是整数标签，这会方便计算，但不利于生成供人类阅读的报表。在生成报告时，我们需要把这些数字标签转换成对应的行业分类名称。这就是groupby_labels参数的作用。

[click]
bining_by_group参数指示Alphalens是否在组内进行分层。如果groupby为None，这个参数就没有意义。当 binning_by_group 设为 True 时，因子值的分箱（如通过 quantiles 或 bins 参数定义）将在每个组内独立进行。这意味着，在不同的组内因子值的分布就可能有所不同，每个组内的因子值也会根据该组的分布被划分为相同的分位数或区间。这种方法通常用于控制不同组别（如行业、市值等）对因子分析结果的影响，确保组内中性。

[click]

好，这一期视频，我们就讲完了get_clean_factor_and_forward_returns这个函数。一个函数，讲了几期视频，原因是因为这个函数的功能很复杂，涵盖了因子分析框架中好几个功能。这部分完成后，后面主要就是如何分析和调优的过程了。

我是QuantTide 量化风云。今天的视频就到这里。本视频摘录自《因子分析与机器学习策略》。在视频之外，课程内容还有大量文稿、参考资料和可运行代码，帮助你从入门到精通，掌握因子分析和构建基于机器学习的策略。欢迎持续关注。

-->
