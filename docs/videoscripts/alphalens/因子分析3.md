---
title: 为Alphalens准备数据
seq: FA01-003 因子分析与机器学习策略
slug: factor-analysis-3
date: 2024-06-11
img: https://images.jieyu.ai/images/2024/01/alphalens.jpg
category:
  - 因子分析
tags:
  - 因子分析
  - Alphalens
lineNumbers: true
---

<!--
欢迎来到量化风云频道。做量化人的视听杂志，每天一分钟，轻松学量化
-->

---
clicks: 20
---

<style>
.abs {
    position: absolute;
}

ul {
    list-style-type: none;
    font-size: 4vw;
}

.step1-header {
    position: absolute;
    top: 15%;
    left: 780px;
}

</style>

<div class="abs step1-header" v-motion
    :click-4="{opacity: 1}"
    :enter="{opacity: 0}">
    因子检验步骤
</div>
<div class="abs step1" v-motion
    :click-4="{scale:0.2, x: 450, y:50}"
    :enter="{scale:1,x:50,y:150}">
<v-clicks>

- :one: 生成因子和价格数据
- :two: get_clean_factor_and_forward_returns
- :three: create_full_tear_sheet

</v-clicks>
</div>

<!--click 4-->

<div class="abs" v-motion
    :click-9="{scale:0}"
    :enter="{scale:1,x:50,y:150,opacity:1}">

<v-clicks>

- :one: 收益率的标准差
- :two: 中长期业绩稳健股

</v-clicks>
</div>

<!--click 6-->

<div class="abs" v-motion style="width: 300px;box-shadow: 0 2px 4px rgba(0,0,0,0.5);"
    :click-6="{scale:1, y:380}"
    :click-7="{x:10}"
    :click-9="{scale:0}"
    :enter="{scale: 0, x:390, y: 500}">
    <img src="https://images.jieyu.ai/images/2024/06/low-vol-fjgs.jpg"/>
</div>

<div class="abs" v-motion style="width: 300px;box-shadow: 0 2px 4px rgba(0,0,0,0.5);"
    :click-7="{scale:1, y: 380}"
    :click-8="{x:670}"
    :click-9="{scale:0}"
    :enter="{scale:0, x:390, y:500}">
    <img src="https://images.jieyu.ai/images/2024/06/low-vol-dzjt.jpg">
</div>

<div class="abs" v-motion style="width: 300px;box-shadow: 0 2px 4px rgba(0,0,0,0.5);"
    :click-8="{scale:1, y:380, x: 340}"
    :click-9="{scale:0}"
    :enter="{scale:0,x:390,y:500}">
    <img src="https://images.jieyu.ai/images/2024/06/high-vol-zhdc.jpg"/>
</div>

<!--click9-->

<div class="abs" v-motion
    :click-9="{scale:1}"
    :click-11="{scale:0}"
    :enter="{x:50, y:150,scale:0}">

```python
from coursea import *
import numpy as np
import matplotlib.pyplot as plt

await init()
sec = "600282.XSHG"

bars = await Stock.get_bars(sec, 61, FrameType.MONTH,end=datetime.date(2024,5,31))
close = bars["close"]
returns = close[1:]/close[:-1]-1

vol = np.std(returns)
plt.plot(close)
vol
```
</div>

<div class="abs" v-motion
    :click-9="{scale:1}"
    :click-10="{scale:0}"
    :enter="{x:750, y:250, width:200,scale:0}">
    <img src="https://images.jieyu.ai/images/2024/06/600282-close-plot.jpg">
</div>

<div class="abs" v-motion
    :click-10="{scale:1}"
    :click-12="{scale:0}"
    :enter="{x:750, y:250, width:200, scale:0}">
    <img src="https://images.jieyu.ai/images/2023/07/factor_df_format.png">
</div>

<!--click 11-->
<div class="abs" v-motion
    :click-11="{scale:1}"
    :click-14="{scale:0}"
    :enter="{x:50, y:150,scale:0}">

```python
from bottleneck import move_std

universe = ["000001.XSHE", "600282.XSHG"]

returns = []

for sec in universe:
    bars = await Stock.get_bars(sec, 62, FrameType.MONTH,end=datetime.date(2024,5,31))
    close = bars["close"]
    returns.append(close[1:]/close[:-1]-1)

returns = np.asarray(returns)

# output (2, 61)
print(returns.shape)

# 高性能计算关键
move_std(returns, 60, axis=1)
```
</div>

<!-- click 12 -->

<div class="abs" v-motion
    :click-12="{scale:1}"
    :click-13="{scale:0}"
    :enter="{x:750, y:250, width:200,scale:0}">

$$
\begin{bmatrix} 0.02 & -0.01 & .. & 0.03 \\ 0.05 & 0.01 & .. & 0.03 \\ 0.01 & 0.02 &.. & 0.04 \end{bmatrix}
$$

**np.std(returns, axis=1)**

$$
\begin{bmatrix}
v0 \\ v1 \\ .. \\vn
\end{bmatrix}
$$
</div>

<div class="abs" v-motion style="color:red;font-size:2.5vw"
    :click-13="{scale:1}"
    :click-14="{scale:0}"
    :enter="{x:750, y:250, scale:0}">

0.84s => 4.5us
</div>

<!--click 14-->

<div class="abs" v-motion style="color:red;font-size:2.5vw"
    :click-14="{scale:1}"
    :click-15="{scale:0}"
    :enter="{x:50, y:250, scale:0}">

<img src="https://images.jieyu.ai/images/2023/07/prices_df_format.png">
</div>

<!--click 15-->
<div class="abs" v-motion
    :click-15="{scale:1}"
    :click-17="{opacity: 0}"
    :enter="{x:50, y:150, scale:0}">

```python{all|2-7|9}{at:15}
prices = []
for code in codes:
    bars = get_bars_in_range(code, ..., start_, end_)
    
    df = pd.DataFrame(bars[["frame", "close"]], columns=["frame","close"])
    df["code"] = [code] * len(bars)
    prices.append(df)

prices = pd.concat(prices).pivot(index='frame', columns='code', values='close')
prices.index = pd.to_datetime(prices.index, utc=True)
prices = prices.rename_axis('date')
```
</div>

<Promotion :at=17 :dur=15 />

<!--
上一期视频我们介绍了通过Alphalens来分析因子绩效，主要是三个步骤

[click]


flexed bicep
Unicode: U+1F4AA, UTF-8: F0 9F 92 AA
1. 按Alphalens要求的格式生成因子和价格数据

[click]

2. 调用 get_clean_factor_and_forward_returns 进行数据清洗
[click]
3. 调用create_full_tear_sheet 生成绩效报告

我们今天讲第一个步骤，如何生成因子和价格数据。这里的关键，我们最终得到的因子数据，要转换成为符合Alphalens格式要求的DataFrame.

在这里我们举一个低波动因子的例子。

[click]

低波动因子即股票收益率的标准差。

[click]

低波动因子能选出业绩较好的个股.

一些业绩比较好，护城河比较深的个股，多是以机构持仓为主，因此波动不会太大，但因为有业绩支撑，所以也能持续上涨。

很显然，如果收益标准差比较小，那么股票的价格波动就不大，股票的收益率就比较稳定。比如，如果一支股票连续10月，每月匀速上涨2%，那么这10个月的波动率就为零。

[click]

显然我们无法找到波动率为零的例子。这里我们找了一个波动底很低，但处于上涨中的例子。这个例子是到5月份为止，波动率最低的前10中的一支。实际上前十支中，大多数都处在上涨中，这也跟今年的风格有关。

[click]
当然，10月波动率为零的个股，或者比较小的个股，并不一定是在上涨，也可能是横盘或者下跌中。

这是波动率最小的20支个股中的一支，它就正处在一个稳定的下降中。

但是，我们完全可以通过均线走势来过滤掉这种股。这在量化24课中有介绍。

[click]

个股大涨往往是见顶的指标。低波动因子会不会选到这种个股？我们来看看中间这个例子。在大涨之前，它的波动率较低，大涨之后，波动率急剧上升，是5月波动率最大的前10支之一。显然，低波动因子不会选到上涨之后的个股。


通过低波动因子选股，与通过业绩选股有重叠的部分。大家可以思考一下，哪种方式更好。在A股，一些公司的财务报表不是太可信的情况下，可能低波动因子更能过滤掉业绩虚增的情况。

[click]

言归正转，低波动因子究竟应该如何计算呢？

如果仅仅是针对个股，不考虑滑动窗口，那么波动率的计算非常容易。这段代码就演示了如何计算个股的波动率。

但是，Alphales要求的，是这样一个DataFrame：

[click]

它是一个双重索引的DataFrame，第一层索引是日期，第二层索引是股票代码。score列则是因子值。

如何得到这样一个表格呢？我们当然可以遍历因子检验区间[start, end]，对每一个日期，取universe中的每支股票，获取行情数据，再计算股票的波动率。但这样的代码是没有灵魂的。

我司曾经给研究员配置的电脑是2万多，有GPU，但内存只有32G。以这样的配置，一些研究员做因子分析非常痛苦，需要等待很长的时间。内存虽然还有扩大的空间，但算法不够优化，可能是最主要的问题。

接下来，我们来看看如何既简洁、又快速地完成这个计算。

[click]
这段代码的速度非常快。关键是使用了bottleneck中的move_std函数，它完全消除了在Python域可能的循环。

numpy的std函数可以同时计算多行的std。如果多支股票的收益率按行排成右图的矩阵，那么我们就可以通过np.std(returns, axis=1)一次性求得所有个股的波动率。

[click]

但是，众所周知，numpy并没有提供滑动窗口函数，因此，我们要计算连续周期下的波动率，至少还得使用一层循环。bottleneck正好补齐了这个缺。

[click]

在我们的测试中，这个示例耗时为4.5us。完成同样的任务，使用np.std加循环计算，耗时为0.84秒。bottleneck提速了近200倍。

[click]

最后我们来看Alphalens对价格数据的要求。

它是以日期为索引，股票代码为列名，价格为单元格值的DataFrame。

在构建这个dataframe时，建议这样获取数据。

通常，我们通过数据源获得的数据都是按支获取的、一定时间范围内的行情数据。这个数据往往不带code信息，我们需要像第6行一样，先补齐code信息，然后像第9行一样，通过pivot方法，将它转换成Alphalens需要的格式

[click]
今天的视频就到这里。谢谢观看。


-->
