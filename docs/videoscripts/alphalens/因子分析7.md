---
title: 你没看错，7年2500倍
seq: FA01-007 因子分析与机器学习策略
slug: factor-analysis-7
date: 2024-07-26
category:
  - 因子分析
tags:
  - 因子分析
  - Alphalens
lineNumbers: true
---

<!-- 分层不对，努力白费？！ -->

<!--
7年2500倍！没算错，但只有少数人能赚到！
大家好，这里是QuanTide，量化风云
-->

---
clicks: 25
---

<!--
上一次课我们讲到，Alphalens提供了两种分层机制，按分位数(quantiles)，或者按数值(bins)，并且提了一个问题，为什么Alphalens要提供两种分层机制？难道有了按分位数的分层机制还不够吗？
-->

<div v-click=[0,2]>

<v-drag pos="121,288,100,100">

<Ellipse class="abs w-full h-full" >分层</Ellipse>
</v-drag>

<v-drag pos="402,118,128,125">

<Ellipse class="abs w-full h-full" >by quantiles</Ellipse>
</v-drag>

<v-drag pos="413,385,123,122">

<Ellipse class="abs w-full h-full" >by bins</Ellipse>
</v-drag>

<v-drag-arrow pos="216,358,200,81"/>
<v-drag-arrow pos="211,310,194,-131" />

</div>


<!--

1. 今天我们就来看一个实际的例子，通过分析，你就会明白为何我们需要两种分层机制。

-->

<Meme name="doyouhavequestions" class="abs left-60% top-30% w-200px" :at=1 :dur=5000 />

<!-- 2 -->


<div v-click=[2,3]>

<RightRevealImg src="https://images.jieyu.ai/images/2024/07/boost-returns.jpg" class="abs top-30%"/>
</div>

<!-- 3 -->

<div v-click=[3,4] class="abs w-full h-200px top-40% text-center text-6xl">

plot_quantile_statistics_table
</div>

<div v-click=[4,6] class="abs left-150px">

```python {14-16}

async def factor_and_prices(universe, n = 10, end):
    dfs = []
    start_ = tf.shift(end, -n, FrameType.DAY)

    for sec in universe:
        bars = await Stock.get_bars(sec, n, FrameType.DAY, end=end)
    
        df = pd.DataFrame(bars[["frame", "close", "open"]], columns=["frame", "close", "open"])
        df["asset"] = sec
    
        df = df.rename(columns={"frame": "date"})
        
        # 计算个股的月收益率及36月波动率（滑动窗口）
        rsi = ta.RSI(df.close.astype(np.float64), 6)
        rsi[:18] = np.nan
        df["rsi"] = 100 - rsi

        df["open"] = df.open.shift(-1)
        df = df.set_index('date')
        
        dfs.append(df)

    df = pd.concat(dfs)

    return df.dropna(how='any').sort_index()

raw = await factor_and_prices(universe, 2000)
raw.head()
```

<v-drag pos="87,227,362,93">

<Box />
</v-drag>
</div>

<div v-click=[5,6] class="abs w-full h-200px top-50% text-center text-6xl">

$$
factor = 100 - RSI
$$
</div>

<!-- 6-->

<div v-click=[6,9] class="abs top-40% left-150px">

```python {4|6}

factor = raw[['asset', 'rsi']].set_index('asset', append=True)
prices = raw.pivot(columns="asset", values="open")

cleaned = get_clean_factor_and_forward_returns(factor, prices)

plot_quantile_statistics_table(cleaned)
```

<v-drag pos="-4,51,515,55" v-click=[6,7]>

<Box />
</v-drag>

<v-drag pos="1,86,515,55" v-click=[7,9]>

<Box />
</v-drag>
</div>

<!-- 8 -->

<div v-click=[8,9] class="abs top-70% left-50px text-4xl">

如果分层不正确，那么后面的结果就不用看了。
</div>

<!-- 9 -->

<div v-click=[9,10] class="abs top-30% left-150px w-75%">

![](https://images.jieyu.ai/images/2024/07/cut-by-quantile.jpg)

<v-drag pos="97,60,260,57">

<Box />
</v-drag>

</div>

<!--10-->

<div v-click=[10,11] class="abs top-20% left-150px w-70%">

![](https://images.jieyu.ai/images/2024/07/cum-returns-by-quantile.jpg)
</div>

<!--11-->

<div v-click=[11,14] class="abs top-30% left-50px text-4xl">

```python {4-5}
factor = raw[['asset', 'rsi']].set_index('asset', append=True)
prices = raw.pivot(columns="asset", values="open")

bins = [0, 10, 20, 30, 70, 80, 90, 100]
cleaned = get_clean_factor_and_forward_returns(factor, prices,quantiles=None, bins = bins)

create_full_tear_sheet(cleaned)
```

<v-drag pos="5,37,705,88" v-click=[11,12]>

<Box />
</v-drag>
</div>

<!--12-->

<div class="abs top-30% left-50px text-4xl">

<v-drag pos="485,65,121,60" v-click=[12,13]>

<Box />
</v-drag>
</div>

<!--13-->
<div class="abs top-30% left-50px text-4xl">

<v-drag pos="6,51,358,45" v-click=[13,14]>

<Box />
</v-drag>
</div>

<div v-click=[14,15] class="abs top-30% left-50px w-75%">

 

<v-drag pos="92,61,254,45" v-click=[14,15]>

<Box />
</v-drag>

<v-drag pos="96,165,254,45" v-click=[14,15]>

<Box />
</v-drag>

<v-drag pos="96,273,254,45" v-click=[14,15]>

<Box />
</v-drag>
</div>

<!--15-->

<div v-click=[15,16] class="abs top-30% left-50px w-75%">

![](https://images.jieyu.ai/images/2024/07/cum-returns-by-bins.jpg)
</div>

<!--16-->

<div v-click=[16,18] class="abs top-10% left-10% w-75% h-full">

<div class="abs h-50% w-80%">

![](https://images.jieyu.ai/images/2024/07/nprw-by-quantiles.jpg)

<p class="abs color-red text-3xl left-100% top-20% w-50%">by quantiles</p>
</div>

<div class="abs h-50% top-45% w-80%">

 
<p class="abs color-red text-3xl left-100% top-10% w-50%">by bins</p>

</div>

<p class="abs top-40% color-red text-4xl left-15% bg-black" v-click=[17,18]>做空第1组，做多第4组！</p>
</div>

<div v-click=[18,19] class="abs top-30% left-50px w-80%">

```python {1,5}
rsi_50 = raw[raw.rsi < 50]
factor = rsi_50[['asset', 'rsi']].set_index('asset', append=True)
prices = rsi_50.pivot(columns="asset", values="open")

bins = [0, 10,20, 30, 35, 40, 45, 50]
cleaned_rsi_50 = get_clean_factor_and_forward_returns(factor, prices, quantiles=None, bins = bins)

create_full_tear_sheet(cleaned_rsi_50)
```

<v-drag pos="0,-3,254,45" v-click=[18,19]>

<Box />
</v-drag>

<v-drag pos="1,69,332,45" v-click=[18,19]>

<Box />
</v-drag>
</div>

<div v-click=[19,20] class="abs top-30% left-50px w-80%">

  
</div>

<div v-click=[20,21] class="abs top-30% left-50px w-80%">

![](https://images.jieyu.ai/images/2024/07/boost-returns.jpg)
</div>

<div v-click=[21,25]
     class="abs left-50px w-80% top-30%"
    :enter="{y: 150}"
    :click-21="{y: 1000}">

<div class="text-6xl mb-4">Conclusion</div>

<div class="text-3xl mb-3" v-motion 
    :click-22="{color: 'black'}" 
    :click-21="{color:'red'}">正确选择分层方法</div>
<div class="text-3xl mb-3" v-motion 
    :click-21="{color:'black'}"
    :click-22="{color: 'red'}" 
    :click-23="{color:'black'}">plot_quantile_statistics_table</div>
<div class="text-3xl mb-3" v-motion   
    :click-22="{color:'black'}"
    :click-23="{color: 'red'}" 
    :click-24="{color:'black'}">根据MPWR重构因子</div>
<div class="text-3xl mb-3" v-motion
    :click-23="{color:'black'}"
    :click-24="{color: 'red'}" 
    :click-25="{color:'black'}">极少数人能兑现2500倍收益</div>
</div>

<div v-click=[21,22]>

<v-drag pos="473,302,100,100">

<Ellipse class="abs w-full h-full" >分层</Ellipse>
</v-drag>

<v-drag pos="699,113,128,125">

<Ellipse class="abs w-full h-full" >by quantiles</Ellipse>
</v-drag>

<v-drag pos="704,385,123,122">

<Ellipse class="abs w-full h-full" >by bins</Ellipse>
</v-drag>

<v-drag-arrow pos="568,372,145,59"/>
<v-drag-arrow pos="548,312,164,-111" />

</div>

<div v-click=[22,23] class="abs top-35% left-50% w-50%">

![](https://images.jieyu.ai/images/2024/07/cut-by-bins.jpg)
</div>

<div v-click=[23,25] class="abs top-25% left-50% w-50% h-full">

<div class="abs h-50% w-80%">

![](https://images.jieyu.ai/images/2024/07/nprw-by-quantiles.jpg)

</div>

<div class="abs h-50% top-35% w-80%">

![](https://images.jieyu.ai/images/2024/07/nprw-by-bins.jpg)

</div>

</div>

<div v-click=[25,26]>

<div class="abs w-full top-40% ml-0 pl-0 flex justify-center">

<QtBrand style="width: 120px" />
<div class="abs text-3xl">The End</div>
</div>
</div>

<!--
上一次课我们讲到，Alphalens提供了两种分层机制，按分位数(quantiles)，或者按数值(bins)，并且提了一个问题，为什么Alphalens要提供两种分层机制？难道有了按分位数的分层机制还不够吗？

[click]

今天我们就来看一个实际的例子，通过分析，你就会明白为何我们需要两种分层机制。为什么弄懂分层机制如此重要？

[click]

我今天还会用一个例子，让你认识到，我们是如何将一个策略从7年收益1.6倍，优化到7年2500倍收益的，原因就是因为做对了分层。

[click]

这一次课，我们还会学习一个新的API，分层效果好不好，就看它的输出！


跟往常一样，我们非常注重实战，所以会给很多经验之谈，相信对你工作和面试都非常有帮助。好，进入正题

[click]

我们先随机取400个ticker为一个universe，用RSI公式求出因子。这里注意两点，第一，第15行，我们使用的6期的RSI，它的前18个数据是不太准确的，所以我们将其置为nan，这样就不会带入因子分析。第二，RSI取值从0到100，一般来说，超过70要卖，低于30要买，因此，在分析之前，我们的假设是，RSI与收益成反比。

所以，我们构建的因子要用这个公式:

[click]

factor = 100 - rsi

这样才能满足因子的单调递增假设

[click]

我们先用Alphalens的默认参数来分层。这里我们没有传入quantiles或者bins参数，意味着将使用默认的quantiles=5

[click]

这个函数是我们今天要介绍的一个新的API。它的作用是显示alphalens内部进行分层的结果的一个报表。当我们通过Alphalens创建全部撕页时，最先出现的报告就是通过它生成的。这也说明了在Alphalens框架中，分层的重要性。

[click]

如果分层不正确，那么后面的结果就不用看了。

[click]


看出问题没有？第一个分层，它的RSI跨度从0到了78.9。从经典的技术分析来说，这一分层同时把超买和超卖的信号都包括了，显然，这个分法简直荒谬。

所以，如果你不知道在这一步就已经错了的话，那么因子分析是不可能得到正确结果的。

不过，我们还是看一下，在这种分层情况下，因子分析的最终结果

[click]


7年的多空组合的累计收益是1.6倍左右。这么说来，如果你能在A股做空的话，其实也没那么差。

[click]

无论最终表现怎么样，但我们已经知道，这个做法是错误的。

既然我们已经知道了RSI在不同的数值下，大概表现会如何（假设），所以，我们应该按数值来进行分层，这就要用到bins参数。

[click]


在使用bins参数时，注意一定要同时传入quantiles = None，否则会报错。

[click]

这里我们用的bins是[0, 10, 20, 30, 70, 80,90,100]，是基于一些经验观察，然后进行了假设，即，我们认为，关注50左右的RSI是没有意义的。

[click]

现在，我们看看分层的情况。这个分层就非常合理了。因为我们期望的就是，如果在RSI少于10，20，30的时候做多，在RSI大于70，80，90的时候做空，收益会如何。这个分层能够给到我们想要的结果。

[click]

我们看看按数值分层后，多空组合的结果。现在7年的收益是...还是1.6

[click]

不过，我们查看 mpwr图的话，会发现它的线性规律更强了。上面的是按quantile分层的分层收益均值图，下面是按bins的分层收益图。很明显，下面的图以中心为对称，出现明显的线性规律。


这样我们也找到了原因。这个原因是，因子最大的组和最小的组，它对收益的贡献是接近的，这样就不存在多空套利的可能了。但是这两组都与第4组构成较好的价差(spread)。

[click]

如果我们能让alphalens对第1组做空，对第4组做多，情况会如何？

[click]

但是Alphalens并不执行这样的操作。我们要重构因子。方法就是，我们把因子大于50的记录全部删除掉。bins也进行微调。


[click]

我们再来做因子分析。这次发现，因子分层出现了比较明显的单调递增规律。这正是我们在进行因子分析时要寻找的圣杯！

[click]

再来看多空累计收益，7年时间达到了惊人的2500倍！能够超过这个收益的，大约也只有涨停板指数了。

[click]

结论

要根据因子的特性，正确选择分层方法。这里再举一个例子，假如我们做的是新闻情感类因子，这些因子往往是离散数据，此时一般都只能使用自定义的bins来进行分层。
   
[click]
alphalens自动为我们实现了分层。因此，检查分层是否合理，需要通过它的API - plot_quantile_statistics_table来观察。

[click]
分层正确实现之后，我们通过分层收益均值图（MPWR）来观察分层是否具有线性。看看能否通过重构，使得因子分层收益满足线性要求。

[click]
最后，讨论一下这个2500倍的收益

一般来说，当我们看到这么好的收益时，就应该认为是实施步骤出现了问题。
在我们的实验中，是否存在实施问题呢？因子分析比较复杂，这就是为什么我们要尽可能地使用框架的原因。用对了框架，再离奇的结论也要接受。

那么，这个收益我们能否兑现？只有极少数人能够兑现这个收益。因为它的大部分收益是通过做空第一组来实现的。在我们的算法中，第一组实际上就是RSI大于90的那一组。

这一组的股票，往往是涨停股了，平常融券不易，此时融券就更难。但是，从今年以来披露的一些违规案例来看，有些机构先拉涨停板，再融券做空，这是充分利用了我们这里因子分析的结果：即RSI>90时，做空胜算很大。

所以，只有极少数人能兑现这么高的收益。

做量化就是造印钞机，所以，没那么容易发现交易的圣杯，即使发现了，也不一定能实现这个策略。不过，掌握了我们今天介绍的方法，就一定能在寻找因子的过程中，快人一步。

[click]

今天的内容就在这里。我是QuanTide，量化风云。
-->
