---
title: quantiles和bins参数
seq: FA01-005 因子分析与机器学习策略
slug: factor-analysis-5
date: 2024-07-22
category:
  - 因子分析
tags:
  - 因子分析
  - Alphalens
lineNumbers: true
---


<!--

因子检验有三种方法，回归、IC和分层回溯。在Alphalens中，分层回溯是如何实现的呢？

大家好，这里是QuanTide，量化风云。今天我们继续讲解Alphalens。

-->

---
clicks: 13
---

<style scoped>
.toc {
    font-size: 1.5vw;
    line-height: 2rem;
}


</style>

<RadioWave class="abs w-300px h-300px left-0 top-400px" color="white" />

<v-drag pos="491,80,488,287" v-motion
        :click-1="{scale: 0}"
        :enter="{scale: 1}">
    <Box/>
</v-drag>

<div class="abs left-50% top-15% w-50% h-full" v-motion
    :click-4="{x: 2000}"
    :click-11="{x:0}"
    :enter="{x:0}"
>

```python
get_clean_factor_and_forward_returns(
    factor,
    prices,
    groupby=None,
    binning_by_group=False,
    quantiles=5,
    bins=None,
    periods=(1, 5, 10),
    filter_zscore=20,
    groupby_labels=None,
    max_loss=0.35,
    zero_aware=False,
    cumulative_returns=True,
)
```

<p class="text-2xl text-center mt-4"> get_clean_factor_and_forward_returns </p>
</div>

<!-- 1 -->

<CapsuleList class="abs w-30% left-10% top-20% h-50%"
    :click-1="{x: 0}"
    :click-2="{y: 600}"
    :click-11="{y:0}"
    :enter="{x: -600, y:0}">

```yaml
    - quantiles
    - bins
    - binning_by_group
    - groupby
    - zero_aware
```
</CapsuleList>

<!-- 2 -->

<v-drag pos="549,178,169,53" v-motion
    :click-2="{scale:1}"
    :click-4="{scale:0}"
    :enter="{scale:0}">
    <Box/>
</v-drag>


<CapsuleList class="abs w-30% left-10% top-15% h-30% text-2xl"
    :click-2="{y: 100, opacity:1}"
    :click-3="{opacity:0}"
    :enter="{x: 0, y:0, opacity:0}">

```yaml
    - quantiles
    - bins
```
</CapsuleList>

<!-- 3 -->

<div v-click="[3,11]">
<v-drag pos="30,184,100,100">
<Ellipse class="abs w-full h-full" >分层</Ellipse>
</v-drag>

<v-drag pos="155,132,100,100">
<Ellipse class="abs w-full h-full" >by quantiles</Ellipse>
</v-drag>

<v-drag pos="161,278,100,100">
<Ellipse class="abs w-full h-full" >by bins</Ellipse>
</v-drag>


<v-drag pos="313,130,100,100">
<Ellipse class="abs w-full h-full" >int</Ellipse>
</v-drag>

<v-drag pos="319,279,100,100">
<Ellipse class="abs w-full h-full" >array</Ellipse>
</v-drag>

</div>

<div v-click="[4,8]">
<v-drag-arrow pos="102,225,60,-37" />
<v-drag-arrow pos="254,183,59,1"/>

<Numbers :data=[-0.68,0.4,-0.38,-1.09,0.71,-0.34,0.05,0.72,2.53,-0.48,0.53,0.89]
       label="factors" 
        class="abs top-10% left-40% scale-70%"/>


<Numbers :data=[-0.68,-0.38,-1.09,-0.48]
       label="33%分位" 
        class="abs top-30% left-45% scale-70%"/>

<Numbers :data=[0.4,-0.34,0.05,0.53]
       label="66%分位" 
        class="abs top-30% left-70% scale-70%"/>

<Numbers :data=[0.71,0.72,2.53,0.89]
       label="100%分位" 
        class="abs top-50% left-60% scale-70%"/>


<Anime class="abs top-75% left-60% w-50% color-red" action="flash" dur="10s"> quantiles = 3 </Anime>
</div>

<div v-click="[5,6]">
<v-drag-arrow pos="502,143,-6,74" color="blue" width="1" />
<v-drag-arrow pos="576,143,-44,73" color="blue" width="1" />
<v-drag-arrow pos="611,138,-40,83" color="blue" width="1" />
<v-drag-arrow pos="826,140,-223,78" color="blue" width="1" />
</div>

<div v-click="[6,7]">
<v-drag-arrow pos="541,141,181,83" color="green" width="1" />
<v-drag-arrow pos="689,139,79,87" color="green" width="1" />
<v-drag-arrow pos="722,139,83,85" color="green" width="1" />
<v-drag-arrow pos="865,138,-21,85" color="green" width="1" />
</div>

<div v-click="[7,8]">
<v-drag-arrow pos="650,138,-18,191" color="red" width="1" />
<v-drag-arrow pos="797,139,-90,188" color="red" width="1" />
<v-drag-arrow pos="900,138,-157,191" color="red" width="1" />
<v-drag-arrow pos="759,145,-90,186" color="red" width="1" />
</div>

<!-- 8 -->

<div v-click="[8,9]">
<v-drag-arrow pos="102,225,60,-37" />
<v-drag-arrow pos="249,185,94,106"/>

<Numbers :data=[-0.68,0.4,-0.38,-1.09,0.71,-0.34,0.05,0.72,2.53,-0.48,0.53,0.89]
       label="factors" 
        class="abs top-10% left-40% scale-70%"/>

<Numbers :data=[-0.68,-1.09]
       label="10%分位" 
        class="abs top-30% left-60% scale-70%"/>

<Numbers :data=[0.4,-0.38,0.71,-0.34,0.05,0.72,-0.48,0.53]
       label="90%分位" 
        class="abs top-50% left-50% scale-70%"/>

<Numbers :data=[2.53,0.89]
       label="100%分位" 
        class="abs top-30% left-75% scale-70%"/>

<Anime class="abs top-75% left-60% w-50% color-red" action="flash" dur="10s"> quantiles = [0.1, 0.9] </Anime>
</div>

<div v-click="[9,11]">
<v-drag-arrow pos="126,240,51,56" />
<v-drag-arrow pos="258,325,66,1"/>
<v-drag-arrow pos="256,314,73,-102"/>

<Numbers :data=[-1.09,-0.68,-0.48,-0.38,-0.34,0.05,0.4,0.53,0.71,0.72,0.89,2.53]
       label="factors" 
        class="abs top-10% left-40% scale-70%"/>

<Numbers :data=[0.12,1.32,2.53]
       label="按绝对值等分 - bins" 
        labelPos="bottom"
        class="abs top-40% left-55% scale-70%"/>

<Numbers :data=[-0.35,0.58,2.53]
       label="按分位数等分 - quantiles" 
       labelPos="bottom"
        class="abs top-40% left-75% scale-70%"/>    

<v-drag-arrow  color="blue" pos="777,239,-139,-109"/>
<v-drag-arrow color="blue" pos="817,238,-39,-103"/>

<v-drag-arrow  color="red" pos="579,239,131,-114"/>
<v-drag-arrow pos="613,238,269,-100" color="red"/>

</div>

<div v-click="[10,11]">

<v-drag-arrow pos="610,269,1,50" color="green"/>
<p class="abs left-500px top-320px"> 2.53 - 1.32 = 1.32 - 0.12 = 1.2</p>

<p class="abs left-620px top-80px color-#1F8000">33%</p>
<p class="abs left-765px top-80px color-#1F8000">66%</p>

</div>

<div v-click="11" class="abs top-210px left-90px h-180px w-320px">
<Box />
</div>

<Promotion :at=12 :dur=10 />

<div v-click="13" class="abs bg-black w-full h-full flex items-center justify-center top-10%">
<video src="https://images.jieyu.ai/images/hot/the-end.mp4" preload autoplay loop></video>
</div>

<!--
在Alphalens中，分层回溯是通过调用这个函数，get_clean_factor_and_forward_returns 自动实现的

[click]

但是我们可以通过参数 quantiles, bins等几个参数来控制它。

[click]

quantiles和bins是互斥的参数，一次只能传入一个，两者都是用来帮助Alphalens控制分层的。那么它们有何区别呢？

[click]

这两个参数既可以是整数，也可以是一个数组。所以它的用法还是有一点复杂的。

[click]
我们先来看按quantiles参数进行分层的情况

quantiles参数，顾名思义，是要按分位数来进行分层。

当它的值为整数时，意思是按分位数进行n等分。在这里，我们指定quantiles为3

[click]
这样就将前33%的因子分到了第一组

[click]
33%到66%的因子分到了第二组

[click]
其它的因子分到了第三组

用这种方法进行分层，各层的样本数可以基本保持一致。

[click]

如果我们不想按等距的分位数进行分层，我们就可以指定具体的分位数，以数组的方式传入。

现在显示的就是按quantiles = [0.1, 0.9]的分层情况。会把因子中，最小的10%的分为一组，中间10%到90%的分到一组，最大的10%的分为另一组。

[click]

alphalens默认是按quantiles来分层。但它也允许我们按bins来划分。

那么，所谓的按bins划分又是什么意思呢？

我们通过右图来说明按分位数分层和按bins分层的区别。核心就在于，分界点的划定方法不同，导致位置不同。

[click]

比如，同样是按三等分划分的情况下，按bins三等分，意味着分界点之间的距离相等。在这里，按bins来划分，三个分界点之间的距离都是1.2

而如果按quantiles来等分的话，意味着分界点的分位数之间是等距的，但它们的绝对距离是不等的。只有当因子是是均匀分布时，分位数对应的分界点，它们之间的距离才会相等。

这就是两个划分在数学上的区别。

好，那么问题来了，Alphalens为什么要提供两种划分方式呢？你能举出来一个例子说明什么情况下，使用哪一种分层方式更好吗？

[click]

还有，我们在一开始就列出了这三个参数，它们各有什么用途呢？

好，这些问题，我们下期视频见！

-->
