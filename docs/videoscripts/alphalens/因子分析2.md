---
title: Alphalens框架分析
slug: alphalens-intro-1
abstract: Alphalens是使用人数最多的因子分析框架。我们将完整介绍框架实现及如何理解报告
date: 2024-06-09
img: https://images.jieyu.ai/images/2024/01/alphalens.jpg
category:
  - 职场
tags:
  - 职场
  - 资源
  - 公司
---

---
layout: prelude
transition: fade
---

<!--
欢迎来到量化风云频道！做量化人的视听杂志，每天1分钟，轻松学量化
-->
---
clicks: 10
---

## Alphalens框架解析

<style>
    * {
        font-size: 8vw;
        line-height: 1.5em;
    }
    .top {
        height : 40%;
        position: absolute;
        top: 10%;
        left: 0;
        width: 100%;
        color:red;
    }

    .bottom {
        height : 40%;
        position: absolute;
        top: 55%;
        left: 0%; 
        width: 100%;
        padding: 0 1vw;
    }

    h2 {
        margin-bottom: 100px;
    }
</style>

<div class="top" v-motion 
    :click-1="{width: '80%'}"
    :click-2="{opacity:0}">

![](https://images.jieyu.ai/images/2024/01/alphalens.jpg)
</div>

<div class="top" v-motion 
    :click-1="{x:110, scale:0.8, y: 80, opacity: 0.7}"
    :click-2="{opacity:0}"
    :enter="{scale:0}">

![](https://images.jieyu.ai/images/2024/06/quantopian.png)
</div>

<!--alphalens-->
<div class="top" v-motion 
    :click-2="{scale:1}"
    :click-3="{scale:3.5, x:-900,y:500}"
    :click-4="{scale:3.5, x:-850,y:-200}"
    :click-5="{x:1200, y:700}"
    :click-6="{x:900,y:-200}"
    :click-7="{scale:0}"
    :enter="{scale:0}">

![](https://images.jieyu.ai/images/2023/07/alphalens-framework.png)
</div>

<div class="top" v-motion
    :click-3="{scale:1}"
    :click-7="{scale:0}"
    :enter="{scale:0, x: 0, y: 600}">

<Audio :at=3 name="wechat-huwo" :delay=1000 />

<h2 style="color:red">Utils</h2>
</div>

<div class="top" v-motion 
    :click-4="{scale:1}"
    :click-7="{scale:0}"
    :enter="{scale:0, x: 150, y: 520}">

<Audio :at=4 name="wechat-huwo" />

<h2 style="color:red">Tears</h2>
</div>

<div class="top" v-motion 
    :click-5="{scale:1}"
    :click-7="{scale:0}"
    :enter="{scale:0, x: 300, y: 600}">

<Audio :at=5 name="wechat-huwo" />

<h2 style="color:red">Performance</h2>
</div>

<div class="top" v-motion 
    :click-6="{scale:1}"
    :click-7="{scale:0}"
    :enter="{scale:0, x: 600, y: 520}">

<Audio :at=6 name="wechat-huwo" />

<h2 style="color:red">Plotting</h2>
</div>

<!--click 7-->

<v-clicks at="+7">

### 提取原始因子和价格数据
### 调用get_clean_factor_and_forward_return
### 调用create_full_tear_sheet
</v-clicks>

<!--clicks 10-->

<div v-click="10" class="top" style="top: 500px; left: 4vw;">

### 原始因子的格式要求
### 价格数据的格式要求
### get_clean_factor_and_forward_returns
</div>

<!-- bottom -->
<div class="bottom">

```python {all|4,5|7,8|10}{at:7}
from al..utils import get_clean_forward_returns
from al..tears import create_full_tear_sheet

raw_factors = calc_factors()
prices = get_prices(univers)

factor = get_clean_factor_and_forward_returns(
    raw_factors, prices, bins=5, quantiles=None)

create_full_tear_sheet(factor, False)
```

</div>

<!--
今天我们介绍的知识点是Alphalens。Alphalens是因子分析最重要的库，



我们之前有文章专门介绍过它的开发者， Fawcett和Quantopian这家公司。

从今天开始，我们将详细介绍这个框架的使用。



这个图是Alphalens的架构图。它一共由四个模块构成：



utils utils是用户与Alphalens交互的模块，这个模块提供了一些工具函数，比如对因子分层，计算前向收益，对数据进行预处理等等。我们使用Alphalens的入口一般是get_clean_forward_returns，也在这个模块下。



Tears 这个模块是分析的入口，也是用户接口之一。我们常常使用的create_full_tear_sheet也出自这个模块，我们调用它来执行因子检验并生成报告。



Performance 这个模块不是对外接口。它是因子检验的执行器。我们在前面的课程中看到的因子IC检验、收益回归分析等，都由这个模块的相应函数来实现。它被tears模块中的方法调用。



Plotting Alphalens为我们承担了大量可视化工作，这部分工作是在plotting模块中实现的。它一般多由performance模块中的方法来调用。



通过Alphalens进行因子分析，一般是这样三步


先是提取原始因子和价格数据。原始因子是指未经过标准化、中性化的因子。通过Alphalens，我们不需要自己进行这些预处理，直接交给框架就好。当然，我们仍然必须理解预处理步骤。



然后我们调用get_clean_factor_and_forward_returns，进行因子标准化、去极值、去缺失值、中性化、因子分层，并且计算1日、5日和10日因子收益率，将这些收益率与因子日期对齐


最后，我们调用create_full_tear_sheet进行因子分析，这将输出为数众多的报表。

今天的内容就到这里。接下来我们将介绍：



一、原始因子应该满足什么样的格式要求
二、原始价格应该满足什么样的格式要求
三、介绍get_clean_factor_and_forward_returns函数

我们明天见！
-->
