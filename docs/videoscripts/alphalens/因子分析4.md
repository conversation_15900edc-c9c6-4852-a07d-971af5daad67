---
title: <PERSON><PERSON><PERSON><br>避坑指南
seq: FA01-004 因子分析与机器学习策略
slug: factor-analysis-4
date: 2024-06-14
img: https://images.jieyu.ai/images/2024/01/alphalens.jpg
category:
  - 因子分析
tags:
  - 因子分析
  - Alphalens
lineNumbers: true
---

<!--
欢迎来到量化风云频道。做量化人的视听杂志，不用开电脑，轻松学量化
-->

---
clicks: 20
---

<style scoped>
.toc {
    font-size: 1.5vw;
    line-height: 2rem;
}


</style>

<div class="abs" v-motion
    :click-13="{scale:0}">
    <img src="https://images.unsplash.com/photo-1573497491208-6b1acb260507?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"/>
</div>

<Meme :at=2 name="crow" left="0" top="0"/>

<div class="abs animate__animated animate__wobble" v-click="[4]" 
    style="font-size: 6vw;z-index:99;color:red;left:400px;top:50px"
    >
    Universe
</div>

<div class="abs" v-motion
    :enter="{scale:0, y:0}"
    :click-3="{scale:1}"
    :click-9="{y: -400}"
    :click-12="{y:0}"
    :click-13="{scale:0}"
>

```python {all|all|all|1-3|5-13|16-20|21-22|24-26|34,35|37|38|all}

# 定义universe
secs = await Security.select().eval()
universe = secs[:50]

# 定义因子测试的时间范围
start = datetime.date(2009, 1, 1)
end = datetime.date(2023, 1, 1)

dfs = []

# 根据因子生成的参数（36月波动率），我们需要调整获取行情的起始时间
start_ = tf.shift(start, -36, FrameType.MONTH)
end_ = tf.shift(end, 10, FrameType.MONTH)

for sec in universe:
    bars = await Stock.get_bars_in_range(sec, FrameType.MONTH, start_, end_)

    df = pd.DataFrame(bars[["frame", "close"]], columns=["frame", "close"])
    df["asset"] = sec

    # 我们的数据源使用"frame"来表示日期，按Alphalens惯例重命名为"date"
    df = df.rename(columns={"frame": "date"})
    
    # 计算个股的月收益率及36月波动率（滑动窗口）
    returns = df.close.pct_change()
    df["vol_36"] = returns.rolling(window=36).std()

    df = df.set_index('date')
    
    dfs.append(df)

df = pd.concat(dfs)

factor = df[['asset', 'vol_36']].set_index('asset', append=True)
prices = df.pivot(columns="asset", values='close')

merged = get_clean_factor_and_forward_returns(factor, prices)
create_full_tear_sheet(merged)













...
```
</div>

<div class="abs top-20%" v-motion
    :click-13="{scale:1}"
    :enter="{scale:0}">

## 错误版本
## 正确版本

</div>

<Iframe v-motion
        :click-13="{scale:1}"
        :enter="{scale:0,width:'70%',x:'42%'}"
        :click-15="{width:'75%'}"
        src="http://***************:5180/aaron_24/lab/workspaces/auto-4/tree/wip/presentation.ipynb">
</Iframe>


<!-- <Audio :at=2 name="wechat-huwo"/> -->

<div v-click="15" class="abs toc"
    v-motion
    :click-19="{scale:0}"
    :click-15="{x:750,y:50}">

<Audio :at=15 name="wechat-huwo"/>

:one: <span v-mark="{at: 15, color: 'red', strokeWidth:5}">因子的数学定义？</span>
</div>

<div v-click="16" class="abs toc"
    v-motion
    :click-16="{x:750,y:80}">

:two: <span v-mark="{at: 16, color: 'red', strokeWidth:5}">价格数据 ⚔️ 收盘价</span>
</div>

<div v-click="17" class="abs toc"
    v-motion
    :click-17="{x:750,y:110}">

:three: <span v-mark="{at: 17, color: 'red', strokeWidth:5}">日期对齐？</span>
</div>

<div v-click="18" class="abs toc"
    v-motion
    :click-18="{x:750,y:140}">

:four: <span v-mark="{at: 18, color: 'red', strokeWidth:5}">时间连续性要求？</span>
</div>

<div v-click="18" class="abs toc"
    v-motion
    :click-19="{x:750,y:170}">

:five: <span v-mark="{at: 19, color: 'red', strokeWidth:5}">Alphalens版本？</span>
</div>


<div v-click="19" class="abs animate__animated animate__wobble" style="left: 700px;top: 300px;color:red">

！pip install alphalens-reloaded
</div>

<Promotion :at=20 :dur=10 />

<!--
[click]

在上一期视频中，我们详细介绍了低波动因子的计算方法，也提到了最终生成的因子和价格数据，应该满足什么样的数据格式。

Alphalens有一个特点，易学难精。


我们在运用过程中就常常遇到一些错误，今天我们就来讨论，在因子和价格数据的生成阶段，可能遇到哪些坑。

[click]

如果你正在应聘量化研究员，并且在简历里写了熟练掌握因子分析，就一定要能回答相关的问题。因为一个熟练掌握Alphalens的人，一定会踩过这些坑。

[click]

我们先来看一段充满了bug的代码。这段代码是一个完整的低波动因子分析的示例。使用的是36个月的波动率。

[click]
因子检验从定义universe开始，Universe也就是股票池。

从某种程度上讲，它对因子检验的结果也产生重要影响。如果universe确定的好，那么因子表现一定好。所以，当你做完因子分析，在读结果时，也要想想，这个结果，多大程度上受样本的影响?

[click]

我们在讲因子分析原理时，介绍到，因子分析就是把T期的因子，与T+n期的收益，在universe上进行回归分析。这是理论。

在实战中，它意味着什么呢？它意味着我们在考虑取数据时，就要让价格数据的时间跨度覆盖因子数据的时间。

这里因为我们要取36月的波动因子，所以价格数据必须向前取36个月。这是第12行的逻辑。

第13行这里的移位操作，是因为我们要计算T+n的收益，这里的n可以是1， 5， 或者10，那么我们取价格数据时，要用n的最大值。

这里的shift函数是Zillionare框架提供的。它是将给定的日期/时间向前、向后移动多少个周期的意思。

[click]

第16~20行是常规操作，如果你使用的不是Zillionare框架，那么这些代码要相应修改。最终，我们是要取区间内的行情数据，生成一个dataframe。这个dataframe有asset, close和frame等三列。

[click]
Alphalens要求因子的索引名为date，但我们的数据源用的是frame，所以要更改一下。

[click]

第24到26行，我们计算波动率。上一期讲过如何加速因子计算。但在这里为了简化代码，我们没有使用加速方法。


[click]

这两行是得到符合Alphalens要求的因子数据和价格数据。

[click]

这一行是调用Alphalens的函数，进行数据清洗、分组、计算前向收益等。

[click]

这一行是生成报表。

[click]

看上去很简单、很完美！但是，短短几行代码，蕴藏着好几个bug，对最终的结果也产生了重要影响。接下来我们就运行一下，看看究竟出现了哪些错误。

这里的notebook就是我们刚刚展示过的代码。我们看一下运行结果。

[click]

这一段是Alphalens在进行数据对齐和清洗时的日志，看上去都还正常，对吧？

我们看到Alphalens输出了许多报表，看上去都还正常，对吧？

因子分析和回测tricky的地方就在这里。如果不懂得分析报表，你就不知道报表本身是否正确。所以，初学者往往会在拿到一个很好的回测结果后沾沾自喜，殊不知这个结果一点用处都没有。

这些报表正确吗？我们挑一个Returns Analysis来看，这是因子年化收益分析。我们看到，1D年化收益率是-0.366。无论它具体是什么含义，但是年化收益为负数，总让我们觉得这因子是不是选错了？

我们再往下拉，这里出现了一个明显的错误，好象是程序出错了，这又是怎么回事呢？

事实上，这段代码包含了至少4个以上的错误。现在，我们就来一一解剖下。

今天的视频就到这里。总结一下，我们讨论了：

[click]

1. 因子除了是浮点数之外，还有什么别的要求吗？

这就涉及到因子的数学定义。因子首先必须是一个浮点数；其次，按惯例，它还应该与因子收益正相关。假设我们认为低波动因子是好的，那么，就应该使得因子值越大，未来收益越高；因子值越小，未来收益越小。但是，低波动因子背后的原理是，波动越小，未来收益越高。因此，我们要在这里取个倒数。

[click]

2. 因子数据与价格数据应该如何对齐？

[click]

3. 为什么我们说价格，而不是收盘价？难道不应该使用收盘价来计算前向收益吗？

[click]

4. 因子数据与价格数据在时间上必须连续吗？如果有缺失的记录，会不会影响因子检验结果？
-->

<!--
[click]

在上一期视频中，我们详细介绍了低波动因子的计算方法，也提到了最终生成的因子和价格数据，应该满足什么数据格式。

Alphalens有一个特点，易学难精。

[click]

我们在运用过程中就常常遇到一些错误，今天我们就来讨论在因子和价格数据生成阶段，可能遇到的坑。

如果你正在应聘量化研究员，并且在简历里写了熟练掌握因子分析，就一定要能回答这些问题。

因为一个熟练掌握Alphalens的人，一定会踩过这些坑。

[click]

我们先来看一段充满了bug的代码。这段代码是一个完整的低波动因子分析的示例。使用的是36个月的波动率。

[click]
因子检验的开始是定义universe，或者说股票池。这是我们在前面没有提到的，尽管它不起眼，但实际上是因子检验事实上的开始。

从某种程度上讲，它对因子检验的结果也产生重要影响。如果universe确定的好，那么因子表现一定好。所以，当你得到一个好的因子或者坏的因子时，也要想想，是不是因为我们样本的关系？

[click]

我们在讲因子分析原理时，介绍到，因子分析就是把T期的因子，与T+n期的收益，在universe上进行回归分析。这里理论。

在实战中，它意味着什么呢？它意味着我们在定义因子检验的时间时，在考虑到如何取数据时，就要让价格数据的时间跨度覆盖因子数据的时间。

因为我们要取36月的波动因子，所以价格数据必须向前取36个月。这是第12行的逻辑。

第13行这里的移位操作，是因为我们要计算T+n的收益，这里的n可以是1， 5， 或者10，那么我们取价格数据时，要用n的最大值。

这里的shift函数是Zillionare框架提供的。它是将给定的日期/时间向前、向后移动多少个周期的意思。

[click]

第16~20行是常规操作，根据我们使用的数据源的不同，这里的代码可能有所不同。最终，我们是要取区间的行情数据，生成一个dataframe。这个dataframe有asset, close和frame等三列。

[click]
Alphalens要求因子的索引名为date，但我们的数据源用的是frame，所以要更改一下。

[click]

第24到26行，我们计算波动率。


[click]

这两行是得到符合Alphalens要求的因子数据和价格数据。

[click]

这一行是调用Alphalens的函数，进行数据清洗、分组、计算前向收益等。

[click]

这一行是生成报表。

[click]

看上去很简单、很完美！但是，短短几行代码，蕴藏着好几个bug，对最终的结果也产生了重要影响。接下来我们就运行一下，看看究竟出现了哪些错误。

这里的notebook就是我们刚刚展示过的代码。我们看一下运行结果。

[click]
【4-output】
这一段是Alphalens在进行数据对齐和清洗时的日志，看上去都还正常，对吧？

我们看到Alphalens输出了许多报表，看上去都还正常，对吧？

因子分析和回测tricky的地方就在这里。如果不懂得分析报表，你就不知道报表本身是否正确。所以，初学者往往会在拿到一个很好的回测结果后沾沾自喜，殊不知这个结果一点用处都没有。

这些报表正确吗？我们挑一个Returns Analysis来看，这是因子年化收益分析。我们看到，1D年化收益率是-0.366。无论它具体是什么含义，但是年化收益为负数，总让我们觉得这因子是不是选错了？

我们再往下拉，这里出现了一个明显的错误，好象是程序出错了，这又是怎么回事呢？

事实上，这段代码包含了至少4个以上的错误。现在，我们就来一一解剖下。

[click]
【4-18】
第18行，我们看一下数据源给我们的数据的情况

【4-32】
第32行，我们看一下此时合成的dataframe是什么样子。

【4-34】
第34行，我们看一下生成的factor是什么样子。也没什么不对，对吧？但实际上，这个因子数据是错的。

[click]

实际上，因子数据是有要求的，首先，它必须是一个浮点数。这个比较好理解。在单因子检验中，不需要做归一化，所以，取值范围是实数域。

第二点，我们按因子与预期收益是一种正相关系来设计因子。如果低波动因子有效，那么应该是波动率越低，预期收益越高；显然，波动率与预期收益是负相关关系。为了将其调整为正相关关系，我们可以将其取倒数。因此，我们这样修改代码：

【4-35】
我们再看一下prices数据。它是通过pivot方法来获取的。我们获取的是close数据。

[click]
这样做对吗？做量化我们非常依赖收盘价。但为什么我们一直说的是价格数据，而不是收盘价呢？

我们回想一下，因子分析的原理。它是拿T0期的因子，与T+n期的收益，在universe上做回归。这里的因子是用T+0的收盘价计算出来的。如果我们仍然使用T0期的收盘价来作为买入价来计算T+n期的收益，那我们就犯了前视偏差错误！

因此，我们应该用离T0期收盘价最近的数据，也就是T1期的开盘价。因此，我们要这样修改代码：

我们再回到价格数据。注意看索引！这个因子是月度因子，为什么会出现1月6~1月12等日期的价格数据？这个问题是pivot带来的。当我们通过pivot，将长表转换成为宽表时，之前000001没有的1月6日的记录，但000002有，所以转换之后的表格，就会存在这个索引，但对000001来说，记录就空着。

这会有什么问题？我们来看看Alphalens计算出来的收益数据。这个数据保存在merged变量中。

我们看到，2008-12-31到2009-01-23这两个月，平安银行的收益率都是0。这个数字看起来不太合理。我们通过行情软件对比，发现这个数字Alphalens算错了!

问题就在pivot转换来的价格数据中。它本来应该只包含那些在factor变量中存在的日期，也就是每个月结束的日期。Alphalens并不知道我们当前在算日收益还是月收益，它只能通过factor和price记录，来推断预期收益。显然，在这里，由于数据不太完美，它的推断出了错。

这并不是一个只在我们的数据源上会发生的问题。实际上是一个非常普遍的问题。数据总是不完美的。实际上，多数人做量化，很难做到真正的创新、找到新的因子和新的策略，重要的是，把已知的因子、策略实现正确，然后适当进行宏观择时，就能取得相对于他人的超额收益。

怎么补救？我们要进行日期对齐。做法是，对原始数据按asset分组之后，进行重采样。通过重采样，我们会达到这样的效果，所有的股票的价格数据，都会对齐到月底。
[click]
这就是第三个错误，日期对齐问题。现在，我们这样修改代码

我们看一下代码改正后，得到的merged变量。现在，日期都对齐到每个月末了。
[click]
这里还有一个问题，如果某支股票存在缺失值，会影响到Alphalens计算前向收益吗？会影响到因子检验吗？

答案是不会。这个问题留给大家。

最后，我们看一下改正后的代码，进行因子分析的结果。可以看出，现在年化收益率为正数了。但是，在最后部分，还是出现了语法错误。

[click]

这是因为，我们使用了Alphalens的较旧的版本。它依赖0.25版的pandas，而在我们的环境中，使用的是pandas的版本已经是1.5以上了。要解决这个问题，我们需要安装最新的Alphalens，即Alphalens-reloaded

今天的视频就到这里。接下来的视频，我们将讲解在Alphalens的因子检验中，如何实现分层回溯、行业中性化的问题。

这个系列的视频，我们会从因子分析讲到机器学习策略的实现，讲解非常细致，与实战结合紧密，学完就能实操，欢迎关注和收藏，以便及时得到更新。
-->
