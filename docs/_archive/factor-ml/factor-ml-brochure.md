---
img: https://images.jieyu.ai/images/hot/mybook/girl-on-sofa.jpg
motto: Learning without thought is labor lost
title: 因子投资与机器学习策略
---

---

<style scoped>

.wrapper {
    width: 100%;
    height: 600px;
    left:0;
    top:400px;
    position:relative;
    color: black;
    text-shadow: 1px 2px 2px rgba(0,0,0,0.5);
    background: linear-gradient(to right, rgba(255,255,255,0.05), rgba(255,255,255,0.9), rgba(255,255,255,0.95)), url(https://images.jieyu.ai/images/hot/mybook/girl-on-sofa.jpg);
    display:flex;
    align-items: center;

    .logo {
        position: absolute;
        width: 200px;
        height: 200px;
        margin-left: 50px;
        border-radius: 1em;
        background: url(https://images.jieyu.ai/images/hot/logo/quantide-alpha-yellow.jpg);
        background-size: cover;
    }

    .title-wrapper {
        position: absolute;
        width: 60%;
        left: 40%;
    }

    .title {
        font-size: 10vw;
        font-family: 'QingLiuLiShu';
        text-align: center;
        margin-right: 5%;
    }
}
</style>

<div class="wrapper">
<div class="logo"/>
<div class="title-wrapper">
<div class="title mt-10%">因子投資</div>
<div class="title">與</div>
<div class="title">機器學習策略</div>
</div>
</div>

---

<style scoped>

.wrapper {
    width: 100%;
    height: 100%;
    left:0;
    top:0;
    position:absolute;
    color: #f0f0f0;
    z-index: -2;
    background-image: linear-gradient(rgba(255,255,255,0.3), rgba(255,255,255,0.9)), url(https://images.unsplash.com/photo-1487147264018-f937fba0c817?w=700&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTJ8fGJhY2tncm91bmR8ZW58MHx8MHx8fDA%3D);
    background-size: cover;
}

.blackboard {
    position: absolute;
    width: 100%;
    height: 49%;
    left: 0%;
    top: 5%;
    border: 1px solid black;
    background: black;
    z-index: -1;
    background-image: url(https://images.jieyu.ai/images/2024/08/empty-blackboard.avif);
    background-size: cover;
}

.title {
    position: absolute;
    top: 10%;
    text-align: center;
    font-size: 10vw;
    font-weight: 700;
    width: 100%;
    /* color: color-mix(in srgb, #09BED9, #000 20%); */
    font-family: 'HongLeiBanShuJianTi';
    color: #f0f0f0;
    text-shadow: 2px 2px 5px rgba(255,255,255, 0.5);
}

.instructor {
    position:absolute;
    top:100px;
    width:100%;
    height:100%;
    /* box-shadow: 5px 5px 10px #403C4280; */
    z-index: 1;

    img {
        position:relative;
        left:-450px;
        top: 200px;
    }
}

.instructor-info {
    width: 40%;
    position: absolute;
    left: 57%;
    bottom: 10%;
}

.instructor-item {
    border-radius: 1.5em;
    font-size: 3.5vw;
    height: 2.5em;
    margin-bottom: 0.5em;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url('https://images.jieyu.ai/images/hot/blue-purple-gradient.jpg');
    font-weight: 700;
    color: black;
}

.contact {
    position: absolute;
    bottom: 25%;
    right: 15%;
    height: 250px;
    width:200px;
    text-align:center;
    padding: 0;
    color: black;

    img {
        margin: 1em auto !important;
    }
}

</style>

<div class="wrapper">

<div class="blackboard">
<div class="title">
因子投资与机器学习策略
</div>
<div class="abs text-5xl top-35% left-10%">课程模块</div>
<div class="abs text-4xl top-50% left-30%">1. 因子检验与 Alphalens</div>
<div class="abs text-4xl top-60% left-30%">2. 因子挖掘 (400+)</div>
<div class="abs text-4xl top-70% left-30%">3. 机器学习构建量化策略 (XGBoost)</div>
<div class="abs text-4xl top-90% left-50%">Presented By: Aaron@QuanTide </div>
</div><!--blackboard-->

<div class="contact">
<img src="https://images.jieyu.ai/images/hot/quantfans.png"/>
<span>扫码加课程助教</span>
</div>

<div class="instructor">
<img src="https://images.jieyu.ai/images/hot/instructor/portrait-stand.png"/>

<div class="instructor-info">
<div class="instructor-item">
前量化投资公司合伙人
</div>
<div class="instructor-item" style="margin-left:-2em;">
Zillionare 开源量化框架作者
</div>
<div class="instructor-item" style="margin-left:-4em;">
著有《Python 高效编程实践指南》
</div>
</div><!--instructor-info-->
</div><!--instructor-->
</div><!--wrapper-->

---

<style scoped>
.wrapper {
    width: 100%;
    height: 100%;
    left:0;
    top:0;
    position:absolute;
    color: #f0f0f0;
    z-index: -2;
    background: linear-gradient(rgba(255,255,255,0.9), rgba(255,255,255,0.3)), url(https://images.unsplash.com/photo-1528460033278-a6ba57020470?q=80&w=2235&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D);
    background-size: cover;

    .takeout {
        position: absolute;
        bottom: 5%;
        right: 5%;
        font-size: 7vw;
        padding: 0.15em 1em;
        border-radius: 1em;
        background: color-mix(in srgb, #82B7C6, #000 20%);
        box-shadow: 1px 2px 2px rgba(0,0,0,0.5);
        color: white;
    }
}

.objective {
    position: absolute;
    top: 5%;
    right: 5%;
    font-size: 7vw;
    padding: 0.15em 1em;
    border-radius: 1em;
    background: color-mix(in srgb, #82B7C6, #000 20%);
    box-shadow: 1px 2px 2px rgba(0,0,0,0.5);
    color: white;
}

.item {
    position: absolute;
    font-size: 5vw;
    font-weight: 500;
    color: red;
    width: 100%;
    padding: 0.5em 1em;
    border-radius: 1em;
    background: linear-gradient(to right, rgba(130,183,198,0.9), rgba(100,150,170,0));
    box-shadow: 1px 2px 2px rgba(0,0,0,0.5);
}

</style>

<div class="wrapper">

<div class="objective">课程目标</div>
<p class="item top-10% rotate-z-15">🔍 会用 Alphalens 因子分析框架</p>
<p class="item top-20% rotate-z-15">📈 会看 Alphalens 报表和判定因子有效性</p>
<p class="item top-30% rotate-z-15">⛏️ 览遍 400+因子，掌握因子挖掘的方法和技术</p>
<p class="item top-40% rotate-z-15">💻 掌握机器学习、聚类模型、XGBoost 模型</p>

<div class="takeout">Takeaway</div>
<p class="item top-50% rotate-z--15">📚 带走 350+因子实现</p>
<p class="item top-60% rotate-z--15">🧩 带走 Pair Trading 中性策略，聚类算法寻找配对资产</p>
<p class="item top-70% rotate-z--15">💰 带走基于 XGBoost 的资价格预测模型</p>
<p class="item top-80% rotate-z--15">💲 带走基于 XGBoost 的趋势交易模型</p>

</div><!--wrapper-->

---

<style scoped>
.wrapper {
    width: 100%;
    height: 100%;
    left:0;
    top:0;
    position:absolute;
    color: #f0f0f0;
    z-index: -2;
    background: linear-gradient(rgba(255,255,255,0.9), rgba(255,255,255,0.8)), url(https://images.unsplash.com/photo-1521133573892-e44906baee46?q=80&w=2187&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D);
    background-size: cover;

    .takeout {
        position: absolute;
        top: 5%;
        left: 25%;
        font-size: 7vw;
        padding: 0.15em 3em;
        border-radius: 1em;
        background: #F8D875;
        box-shadow: 1px 2px 2px rgba(0,0,0,0.5);
        color: white;
    }
}

.module {
    position: absolute;
    width: 90%;
    height: 250px;
    border-radius: 1em;
    background: rgba(255,255,255,0.2);
    color: black;
    li {
        font-size: 4vw;
    }
}

</style>

<div class="wrapper">

<div class="takeout">
教学方式
</div>

<div class="module top-18% left-5%">
<div style='width:20%;float:left;padding: 0rem 1rem 0 0;text-align:center'>
<img src='https://sli.dev/logo-title.png' style="margin: 0 !important;">
</div>

<li>由 Slidev 驱动 演示从未如此动感十足又紧随焦点</li>
<li>Line Highligting 实现动画自由，牢牢跟随代码讲解</li>
<li>Notebook 嵌入运行，演示与 Notebook 紧密结合</li>

<!-- <v-drag-arrow color="red" width="3" class="abs top--80% left-60% opacity-20"/>
<v-drag class="abs top--80% left-60% w-30% h-200px opacity-20">
<Box />
<Ellipse/>
</v-drag> -->
</div><!--module-->

<div class="module top-35% right-5%">

<div style='width:20%;float:right;padding: 0rem 0 0 1em;text-align:center'>
<img src='nbgrader.jpg' style="margin: 0 !important;">
</div>

<li>作业也是 Notebook 格式，方便编辑和运行</li>
<li>作业自动分发，自主校验，定时提交，老师批改</li>
<li>老师评语，仅本人可见，彼此独立</li>

</div><!--module-->

<div class="module top-52% left-5%">

<div style='width:20%;float:left;padding: 0 4em 0 0;text-align:center'>
<img src='class-size.png' style="margin: 2em 0 0 0 !important;">
</div>

<li>腾讯会议直播上课</li>
<li>会议录播，错过可回看</li>
<li>一对一辅导群，你的私人定制！</li>

</div><!--module-->

<div class="module top-69% right-5%">

<div style='width:20%;float:right;padding: 0 0 0 0;text-align:center'>
<img src='https://images.jieyu.ai/images/hot/logo/quantide-alpha-yellow.jpg' style="margin: 2em 0 0 0 !important;">
</div>

<li>咨询课程助理，选择课程</li>
<li>下单成功，通知课程助理创建专属课程环境</li>
<li>助理创建 1：1 辅导群，发送账号、通知上课时间</li>
<li>按时上课，及时完成作业和寻求辅导</li>
<li>两个月后，结束课程</li>

</div><!--module-->

</div><!--wrapper-->
