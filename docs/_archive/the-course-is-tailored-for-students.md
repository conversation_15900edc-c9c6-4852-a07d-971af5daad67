---
title: 练习多，还得赶 due! 为什么高校生要来上这门量化交易课？
date: 2024-09-24
category: career&figure
slug: 
motto: 
img: https://images.jieyu.ai/images/university/yale-new-haven-autumn.jpeg
stamp_width: 60%
stamp_height: 60%
tags: []
---

多年以后，当量化分析师 Jack 坐在陆家嘴世纪金融广场的豪华办公室里，向 VIP 客户介绍自己最新的量化策略的时候，他还会想起那个决定性的夜晚。那时候，Jack 还是一名研二的学生，即将面临择业，意识到自己在校期间学习到的知识，清浅到清澈见底，又古老得像史前的巨蛋。

![](https://images.jieyu.ai/images/hot/shanghai-1.jpg)

那时，他意识到自己把一切都拖到了最后一刻。钟表的滴答声在寂静的房间里回荡，每一秒都在嘲笑他的拖延。他依然能感受到桌上堆积如山的课本的重量，空气中弥漫的咖啡香气，以及笔记本电脑屏幕上微弱的光芒在墙上投下的影子。

正是在这时候，他刷到了《因子分析与机器学习策略》这门课程，一下子就被迷住了。

![](https://images.jieyu.ai/images/hot/course/factor-ml/lizhi-cover.png)

这门课不一样。

Learning without thinking leads to confusion。他说。

于是他也这样做了。

首先是量化课程的内容，不是简单地照搬官方文档，或者照搬象牙塔里不接地气、无法实战的学术研究，而是基于真实的策略、量化库实现和分析师社区的提问，重新进行了思考、解构、重组，赋予自己的灵魂。

比如，在讲因子分析时，到现在为止，你都找不到一份中文资料，超出某券商研报的内容，但它只讲了如何按步就班地调用API，然后对图表进行了最简单地解读。

然而，成功的道路只有一条，失败的方式却五花八门。只讲正确的做法，课程就太简单，就不会有深度、有思者。所以，这门课不仅讲了正确的因子分析过程是怎么样的，正常的因子报表数值范围是怎么样的，还介绍了分析过程出错、或者引入了未来数据时，因子报表会是什么样的表现。

这部分内容中比较如此之多，简直就是比较经济学课堂现场。

最终，正确的因子分析就不再是一种偶然，而是我们排除了各种偏差、错误、随机之后的必然结果（听上去很像机器学习）。这意味着课程的编写者要进行大量的试错（或者社区搜索），才能换得听课者毫不费力，就能获得数年的经验。

当然，机器学习策略部分也是一样。编写者从自然是连续的，还是不连续的哲学问题思考起，讲起了机器学习的两大根本问题：回归还是分类？在指出不可能通过机器学习构造一个决定性的价格预测模型之后，又给出了自己的回答：只要找到虫洞，仍然可能穿越到未来，从而"预测"过去！

![](https://images.jieyu.ai/images/2024/09/interstella-wormhole.png)

只有编写者自己思考还不够，他决定把听课者也卷入进来。

你们爱看短视频，可不仅仅是因为擦边，而是有的短视频做得太精彩啦。所以，这门课在课件上也是精心制作，引入大量的动画来抓住你的注意力，避免和尚念经。

直播课更是引入了各种小quiz，通过倒计时来催着大家思考。

![](https://images.jieyu.ai/images/2024/09/countdown-timer.png)

