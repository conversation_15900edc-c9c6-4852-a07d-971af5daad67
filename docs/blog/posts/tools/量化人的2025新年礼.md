---
title: 睽违17年，ta-lib重装出发！
slug: ta-lib-is-reloaded
date: 2024-12-31
category: arsenal
motto: You only live once, but if you do it right, once is enough
img: https://images.jieyu.ai/images/2024/12/book-of-sun-le.jpg
tags: 
    - tools
    - programming
---

在你看到这篇文章时，2024年已经余额不足，而新的一年，正在等待我们冲刺。新的一年，作为量化人，你将在新年里收到哪些礼物呢? 

先来晒一下我个人收到的礼物吧。昨天一早，收到了孙乐总赠送的《山河独憔悴》，并且很贴心地为我题了字。孙乐总是民主党派人士，江苏省收藏家协会理事和勋奖章收藏专业委员会副秘书长、美国钱币学会会员。

在书序中有这样一段话：

知识的本质被认为是『看』世界，是去看出事物的本质和真相。这本书则是关于换一个角度去理解历史，把『什么看成什么』和把『什么不看成什么』，将这种观看变成一种哲学行为。我想，我们做量化，归根结底也是要从数据的表现中跳出来，把『什么看成什么』这是找到规律，把『什么不看成什么』，这是去伪存真，过滤噪声。

![](https://images.jieyu.ai/images/2024/12/book-of-sun-le.jpg)

这里也有一点小花絮。这本书作者倾向的名字是《江山独自憔悴》。我不懂声律，但也觉得这个标题更响亮和更有韵律感。不过，书中搜集的百余张精美的图片（铜版纸彩印）就在这里，并没有隐去，也没有打码，它们就是曾经的世界。期待这本书尽快在京东和当当上上架。

这个新年，你会收到什么样的礼物呢？不过，作为量化人，我们所有人都收到了一份重磅礼物，ta-lib的c库更新了！hV3*ifARjC@cp8O!

## ta-lib重装出发！

就在新年前几天，ta-lib悄没声地更新了0.6.1版本。而上一个版本，还是17年之前发布的0.4.0.

![](https://images.jieyu.ai/images/2024/12/20241230161950.png)

这么久没有更新的原因是，原作者Mario Fortier一直希望找到一位更年轻的开发者来维护这个库，他本人觉得自己对更现代的C++语言特征，尤其是跨平台编译这一块有些陌生。尽管实际上他是一名非常资深的C++网络开发者，并且发明了在3G通信中使用的实时数据压缩算法。不过，他最近的工作已转向了Python，并且创办了自己的公司（从事区块链和网络软件开发）。

不过久久没能找到接班人，Mario决定继续干下去。于是，在12月23日，他发布了0.6.1版本。这个版本没有增加新的功能，主要是解决编译和自动化工具相关的问题。之前安装ta-lib的c库对初学者而言并非坦途，特别是在Windows下：要么接受恶意软件的风险，要么自己从下载好几个G的Visual studio编译器开始。这也是为什么《量化24课》需要讲解Ta-lib安装问题的原因之一。

0.6.1一发布就收到热烈反馈--包括bug report，于是，在三天后， Mario又发布了0.6.2这个版本 -- 得益于在0.6.1上所做的工具，我们看到新版的ta-lib的可维护性大大增强，以致于可以在3天之内发布新的版本 -- 这包括引入了Github Actions使得整个打包和发布工作自动化。

现在，在windows下安装ta-lib变得轻而易举：

![](https://images.jieyu.ai/images/2024/12/ta-lib-on-windows.png)

不过，它的python-wrapper尽管也为0.6.1进行了更新，但是，没能通过我们的安装测试 -- 在安装python-talib（通过pip install TA-Lib）时，仍然提示需要有vsc++ build tools 14。相信这个问题能很快解决。

在mac下，最新的ta-lib安装非常简单，只需要执行：

```bash
brew install ta-lib
```

如果之前已经安装了ta-lib的旧版本，它会提示我们此次安装将会更新。

在debian系列的Linux（即Ubuntu, Mint）下，安装也很容易，下载后缀为*.deb的安装包，再执行命令：

```bash
sudo dpkg -i ta-lib_0.6.0_*.deb
```

即可。在Linux下支持的cpu架构包括了386， amd64架构和arm64架构，*号就是用来匹配这个架构的。对其它系列的Linux，则仍然要从源码构建，不过，在Linux下进行构建非常容易。

## ta-lib的周边

ta-lib最重要的周边应该是github上的ta-lib-python这个库了。它集得了接近1万的star，这个级别的star数本来应该是被AI项目占据的 -- 这也充分说明近年来量化金融的受众正在迅速扩大。

ta-lib-python也迅速响应了ta-lib的更新，最新发布的0.5.2，已经适配了ta-lib的0.6.1版本。根据我们的测试，在mac上整个安装过程非常丝滑，先安装ta-lib的c库，再安装ta-lib-python不会出任何错误。但是在Windows下，即使已经通过msi安装了ta-lib的c库，但它的python-wrapper似乎仍然无法找到已经安装的c库和头文件，因此，仍然需要本地构建ta-lib的c库。

在等待ta-lib c库更新的日子里，ta-lib-python也并不有闲着。它先是完成了通过cython，而不是swig来绑定c库这一转换，据称带来了2~4倍的性能提升。更激动人心的是，Python 3.13放出了GIL-Free模式之后，作者正在尝试这个GIL-Free版本。一旦Cython 3.1正式发布（ta-lib-python依赖于这个版本），很有可能ta-lib-python就会立即支持GIL-Free。

另一个重要的周边是polars-talib，也正在积极开发中，尽管目前还只是0.1.4版本。它是polars的一个扩展，根据测试，计算速度比在pandas中使用talib（ta-lib-python）快了100多倍。

除此之外，一个用rust实现的ta-lib也正在开发中。

美好的事情正在发生，就在这个新年将来到来的时刻。

## 下一站

ta-lib的下一站更精彩。在扫除了构建和自动化障碍之后，ta-lib将可以以更快的速度、更小的精力来发布新的功能。长期以来，ta-lib缺失了一些深受大家喜爱的技术指标，比如KDJ， PVT, TMO等等。社区已经提出了14个待实现的指标，其中有两个，即RMA和PVI已经排上日程。

!!! tip
    如果你正在发愁挖不到新的因子，建议自己先实现RMA和PVI。毕竟这两个指标优先被排上日程是有原因的。当然，我也很期待Connors RSI和Awesome Oscillator指标的实现。

![](https://images.jieyu.ai/images/2024/12/talib-new-functions.jpg)

## 摇人！

如果你喜欢Quantide公众号的文章，也想和我们一起研究量化，学习、开发、分享，欢迎加入匡醍！我们欢迎 the crazy ones， the misfits，the rebels！



![草丛后面藏着什么?雄狮少年2](https://images.jieyu.ai/images/2024/12/lion2.jpg)
