---
title: 除了编程，量化人还能怎么用AI？
date: 2025-08-05
img: https://cdn.jsdelivr.net/gh/zillionare/imgbed2@main/images/2025/08/brooke-lark-8beTH4VkhLI-unsplash.jpg
excerpt: 当量化人面临信息过载的困境时，AI悄然成为破局关键。从Grok的私人定制资讯，到豆包和NotebookLM将研报秒变播客，再到TradingAgents项目——一个能模拟真实交易团队、年化收益提升30%的神秘框架。这个由UC伯克利开发的多智能体系统，究竟如何让一个人拥有整个交易团队的决策能力？
category: tools
tags:
    - tools
    - AI
---

知乎问题，除了编程，量化人还能怎么用 AI？

**AI将重建一切**。今天早上看到但总的一个发言，AI 是十年一遇的浪潮，就像 2000 年的互联网、2013 年的移动支付。现在不买算力、模型、应用，以后只能拍大腿！这是量化人应该如何用AI的第一个想法，就是在投资上『All in AI』。

说点真的。都说现在是一个内卷的时代。作为量化人，新的技术、算法、模型、资讯每天层出不穷，根本看不过来、学不过来。

怎么办？

## Grok 私人定制

生活中总是充满矛盾。

一方面，我们常常感叹信息过载、需要采取信息断食；另一方面，我们又常常感觉自己仍然生活在信息荒原之中。

无他，只因我们差一个智能助手。如果有一个智能助手帮我们主动搜集、筛选，只把我们感兴趣的资讯，主动送到我们手上来，吃上信息特供，就不会有『营养』问题了。这方面，我们大概可以使用 Grok 订阅任务。

![](https://cdn.jsdelivr.net/gh/zillionare/imgbed2@main/images/2025/08/20250805151337.png)

用法很简单，没有一个量化人会需要我介绍。在最后，填上一个邮箱地址，这样资讯摘要就会准时抵达你的邮箱。

## AI播客，自学神器

阅读是视觉维度，但要看的资料太多，眼睛累不过来。于是，我们就想到了制作《量化好声音》播客，为学习和成长开启另一个维度--听觉维度，作为大家在地铁通勤、睡前时可以听的有声读物。

在制作节目的过程中，我们也发现了AI的一些新用法，这里分享给大家 -- 你也可以用它们来制作自己的专属播客，把来不及『看』的量化论文、研报，制作成播客读物，做到人在路上，学习在线上。

![](https://images.jieyu.ai/images/hot/promotion/voice.jpg)


这里有两个工具，一个是豆包的AI播客。只要你上传研报，豆包就会自动生成一个音频播客。标准男女双人搭配聊天，声音自然流畅、轻快活泼，就如同在听一场情感播客一样。

另一个则是 Google 的 NotebookLM。与豆包相比，它的音色更像新闻联播，更稳重端庄一些。不过两者的关键差异，在于可定制性。

豆包播客无法定制内容，一次只能投喂一个材料源；Google 的 NotebookLM 则可以投喂多个材料源，并且通过提示词来指导内容的生成。比如，对于一篇研报，我们可以这样定制提示词：

!!! tip
    你是一名量化金融专家。你拿到的是一份研报，将生成一个双人对话的播客。两名主持人将讨论以下问题：
    1. 这篇研报提出了什么样的观点？
    2. 研报的价值在哪里？
    3. 听众可以从中学习到什么样的知识点、技巧、方法、获得什么样的数据？
    4. 研报中提到的方法，具体的实现步骤如何？
    5. 量化人应该如何运用这些结论？
   
   你生成的内容必须完全忠实于研报，包括每一个数字、每一个结论、每一项推导，不得修改。语气不要太浮夸，不要过多赞美研报和结论。


现在，拿着这些提示词，自己试一试吧。

基于大语言模型，它能清晰地解构研报的关键内容，并重组为轻松活泼的双人对话。

只要大概5~10分钟，就能『听』完一份研报。这一点非常重要。如果不是专门给出工作时间，我觉得大家很难有时间一年读完300份研报论文。

借助这个方法，我们可以快速『听』完300份研报论文，然后择其要精读之。这样就能大大加快我们撷取他人精华的速度。

## 一个人，加上AI，就是一个交易团队

这可能是量化人用 AI 最高级的用法之一。就是通过 AI 打造一个交易团队。

传统交易团队中，大家最希望听到的是不同的声音，毕竟兼听则明嘛。但是人性又会导致，我们最不能接受的，就是不同的声音。所以，在一个交易团队中，我们实际上即使收集到到了不同角度的信息和观点，最终也很难做到科学决策，一言堂是必然的结果。

这就是 TradingAgents 项目能带来的核心价值。

**TradingAgent** 是一个**基于大语言模型（LLM）的多智能体金融交易框架**，由UC伯克利、MIT等高校中国学者开发，在 github 上获得了超过18k 的星标。

它的本质是模拟**真实交易团队协作流程**，通过多个分工明确的智能体（Agent）协同完成交易决策。也就是说，部署了一个 TradingAgent ，你实际上将得到一个交易团队。所以，这是为什么这么多人在用它的根本原因。


这个『团队』的核心构成是：

  - **分析师团队**：包含基本面分析、技术分析、新闻/社交媒体情绪分析等智能体，负责数据清洗、指标生成。  
  - **研究团队**：分为“乐观派”和“悲观派”，分别生成买方证据和卖方证据，模拟多视角辩论。  
  - **交易执行链**：交易员智能体整合双方证据形成交易建议，经风控团队审核后，由“基金经理”智能体决策执行。  


TrdingAgents 能解决什么问题呢？ 它能搜集并整合多维度的资讯、数据和指标，既模拟了真实交易团队的复杂互动，又克服了人类沟通中的困难和主观偏差，避免了沟通中的信息扭曲、丢失、人与人之间的不合理对抗等。

这张图基本上刻画出了它的运行模式：

<div style='width:66%;text-align:center;margin: 0 auto 1rem'>
<img src='https://cdn.jsdelivr.net/gh/zillionare/imgbed2@main/images/2025/08/20250805160149.png'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>


它有多个智能体。这些智能体可分为5个层次。最底层的牛马是分析师，负责抓取相关数据。他们输出的数据和指标，会交给研究层。

这一层的智能体分为两派，一派是乐观派，一派是悲观派。两派会激列地辩论。通过辩论，事实得到澄清，逻辑逐渐清晰。最后，他们将向交易层的 Agent 提出买方和卖方建议。

这个地方 AI 的优势就进一步体现出来了。AI Trader 只会基于事实和数据，而不是根据喜欢谁、厌恶谁来决定听取谁的意见，更不会出现一句顶一万句的领导决策。

<div style='width:66%;text-align:center;margin: 0 auto 1rem'>
<img src='https://cdn.jsdelivr.net/gh/zillionare/imgbed2@main/images/2025/08/vitaly-gariev-VZQAo20ArSA-unsplash.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>


在真理越辩越明之后，它会把交易建议发给后面的风控层的 Agent。最后，风控层的 Agent 会在评估之后，再向经理层提出交易建议，并由经理层的 Agent 做出最终的交易决定和执行。这是不是像极了一个真实的、管理十亿资金的交易团队？

TradingAgents 回测显示，在苹果、谷歌、亚马逊等标的上，年化收益提升近 30%，最大回撤控制得非常优异。

TradingAgents 的核心价值和意义在哪里？可能最大的价值，就是**它的协作远超人类自己**。一方面，Agent 收集资讯、思考和交流的速度远超人类自己，另一方面，它们在协作时，不受情绪波动影响。

不受个人喜好厌恶影响，永远都只做『**理性辩论**』，这恰恰是人类无法企及的目标。
