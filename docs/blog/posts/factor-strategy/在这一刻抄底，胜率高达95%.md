---
title: 在这一刻抄底，胜率高达95%
slug: falling-knife-or-just-a-bungee-jumping
date: 2024-11-20
img: https://images.jieyu.ai/images/2024/11/the-catcher-in-the-rye.png
category: strategy
motto: 没有最终的成功，也没有致命的失败，最可贵的是继续前进的勇气。
tags: 
    - strategy
---


在4月8日，我们发表了一篇名为《1赔10，3月27日我抄底了》的文章，基于坚实的统计数据，说明了为什么当天应该抄底。时间过去了半年，中证1000又为我们提供了两个新的例证。这篇文章我们就来回顾一下。

## 原理和定义

我们先介绍一下原理。你可能观察到，当发生一段连续下跌时，那么下跌的幅度越大，则反弹的力度就越大，并且越可能发生反弹。这就像弹簧一样，或者像是蹦极 -- 在连续下跌到某个点之后，总会往回弹一点。

但我们需要求出连续下跌到什么程度时，反弹的概率会是多大。这样一来，我们的操作才有更有底气和依据。

首先需要定义什么是连续下跌。实际上，我们可以像前文所述，使用每日涨跌。但在这篇文章里，我们想尝试另一种方法，即通过连续阴线区间的跌幅来定义，这样可以过滤一些假信号。

具体的计算方法如下图：

![](https://images.jieyu.ai/images/2024/11/falling-knift.jpg)

我们先看图中序号1到序号2这一段。这是10月8号到10月11日的走势。10月8日这一天跳空高开，接着一路下跌直到10月11日，8号开盘买入的人，到11日收盘时，亏损达到15.8%。这个损失，就是我们所说的连续阴线区间的跌幅。

我们再看序号3到序号4这一段。这是11月14日到11月18日的情况，14日开盘买入者共亏损7.5%，连续阴线区间的跌幅就是7.5%。

如果我们使用连续下跌（而不是连续阴线的区间跌幅）来计算最大跌幅，将会是从11月12日起开始计算下跌，但提前一天（11月15日）就可能给出抄底信号，这个信号就有点过早。因为在这区间中，出现了一天的假阳线（13日，下跌但收阳线）。这一天**筹码发生交换**，假设前一日亏损者把筹码全部倒给了新入场者，那么**活跃交易者的持仓成本是下降的**，他们还能再扛一阵子。

这是我们这次改进中，使用连续阴线的区间跌幅的原因。但实际上两种定义各有优劣，你可以使用机器学习模型来决定何时使用哪一个。

## 代码实现

既然模型定义清楚，现在我们就开始实现。这段代码需要用到发现连续阴阳线的一个函数，我们把它定义为find_runs:



```python
def find_runs(x):
    """Find runs of consecutive items in an array.
    """

    # ensure array
    x = np.asanyarray(x)
    if x.ndim != 1:
        raise ValueError("only 1D array supported")
    n = x.shape[0]

    # handle empty array
    if n == 0:
        return np.array([]), np.array([]), np.array([])

    else:
        # find run starts
        loc_run_start = np.empty(n, dtype=bool)
        loc_run_start[0] = True
        np.not_equal(x[:-1], x[1:], out=loc_run_start[1:])
        run_starts = np.nonzero(loc_run_start)[0]

        # find run values
        run_values = x[loc_run_start]

        # find run lengths
        run_lengths = np.diff(np.append(run_starts, n))

        return run_values, run_starts, run_lengths
```

为了让大家都能复现代码，我们使用了akshare来提供数据。这是一个免费、开源的行情数据源。

```python
import akshare as ak
now = datetime.datetime.now().date()
start = now - datetime.timedelta(days=365*4)

start_date = start.strftime("%Y%m%d")
end_date = now.strftime("%Y%m%d")

# 通过akshare获取中证1000日线数据（近1000天）
bars = ak.index_zh_a_hist(symbol="000852", start_date=start_date, end_date=end_date)

bars.rename(columns = {
    "日期": "date",
    "开盘": "open",
    "最高": "high",
    "最低": "low",
    "收盘": "close",
    "成交量":"volume"
}, inplace=True)

bars["date"] = pd.to_datetime(bars["date"])
bars.set_index("date", inplace=True)

bars["flag"] = np.select([bars["close"] > bars["open"], 
                          bars["close"] < bars["open"]], 
                          [1, -1], 
                          0)
v, s, l = find_runs(bars["flag"] == -1)

cum_neg_returns = []
for vi, si, li in zip(v, s, l):
    if vi and li > 1:
        cum_neg_returns.append((bars.index[si], 
                                bars.index[si + li-1], 
                                bars.close[si + li -1 ]/bars.open[si] - 1))
        
r = pd.DataFrame(cum_neg_returns, columns=["start", "end", "cnr"])
r.cnr.hist()
```

从生成的直方图来看，连续跌幅达到7.5%以后的次数就很少了，也就是，连续下跌超过7.5%，还能继续下跌是小概率事件。

<!-- BEGIN IPYNB STRIPOUT -->
<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/2024/11/falling-knife-hist.jpg'>
<span style='font-size:0.8em;display:inline-block;width:100%;text-align:center;color:grey'></span>
</div>
<!-- END IPYNB STRIPOUT -->

我们看一下连续下跌最厉害的10次，分别是什么情况：

```python
r.nsmallest(10, "cnr").sort_values("end", ascending=False)
```

<!-- BEGIN IPYNB STRIPOUT -->
<div>
<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>start</th>
      <th>end</th>
      <th>cnr</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>107</th>
      <td>2024-11-14</td>
      <td>2024-11-18</td>
      <td>-0.075433</td>
    </tr>
    <tr>
      <th>106</th>
      <td>2024-10-08</td>
      <td>2024-10-11</td>
      <td>-0.158511</td>
    </tr>
    <tr>
      <th>89</th>
      <td>2024-03-21</td>
      <td>2024-03-27</td>
      <td>-0.071948</td>
    </tr>
    <tr>
      <th>88</th>
      <td>2024-01-26</td>
      <td>2024-02-05</td>
      <td>-0.190592</td>
    </tr>
    <tr>
      <th>87</th>
      <td>2024-01-19</td>
      <td>2024-01-22</td>
      <td>-0.067846</td>
    </tr>
    <tr>
      <th>78</th>
      <td>2023-10-16</td>
      <td>2023-10-23</td>
      <td>-0.074109</td>
    </tr>
    <tr>
      <th>45</th>
      <td>2022-09-15</td>
      <td>2022-09-19</td>
      <td>-0.066983</td>
    </tr>
    <tr>
      <th>37</th>
      <td>2022-04-20</td>
      <td>2022-04-26</td>
      <td>-0.170335</td>
    </tr>
    <tr>
      <th>34</th>
      <td>2022-03-14</td>
      <td>2022-03-15</td>
      <td>-0.066983</td>
    </tr>
    <tr>
      <th>33</th>
      <td>2022-03-03</td>
      <td>2022-03-09</td>
      <td>-0.086669</td>
    </tr>
  </tbody>
</table>
</div>
<!-- END IPYNB STRIPOUT -->

我们看到2024年就发生了5次，分别是1月22日、2月5日、3月27日、10月11日和最近的11月18日。其中2月5日和10月11日这两次下跌幅度很大，后面的反弹也就很大，果然是风浪越大鱼越贵！

1月22日这次能出现在表中非常意外，毕竟这一次只下跌了两天。但是，随后也确实出现了一波持续3天的小反弹，涨幅超过6.1%，还比较可观。

## 底该怎么抄？

在11月8日收盘时，阴线连续下跌幅度为7.54%，盘中跌幅更大，所以，在盘中就出现了连续跌幅达到7.54%的情况。如果此时你决定抄底，成功的概率有多大？

有一个奇怪的pandas函数可以帮我们计算出来：

```python
decline_ratio = -0.075433
r.cnr.le(decline_ratio).mean()
```

它的奇妙之处在于，le用来找出小于等于decline_ratio的数据，把它们标记为true,其它的标记为false,然后mean用在bool变量上，会求出真值的比例，也就是我们要找的概率！

这个概率是4.63%。如果你在此时抄底，那么还有4.63%的概率，你需要忍受继续下跌，这就是发生在10月9日的情况。按照概率的提示，你大概会在10月9日的盘中杀进来，然后要忍受此后两天的继续下跌，这个跌幅跟你之前看到的差不多（也是7.5%左右）。

不过，好消息是，如果你在10月9日杀进来，你有在10月10日选择小幅盈利出局的权利。如果你在这一天选择出局，把筹码倒给了新入场的人，他们还可以再抗7.5%的跌幅！这就是为什么股谚说多头不死，下跌不止。

如果你不会编程，可以通过下面的表格速查抄底成功概率：

```python
data = []
for loss in np.linspace(-0.06, -0.076, 15):
    data.append((loss, 1- r.cnr.le(loss).mean()))

df = pd.DataFrame(data, columns=['最大亏损', '抄底胜率'])
df.style.format("{:.1%}")
```

<!-- BEGIN IPYNB STRIPOUT -->
<style type="text/css">
</style>
<table id="T_5e9d0">
  <thead>
    <tr>
      <th class="blank level0" >&nbsp;</th>
      <th id="T_5e9d0_level0_col0" class="col_heading level0 col0" >最大亏损</th>
      <th id="T_5e9d0_level0_col1" class="col_heading level0 col1" >抄底胜率</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th id="T_5e9d0_level0_row0" class="row_heading level0 row0" >0</th>
      <td id="T_5e9d0_row0_col0" class="data row0 col0" >-6.0%</td>
      <td id="T_5e9d0_row0_col1" class="data row0 col1" >87.0%</td>
    </tr>
    <tr>
      <th id="T_5e9d0_level0_row1" class="row_heading level0 row1" >1</th>
      <td id="T_5e9d0_row1_col0" class="data row1 col0" >-6.1%</td>
      <td id="T_5e9d0_row1_col1" class="data row1 col1" >87.0%</td>
    </tr>
    <tr>
      <th id="T_5e9d0_level0_row2" class="row_heading level0 row2" >2</th>
      <td id="T_5e9d0_row2_col0" class="data row2 col0" >-6.2%</td>
      <td id="T_5e9d0_row2_col1" class="data row2 col1" >87.0%</td>
    </tr>
    <tr>
      <th id="T_5e9d0_level0_row3" class="row_heading level0 row3" >3</th>
      <td id="T_5e9d0_row3_col0" class="data row3 col0" >-6.3%</td>
      <td id="T_5e9d0_row3_col1" class="data row3 col1" >87.0%</td>
    </tr>
    <tr>
      <th id="T_5e9d0_level0_row4" class="row_heading level0 row4" >4</th>
      <td id="T_5e9d0_row4_col0" class="data row4 col0" >-6.5%</td>
      <td id="T_5e9d0_row4_col1" class="data row4 col1" >88.0%</td>
    </tr>
    <tr>
      <th id="T_5e9d0_level0_row5" class="row_heading level0 row5" >5</th>
      <td id="T_5e9d0_row5_col0" class="data row5 col0" >-6.6%</td>
      <td id="T_5e9d0_row5_col1" class="data row5 col1" >89.8%</td>
    </tr>
    <tr>
      <th id="T_5e9d0_level0_row6" class="row_heading level0 row6" >6</th>
      <td id="T_5e9d0_row6_col0" class="data row6 col0" >-6.7%</td>
      <td id="T_5e9d0_row6_col1" class="data row6 col1" >90.7%</td>
    </tr>
    <tr>
      <th id="T_5e9d0_level0_row7" class="row_heading level0 row7" >7</th>
      <td id="T_5e9d0_row7_col0" class="data row7 col0" >-6.8%</td>
      <td id="T_5e9d0_row7_col1" class="data row7 col1" >93.5%</td>
    </tr>
    <tr>
      <th id="T_5e9d0_level0_row8" class="row_heading level0 row8" >8</th>
      <td id="T_5e9d0_row8_col0" class="data row8 col0" >-6.9%</td>
      <td id="T_5e9d0_row8_col1" class="data row8 col1" >93.5%</td>
    </tr>
    <tr>
      <th id="T_5e9d0_level0_row9" class="row_heading level0 row9" >9</th>
      <td id="T_5e9d0_row9_col0" class="data row9 col0" >-7.0%</td>
      <td id="T_5e9d0_row9_col1" class="data row9 col1" >93.5%</td>
    </tr>
    <tr>
      <th id="T_5e9d0_level0_row10" class="row_heading level0 row10" >10</th>
      <td id="T_5e9d0_row10_col0" class="data row10 col0" >-7.1%</td>
      <td id="T_5e9d0_row10_col1" class="data row10 col1" >93.5%</td>
    </tr>
    <tr>
      <th id="T_5e9d0_level0_row11" class="row_heading level0 row11" >11</th>
      <td id="T_5e9d0_row11_col0" class="data row11 col0" >-7.3%</td>
      <td id="T_5e9d0_row11_col1" class="data row11 col1" >94.4%</td>
    </tr>
    <tr>
      <th id="T_5e9d0_level0_row12" class="row_heading level0 row12" >12</th>
      <td id="T_5e9d0_row12_col0" class="data row12 col0" >-7.4%</td>
      <td id="T_5e9d0_row12_col1" class="data row12 col1" >94.4%</td>
    </tr>
    <tr>
      <th id="T_5e9d0_level0_row13" class="row_heading level0 row13" >13</th>
      <td id="T_5e9d0_row13_col0" class="data row13 col0" >-7.5%</td>
      <td id="T_5e9d0_row13_col1" class="data row13 col1" >95.4%</td>
    </tr>
    <tr>
      <th id="T_5e9d0_level0_row14" class="row_heading level0 row14" >14</th>
      <td id="T_5e9d0_row14_col0" class="data row14 col0" >-7.6%</td>
      <td id="T_5e9d0_row14_col1" class="data row14 col1" >96.3%</td>
    </tr>
  </tbody>
</table>
<!-- END IPYNB STRIPOUT -->

再往后胜率不变（因为数据量少），所以就没有列出了。在实际操作中，可以从-6%之后开始，使用马丁格尔交易法。

!!! tip
    我在这里没有使用最高价和最低价。这两个价格的稳定性不如收盘价与开盘价（即成交量少）。但你也可以试试。

你还可以计算出抄底之后的可能获利。你可以这样定义：从连续下跌之后出现的连续阳线涨幅，即为抄底之后的盈利。这个计算比较简单，你可以先过滤出连续跌幅大的，再通过循环来计算此后的平均收益。

百闻不如一练。我讨厌读那些看上去很美好，但无法验证的文章。很多时候，读这些文章只是在浪费时间，因为你都不知道哪句是真的，反正都无法验证。

同往常一样，这篇文章同样提供可运行的代码。你只要登录Quantide Research平台，就可以运行本文的代码验证我们作出的结论，然后选择下载本文代码，持续跟踪连续下跌引起的反弹信号。

<!-- BEGIN IPYNB STRIPOUT -->
加入星球，就可以拿到门票。在星球（及Quantide Research平台）里，我们已经发布了可alpha101因子库（可运行）、5个年化超过15%的因子，还有三角形整理检测等代码。未来将继续以更新公众号的频率，持续同步发布笔记相关代码。

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/hot/logo/zsxq.png'>
<span style='font-size:0.6rem'></span>
</div>
<!-- END IPYNB STRIPOUT -->

如果你不明白这里概率计算的原理，或者想为自己打下坚实的量化基础，可以考虑选修《量化24课》或者《因子分析与机器学习策略》。
