---
date: 2023-12-24
category: strategy
tags:
    - 因子
    - strategy
motto: 你当像鸟飞向你的山
title: 净新高占比因子
slug: net-high-net-low-factor
lunar: 冬月十二
---

个股的顶底强弱比较难以把握，它们的偶然性太强。董事长有可能跑路，个股也可能遇到突发利好（比如竞争对手仓库失火）。在个股的顶底处，**情绪占据主导地位，理性退避次席，技术指标出现钝化**，进入<red>现状不可描述，一切皆有可能</red>的状态。

但是，行业指数作为多个随机变量的叠加，就会出现一定的规律性（受A4系统性影响的偶然性我们先排除在外，毕竟也不是天天有A4）。这是因子分析和技术分析可以一展身手的地方。

<!--more-->

今天要介绍的净新高占比因子，可以用来捕捉行业强弱趋势以及反转，以此因子为基础，我们可以构建指数增强策略。

!!! tip TakeAway
    1. NHNL因子如何定义
    2. 锚定效应是本因子的基石
    3. 从因子到策略实现
    4. 如何使用XtQuant来获得数据


## 因子定义

净新高占比指标是指行业指数中，创年度新高与年度新低之差的个股数占全行业个股数的百分比：

$$
    (NHNL)\% = (count(HHV) - count(LLV))/N
$$


!!! tip
    从因子定义可以看出，它不是在[-1,1]区间均匀分布的。在机器学习中使用这一方案，会有轻微的梯度优化困难。虽然我们也可以仿RSI的思路，将其改造为：

    $$
        (NHNL)\% = (count(HHV) - count(LLV))/(count(HHV) + count(LLV))
    $$

    但这样做显然没有对应的逻辑存在。这是我们在做因子分析时要注意的点。不要刻舟求剑。

## 因子背后的逻辑

该因子的主要金融原理是<red>锚定效应</red>。行为金融学告诉我们，绝大多数投资者都有很强的锚定效应。投资者始终会以自己买入股票/基金时的价格（即<red>锚定成本</red>）为基准，视自己的账户是处于浮盈还是浮亏状态，来确定自己的操作。

创年度新高的个股，其股票持有者均为浮盈状态，即使其计划卖出，也希望等行情再上涨一段时间，因此其抛压相对较小；而创年度新低的个股，因其股票持有者均为浮亏状态，因此只要行情反弹便有投资者卖出，抛压反而较大。这样就出现了熊市不言底，牛市不言顶的规律，或者说新高之后还有新高，新低之后还有新低的股谚。

!!! tip
    何时这一趋势被反转？上述效应本质上是动量因子。在动量延续时，反转因子的力量也在积聚。此时可以关注反弹因子如月线RSI是否到了前高。如果月线RSI到了前高，再出现确认信号，则一个以季为单位的顶就很可能出现了。

基于这一原理，显然，净新高因子能较好地刻画行业指数的强弱。


## 信号构建

华福证券以中信一级行业指数为例，给出了以下参考指标：

$$
NHNL = \begin{cases} 
        x \geq 30\% \ 贪婪\\\
        20\% \leq x \lt 30\% \ 乐观\\\
        -20\% \lt x \lt 20\% \ 正常区间\\\
        -30\% \lt x \leq -20\% \ 悲观\\\
        x \leq -30\% \ 恐惧
\end{cases}
$$

为了防止一级行业指数个股数太少引发的过大波动，他们建议当一级行业指数上市超过 1 年的个股数小于 40 时，将阈值放宽为±30%/40%。

建议该指标这样使用（以单边做多为例）：

1. 当NHNL进行乐观区间时，开始建仓，此时是动量策略
2. 当NHNL进入贪婪区间时，注意可能到来的反转。首次从贪婪区间跌回乐观区间时，做空信号出现，下一交易日开盘卖出。
3. 当NHNL进行恐慌区间时，注意可能到来的反转。首次从恐慌区间涨回悲观区间时，下一交易日开盘买入。此时注意设置好止损位，反弹延续才能继续持有。

## 代码

代码的关键是要获取行业指数及成份股行情，因为计算一年内的新高和新低非常容易。

我们以XtQuant为例进行演示。XtQuant是迅投研发的行情数据及实盘接口，开通量化权限的话，可以免费获得行情数据，因此它是又一个优秀的免费数据源。我们在第24课有详细介绍它的使用。



```python
sectors = set()

for item in get_sector_list():
    for i in range(6, 1, -1):
        key = item[:i]

        if key.startswith("SW1"):
            sectors.add(key)
            break
print(sectors)

# 显示：
'SW1煤炭', 'SW1交通运', 'SW1综合', 'SW1通信' ...
```

我们大约拿到40个申万一级板块名。接下来我们就需要获取板块内的每支成分股的证券代码:

```python
xt.get_stock_list_in_sector("SW1煤炭")

# 显示：

['600121.SH',
 '600123.SH',
 '600157.SH',
 '600188.SH',
 '600348.SH',
 '600395.SH',
 ...
]
```

获取个股行情数据的方法在最近的笔记中有详细介绍，这里不再重复。

!!! tip
    掌握获取证券列表、再遍历之以获得行情数据，是一个基本方法。也是我们掌握一个数据源时，必须首先掌握的API。这是我们第一课起，就跟大家介绍过的。


更多的代码不便一一演示。我们有示例notebook可以提供。最终我们能得到这样一个效果：

![](https://images.jieyu.ai/images/2023/12/nhnl.png)


## Quiz

请说说为什么净新高占比因子不是在[-1,1]区间内均匀分布的。为了得出结论，你用了几秒？

<claimer>根据华福证券-市场情绪指标专题（五），进行了提炼和改写，特此致谢！</claimer>
