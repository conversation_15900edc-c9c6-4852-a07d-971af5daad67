---
title: 终极猜想！底蓓离的成因分析
slug: how-top-divergency-formed
date: 2024-04-25
category: strategy
motto: 幸福的秘密是自由，自由的秘密是勇敢 - 修昔底德
img: https://images.jieyu.ai/images/2024/04/divergency-cover.jpg
lineNumbers: true
tags: 
    - strategy
    - 技术指标
    - 顶背离
---

这几天圈内都在传底蓓离什么的。作为严肃的量化自媒体，我们就不跟着吃这波瓜了。不过，我一直很关注技术指标的顶背离和底背离，一直在追问它的成因如何，以及如何预测。

底蓓离把我目光再次吸引到这个领域来，于是突然有了一个猜想。虽然我还没来得及完全证实它，但这个猜想，值得你锁定我的频道。

这个猜想是：**RSI的日线顶背离，是因为周线还有上升空间。当周线RSI也达到前期高点时，就会触发回调。此时日线虽然创新高，但高位筹码不稳，回调增多，导致RSI下降。**

---

## 背离的定义
Investopedia对技术指标的#顶背离定义为：

!!! quote
    背离是指资产价格的走势与技术指标（例如震荡指标）相反，或者与其他数据相反。背离警告当前价格趋势可能正在减弱，并且在某些情况下可能导致价格改变方向。<br><br>存在正背离和负背离。正背离表明资产价格可能上涨。负背离表明该资产可能会下跌。

在国内，我们一般使用顶背离和底背离这两个术语。最早大V中较多讨论这两个术语的，可能是**炒股养家**或者**缠中说禅**，说明这一现象在实战中是比较受到关注的。

下图中的右图展示了一个日线的顶背离。左图则是对应的周线情况。

附图指标我使用的是RSI，它是一个非常好的震荡指标，深刻反映了当前市场的赚钱效应和强弱力量对比。

在图中，程序自动标注出来了k线的每一个峰和谷。这些标注使用了我们自己的自适应参数算法，在绝大多数时间、无论周期是哪个级别，都能工作得很好。不过这一次，在周线级别上，没能触发它标注出3月22日那一周的高点。

---

![](https://images.jieyu.ai/images/2024/04/top-rsi-divergency-day.jpg)


从右图中我们注意到，日线上存在2月27日和3月18日两个峰，后一个峰的价格高于前一峰，但后一个峰的RSI略小于前一峰的RSI（前高78.3，后高77.7），形成了顶背离。

我们在课程中详细介绍过一个**独创但更准确**地理论，在运用RSI时，不是低于30就会反弹，高于70就会回调，而是要看它跟前一个峰（或者谷）的RSI相比较，如果上涨时，RSI高于前一个峰对应的RSI，则有可能回调。

但这个结论也有自身的问题：一是在图中，日线在2月27日之前，就已经突破了2024年1月25日下降通道中高点RSI，为何一直到2月27日才回调？我们在课程中已经解决了这个问题。

---

二是为何在2月27日之后，股价还能一直上涨，直到3月18日出现顶背离？这个顶背离问题，我也一直没有思路，但自己着手写了好几个检测顶背离的例程。

## 猜想和验证

现在我们给出回答第二个问题的一个猜想，即尽管2月27日的日线RSI已经达到高位，但周线RSI仍在低位，它没有表示**反对和阻止上涨的意思**，因此日线短暂调整后，在**其它因素趋动下**继续上涨，直到3月22日那一周，盘中突破前高60.5，才引发了一个周线级别的大回调。

!!!tip
    从3月21日起，到3月27日止，中证1000连续回调超过6.94%，从而引发反弹，这是另外一个故事和机会。我们在前面的一篇文章中介绍过，统计数据给出3月27日收盘后，反弹概率超过91.4%的结论，这种情况，应该坚决**抄底**。


RSI是反转指标。它只能以一定概率给出会不会反转，但你不应该指望它给出趋势延续的预测。那是趋势类指标的任务。最重要的趋势类指标，莫过于判断均线的斜率，如果线性回归误差在可接受范围以内的话。在回归误差较大而失效的情况下，课程中也给出了一个非常鲁棒的方法。

我们还是拿数据验证一下：

---

```python
# 日线2月27日、3月18日顶背离，前者RSI 78.3,后者RSI 77.7
dclose = dbars["close"].astype(np.float64)
drsi = ta.RSI(dclose, 6)

for dt in (datetime.date(2024, 2, 27), 
           datetime.date(2024, 3, 18)):
    mask = dbars["frame"] == np.datetime64(dt)
    i = np.flatnonzero(mask)[0]
    print(dt, dclose[i], drsi[i])

# --- output ---
# 2024-02-27 5394.03 78.3
# 2024-03-18 5648.01 77.7
```

从数据看出，尽管日线价格在上涨，但RSI却下降了，构成了日线顶背离。

接着看周线：

```python
# 周线rsi前高出现于2023年11月17日，数值为60.5
wclose = wbars["close"].astype(np.float64).copy()

nov_17 = np.datetime64(datetime.date(2023,11, 17))
i = np.flatnonzero(wbars["frame"] == nov_17)[0]

rsi = np.round(ta.RSI(wclose, 6), 1)
rsi[i]
```

---

前高为60.5。这里有一个**技巧**，如果没看明白，可以给我留言：

```python
# 3月22日，周线rsi数值盘中突破 61.9，高于前高60.5，触发回调

mar_22 = np.datetime64(datetime.date(2024, 3, 22))
i = np.flatnonzero(wbars["frame"] == mar_22)[0]

wclose[i] = wbars["high"][i]

rsi = ta.RSI(wclose.astype(np.float64), 6)
rsi[i]
```

rsi后高为61.9，因此触发回调，并且这个阻力得到了确认。

!!! tip
    注意这里rsi没有到70就回调了。所以，你看到的所有关于RSI的教科书，都该改改了。是不存在 one size fits all。不同的标的、不同的周期以及不同的阶段，应该有不同的参数。这些参数应该可以通过量化程序（或者机器学习）计算出自适应的值。

## 引申结论及思考

在这个市场上，存在各种不同操作频率（这是FFT和wavelet应该有用的原因）的资金。

---

对高频量化，他们看的是tick级数据，可能持有几分钟就会调仓；散户和量化多在日线和周线频率上操作，持有数天就调仓；长线资金以季度为单位。越是长线的资金，资金量越大，调仓时对走向的影响越强。

现在，你应该已经猜到了，有一**部分资金会在日线RSI高点时撤出；大量的资金会在周线的RSI高点撤出；而更大量的资金会在月线的RSI高点撤出。**

但我猜**没有资金会根据季线的RSI高点撤出**。许多真理，都不能线性外推。

我这么说的原因是，**季线资金会按照基本面来进行操作，而不是技术面**。有的同学会拿基本面因子与技术面因子揉在一起，无论是多因子回归，还是机器学习，这都是不对的。它们只会相互打架、抵消。

所以，从现在起，你应该给你的股票上个闹钟，计算出它的日线、周线和月线RSI前期高点，然后实时监控这些指标。

一旦三者都达到高点，这轮行情就结束了。如果月线达到高点，日线和周线不在高位，但发生过顶背离，那么，势必在高位产生过滞胀（量在堆积，但上涨幅度不大），此时主力的筹码很多已经被交换掉了。

此时不走很危险。接下来的回调时间，可能以月计。老胡说，“我不割肉，你怎么割我？”但胡锡进终究是等不到那一天。
