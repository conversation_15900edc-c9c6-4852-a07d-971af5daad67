{"cells": [{"cell_type": "markdown", "id": "6ad9a9be", "metadata": {}, "source": ["---\n", "title: \"WorldQuant? Word Count!\"\n", "date: 2024-12-04\n", "category: factor&strategy\n", "slug: word-count-factor\n", "motto: \n", "img: https://images.jieyu.ai/images/hot/my-company.jpg\n", "stamp_width: 60%\n", "stamp_height: 60%\n", "tags: [factor,strategy]\n", "---\n", "\n", "\n", "如果你去商场逛，你会发现，销量最好的店和最好的商品总是占据人气中心。对股票来说也是一样，被新闻和社交媒体频频提起的个股，往往更容易获得更大的成交量。\n", "\n", "如果一支个股获得了人气，那它的成交量一定很大，两者之间有强相关关系。但是，成交量落后于人气指标。当一支个股成交量开始放量时，有可能已经追不上了（涨停）。如果我们能提前发现人气指标，就有可能获得提前介入的时机。\n", "\n", "那么具体怎么操作（建模）呢？\n", "\n", "我们首先讲解一点信息检索的知识，然后介绍如何运用统计学和信息检索的知识，来把上述问题模型化。\n", "\n", "## TF-IDF\n", "\n", "TF 是 Term Frequency 的意思。它是对一篇文档中，某个词语共出现了多少次的一个统计。IDF 则是 Inverse Document Frequency 的意思，大致来说，如果一个词在越多的文档中出现，那么，它携带的信息量就越少。\n", "\n", "比如，我们几乎每句话都会用到『的、地、得』，这样的词几乎在每个语境（文档）中都会出现，因此就不携带信息量。新闻业常讲的一句话，狗咬人不是新闻，人咬狗才是新闻，本质上也是前者太常出现，所以就不携带信息量了。\n", "\n", "最早发明 TF-IDF 的人应该是康奈尔大学的杰拉德·索尔顿（康奈尔大学的计算机很强）和英国的计算机科学家卡伦·琼斯。到今天，美国计算机协会（ACM）还会每三年颁发一次杰拉德·索尔顿奖，以表彰信息检索领域的突出贡献者。\n", "\n", "TF-IDF 的构建过程如下：\n", "\n", "假如我们有 3 篇文档，依次是：\n", "\n", "1. 苹果 橙子 香蕉\n", "2. 苹果 香蕉 香蕉\n", "3. 橙子 香蕉 梨\n", "\n", "看上去不像文档，不过这确实是文档的最简化的形式--就是一堆词的组合（在 TF-IDF 时代，还无法处理词的顺序）。在第 1 篇文档中，橙子、香蕉和苹果词各出现 1 次，每个词的 TF 都记为 1，我们得到："]}, {"cell_type": "markdown", "id": "2e7fdb28", "metadata": {}, "source": ["```\n", "TF_1 = {\n", "    '苹果': 1/3,\n", "    '香蕉': 1/3,\n", "    '橙子': 1/3,\n", "}\n", "```\n"]}, {"cell_type": "markdown", "id": "a154721c", "metadata": {}, "source": ["在第二篇文档中，苹果出现 1 次，香蕉出现 2 次，橙子和梨都没有出现。于是得到："]}, {"cell_type": "markdown", "id": "43d8c57a", "metadata": {}, "source": ["```\n", "TF_2 = {\n", "    '苹果': 1/3,\n", "    '香蕉': 2/3,\n", "}\n", "```\n"]}, {"cell_type": "markdown", "id": "9eadc1d6", "metadata": {}, "source": ["第三篇文档中，TF 的计算依次类推。\n", "\n", "IDF 实际上是每个词的信息权重，它的计算按以下公式进行：\n", "\n", "$$\n", "\\text{IDF}(t) = \\log \\left( \\frac{N + 1}{1 + \\text{DF}(t)} \\right) + 1\n", "$$\n", "\n", "1. DF：每个词在多少篇文档中出现了。\n", "1. N 是文档总数，在这里共有 3 篇文档，所以$N=3$\n", "2. 公式中，分子与分母都额外加 1，一方面是为了避免 0 作为分母，因为$DF(t)$总是正的，另外也是一种 L1 正则化。这是 sklearn 中的做法。\n", "\n", "这样我们可以算出所有词的 IDF：\n", "\n", "$$\n", "苹果 = 橙子 = \\log \\left( \\frac{4}{2+1} \\right) + 1 = 1.2876\n", "$$\n", "\n", "$$\n", "梨 = \\log \\left( \\frac{4}{1+1} \\right) + 1 = 1.6931\n", "$$\n", "\n", "因为梨这个词很少出现，所以，一旦它出现，就是人咬狗事件，所以它的信息权重就大。而香蕉则是一个烂大街的词，在 3 篇文档中都有出现过，所有我们惩罚它，让它的信息权重为负：\n", "\n", "$$\n", "香蕉 = \\log \\left( \\frac{4}{3+1} \\right) + 1 = 1\n", "$$\n", "\n", "最后，我们得到每个词的 TF-IDF：\n", "\n", "$$\n", "TF-IDF=TF\\times{IDF}\n", "$$\n", "\n", "这样我们以所有可能的词为列，每篇文档中，出现的词的 TF-IDF 为值，就得到如下稀疏矩阵：\n", "\n", "|        | 苹果     | 香蕉    | 橙子     | 梨       |\n", "| ------ | -------- | ------- | -------- | -------- |\n", "| 文档 1 | 1.2876/3 | 1/3     | 1.2876/3 | 0        |\n", "| 文档 2 | 1.2876/3 | 1/3 * 2 | 0        | 0        |\n", "| 文档 3 | 0        | 1/3     | 1.2876/3 | 1.6931/3 |\n", "\n", "在 sklearn 中，最后还会对每个词的 TF-IDF 进行 L2 归一化。这里就不手动计算了。\n", "\n", "我们把每一行称为文档的向量，它代表了文档的特征。如果两篇文档的向量完全相同，那么它们很可能实际上是同一篇文章（近似。因为，即使完全使用同样的词和词频，也可以写出不同的文章。\n", "\n", "比如，『可以清心也』这几个字，可以排列成『以清心也可』，也可以排列成『心也可以清』，或者『清心也可以』，都是语句通顺的文章。\n", "\n", "在实际应用中，我们可以使用 sklearn 的 TfidfVectorizer 来实现 TF-IDF 的计算："]}, {"cell_type": "code", "execution_count": null, "id": "ee61332e", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "def jieba_tokenizer(text):\n", "    return list(jieba.cut(text))\n", "\n", "d1 = \"苹果橙子香蕉\"\n", "d2 = \"苹果香蕉香蕉\"\n", "d3 = \"橙子香蕉梨\"\n", "\n", "vectorizer = TfidfVectorizer(tokenizer = jieba_tokenizer)\n", "matrix = vectorizer.fit_transform([d1, d2, d3])\n", "\n", "df = pd.DataFrame(matrix.toarray(), columns = vectorizer.get_feature_names_out())\n", "df"]}, {"cell_type": "markdown", "id": "6dd94f19", "metadata": {}, "source": ["<!-- B<PERSON>IN IPYNB STRIPOUT -->\n", "<div>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>梨</th>\n", "      <th>橙子</th>\n", "      <th>苹果</th>\n", "      <th>香蕉</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.000000</td>\n", "      <td>0.619805</td>\n", "      <td>0.619805</td>\n", "      <td>0.481334</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.541343</td>\n", "      <td>0.840802</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.720333</td>\n", "      <td>0.547832</td>\n", "      <td>0.000000</td>\n", "      <td>0.425441</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "<!-- END IPYNB STRIPOUT -->\n", "\n", "结果与我们手工计算的有所不同，是因为我们在手工计算时，省略了计算量相对较大的L2归一化。\n", "\n", "从上面的例子可以看出，TF-IDF 是用来提取文章的特征向量的。有了这个特征向量，就可以通过计算余弦距离，来比较两篇文档是否相似。这可以用在论文查重、信息检索、比较文学和像今日头条这样的图文推荐应用上。\n", "\n", "比如，要证明曹雪芹只写了《红楼梦》的前87回，就可以把前87回和后面的文本分别计算TF-IDF，然后计算余弦距离，此时就能看出差别了。\n", "\n", "又比如，如果用TF-IDF分析琼瑶的作品，你会发现，如果去掉一些最重要的名词之后，许多她的文章的相似度会比较高。下面是对《还珠格格》分析后，得到的最重要的词汇及其TF-IDF："]}, {"cell_type": "markdown", "id": "f42e3d25", "metadata": {}, "source": ["```\n", "紫薇: 0.0876\n", "皇帝: 0.0754\n", "尔康: 0.0692\n", "皇后: 0.0621\n", "五阿哥: 0.0589\n", "容嬷嬷: 0.0573\n", "小燕子: 0.0556\n", "四阿哥: 0.0548\n", "福晋: 0.0532\n", "金锁: 0.0519\n", "```\n"]}, {"cell_type": "markdown", "id": "6199c4f4", "metadata": {}, "source": ["跟你的印象是否一致？但是，TF-IDF的分析方法，在量化交易中有何作用，目前还没有例证。\n", "\n", "讲到这里，关于 TF-IDF 在量化交易中的作用，基本上就讲完了。因为，接下来，我们要跳出 TF-IDF 的窠臼，自己构建因子了！\n", "\n", "## word-count 因子\n", "\n", "根据 TF-IDF 的思想，这里提出一个 word-count 因子。它的构建方法是，通过 tushare 获取每天的新闻信息，用 jieba 进行分词，统计每天上市公司名称出现的次数。这是 TF 部分。\n", "\n", "在 IDF 构建部分，我们做法与经典方法不一样，但更简单、更适合量化场景。这个方法就是，我们取每个词 TF 的移动平均做为 IDF。**这个IDF就构成了每个词的基准噪声**，一旦某天某个词的频率显著大于基准噪声，就说明该公司上新闻了！\n", "\n", "最后，我们把当天某个词的出现频率除以它的移动平均的读数作为因子。显然，这个数值越大，它携带的信息量也越大，表明该词（也就是该公司）最近在新闻上被频频提起。\n", "\n", "\n", "\n", "## 获取新闻文本数据\n", "\n", "我们可以通过 tushare 的 news 接口来获取新闻。\n", "这个方法是："]}, {"cell_type": "markdown", "id": "19f13697", "metadata": {}, "source": ["```text\n", "news = pro.news(src='sina', \n", "                date=start,\n", "                end_date=end,\n", ")\n", "```\n"]}, {"cell_type": "markdown", "id": "4bb51cac", "metadata": {}, "source": ["我们把获取的新闻数据先保存到本地，以免后面还可能进行其它挖掘：\n", "\n", "!!! attention\n", "    即使是高级账号，tushare对每天能调用的新闻条数也是有限制的。所以，请在您本地安装tushare, 申请高级账号来运行以下代码，不要在Quantide Reseach环境中运行！！"]}, {"cell_type": "markdown", "id": "bba99515", "metadata": {}, "source": ["````markdown\n", "```python\n", "def retry_fetch(start, end, offset):\n", "    i = 1\n", "    while True:\n", "        try:\n", "            df =pro.news(**{\n", "                \"start_date\": start,\n", "                \"end_date\": end,\n", "                \"src\": \"sina\",\n", "                \"limit\": 1000,\n", "                \"offset\": offset\n", "            }, fields=[\n", "                \"datetime\",\n", "                \"content\",\n", "                \"title\",\n", "                \"channels\",\n", "                \"score\"])\n", "            return df\n", "        except Exception as e:\n", "            print(f\"fetch_new failed, retry after {i} hours\")\n", "            time.sleep(i * 3600)\n", "            i = min(i*2, 10)\n", "\n", "def fetch_news(start, end):\n", "    for i in range(1000):\n", "        offset = i * 1000\n", "        df = retry_fetch(start, end, offset)\n", "\n", "        df_start = arrow.get(df.iloc[0][\"datetime\"]).format(\"YYYYMMDD_HHmmss\")\n", "        df_end = arrow.get(df.iloc[-1][\"datetime\"]).format(\"YYYYMMDD_HHmmss\")\n", "        df.to_csv(os.path.join(data_home, f\"{df_start}_{df_end}.news.csv\"))\n", "        if len(df) == 0:\n", "            break\n", "\n", "        # tushare 对新闻接口调用次数及单次返回的新闻条数都有限制\n", "        time.sleep(3.5 * 60)\n", "```\n", "````\n"]}, {"cell_type": "markdown", "id": "7eec816f", "metadata": {}, "source": ["在统计新闻中上市公司出现的词频时，我们需要先给 jieba 增加自定义词典，以免出现分词错误。比如，如果不添加关键词『万科 A』，那么它一定会被 jieba 分解为万科和 A 两个词。\n", "\n", "增加自定义词典的代码如下："]}, {"cell_type": "code", "execution_count": 1, "id": "42de1b76", "metadata": {}, "outputs": [], "source": ["def init():\n", "    # get_stock_list 是自定义的函数，用于获取股票列表。在 quantide research 环境可用\n", "    stocks = get_stock_list(datetime.date(2024,11,1), code_only=False)\n", "    stocks = set(stocks.name)\n", "    for name in stocks:\n", "        jieba.add_word(name)\n", "\n", "    return stocks"]}, {"cell_type": "markdown", "id": "a23a9b8f", "metadata": {}, "source": ["这里得到的证券列表，后面还要使用，所以作为函数返回值。\n", "\n", "接下来，就是统计词频了："]}, {"cell_type": "code", "execution_count": 2, "id": "dac56eea", "metadata": {}, "outputs": [], "source": ["def count_words(news, stocks)->pd.DataFrame:\n", "    data = []\n", "    for dt, content, _ in news.to_records(index=False):\n", "        words = jieba.cut(content)\n", "        word_counts = Counter(words)\n", "        for word, count in word_counts.items():\n", "            if word in stocks:\n", "                data.append((dt, word, count))\n", "    df = pd.DataFrame(data, columns=['date', 'word', 'count'])\n", "    df[\"date\"] = pd.to_datetime(df['date'])\n", "    df.set_index('date', inplace=True)\n", "\n", "    return df"]}, {"cell_type": "markdown", "id": "ffc79f18", "metadata": {}, "source": ["tushare 返回的数据共有三列，其中 date, content 是我们关心的字段。公司名词频就从 content 中提取。\n", "\n", "然后我们对所有已下载的新闻进行分析，统计每日词频和移动均值："]}, {"cell_type": "code", "execution_count": 4, "id": "203ed0f3", "metadata": {}, "outputs": [], "source": ["def count_words_in_files(stocks, ma_groups=None):\n", "    ma_groups = ma_groups or [30, 60, 250]\n", "    # 获取指定日期范围内的数据\n", "    results = []\n", "\n", "    files = glob.glob(os.path.join(data_home, \"*.news.csv\"))\n", "    for file in files:\n", "        news = pd.read_csv(file, index_col=0)\n", "\n", "        df = count_words(news, stocks)\n", "        results.append(df)\n", "\n", "    df = pd.concat(results)\n", "    df = df.sort_index()\n", "    df = df.groupby(\"word\").resample('D').sum()\n", "    df.drop(\"word\", axis=1, inplace=True)\n", "    df = df.swaplevel()\n", "    unstacked = df.unstack(level=\"word\").fillna(0)\n", "    for win in ma_groups:\n", "        df[f\"ma_{win}\"] = unstacked.rolling(window=win).mean().stack()\n", "    \n", "    return df"]}, {"cell_type": "markdown", "id": "bed785fe", "metadata": {}, "source": ["最后，完整的代码如下："]}, {"cell_type": "code", "execution_count": 7, "id": "bd962876", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Dumping model to file cache /var/folders/b5/73vzvtdn4pn_8wt6rpd2tb_w0000gn/T/jieba.cache\n", "Loading model cost 0.423 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"ename": "ValueError", "evalue": "No objects to concatenate", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[7]\u001b[39m\u001b[32m, line 60\u001b[39m\n\u001b[32m     57\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m df.sort_index(), unstacked.sort_index()\n\u001b[32m     59\u001b[39m stocks = init()\n\u001b[32m---> \u001b[39m\u001b[32m60\u001b[39m factor, raw = \u001b[43mcount_words_in_files\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstocks\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     61\u001b[39m factor.tail(\u001b[32m20\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[7]\u001b[39m\u001b[32m, line 48\u001b[39m, in \u001b[36mcount_words_in_files\u001b[39m\u001b[34m(stocks, ma_groups)\u001b[39m\n\u001b[32m     45\u001b[39m     df = count_words(news, stocks)\n\u001b[32m     46\u001b[39m     results.append(df)\n\u001b[32m---> \u001b[39m\u001b[32m48\u001b[39m df = \u001b[43mpd\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconcat\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresults\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     49\u001b[39m df = df.sort_index()\n\u001b[32m     50\u001b[39m df = df.groupby(\u001b[33m\"\u001b[39m\u001b[33mword\u001b[39m\u001b[33m\"\u001b[39m).resample(\u001b[33m'\u001b[39m\u001b[33mD\u001b[39m\u001b[33m'\u001b[39m).sum()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/reshape/concat.py:382\u001b[39m, in \u001b[36mconcat\u001b[39m\u001b[34m(objs, axis, join, ignore_index, keys, levels, names, verify_integrity, sort, copy)\u001b[39m\n\u001b[32m    379\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m copy \u001b[38;5;129;01mand\u001b[39;00m using_copy_on_write():\n\u001b[32m    380\u001b[39m     copy = \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m382\u001b[39m op = \u001b[43m_Concatenator\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    383\u001b[39m \u001b[43m    \u001b[49m\u001b[43mobjs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    384\u001b[39m \u001b[43m    \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m=\u001b[49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    385\u001b[39m \u001b[43m    \u001b[49m\u001b[43mignore_index\u001b[49m\u001b[43m=\u001b[49m\u001b[43mignore_index\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    386\u001b[39m \u001b[43m    \u001b[49m\u001b[43mjoin\u001b[49m\u001b[43m=\u001b[49m\u001b[43mjoin\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    387\u001b[39m \u001b[43m    \u001b[49m\u001b[43mkeys\u001b[49m\u001b[43m=\u001b[49m\u001b[43mkeys\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    388\u001b[39m \u001b[43m    \u001b[49m\u001b[43mlevels\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlevels\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    389\u001b[39m \u001b[43m    \u001b[49m\u001b[43mnames\u001b[49m\u001b[43m=\u001b[49m\u001b[43mnames\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    390\u001b[39m \u001b[43m    \u001b[49m\u001b[43mverify_integrity\u001b[49m\u001b[43m=\u001b[49m\u001b[43mverify_integrity\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    391\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcopy\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcopy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    392\u001b[39m \u001b[43m    \u001b[49m\u001b[43msort\u001b[49m\u001b[43m=\u001b[49m\u001b[43msort\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    393\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    395\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m op.get_result()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/reshape/concat.py:445\u001b[39m, in \u001b[36m_Concatenator.__init__\u001b[39m\u001b[34m(self, objs, axis, join, keys, levels, names, ignore_index, verify_integrity, copy, sort)\u001b[39m\n\u001b[32m    442\u001b[39m \u001b[38;5;28mself\u001b[39m.verify_integrity = verify_integrity\n\u001b[32m    443\u001b[39m \u001b[38;5;28mself\u001b[39m.copy = copy\n\u001b[32m--> \u001b[39m\u001b[32m445\u001b[39m objs, keys = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_clean_keys_and_objs\u001b[49m\u001b[43m(\u001b[49m\u001b[43mobjs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkeys\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    447\u001b[39m \u001b[38;5;66;03m# figure out what our result ndim is going to be\u001b[39;00m\n\u001b[32m    448\u001b[39m ndims = \u001b[38;5;28mself\u001b[39m._get_ndims(objs)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/reshape/concat.py:507\u001b[39m, in \u001b[36m_Concatenator._clean_keys_and_objs\u001b[39m\u001b[34m(self, objs, keys)\u001b[39m\n\u001b[32m    504\u001b[39m     objs_list = \u001b[38;5;28mlist\u001b[39m(objs)\n\u001b[32m    506\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(objs_list) == \u001b[32m0\u001b[39m:\n\u001b[32m--> \u001b[39m\u001b[32m507\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>r\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mNo objects to concatenate\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    509\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m keys \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    510\u001b[39m     objs_list = \u001b[38;5;28mlist\u001b[39m(com.not_none(*objs_list))\n", "\u001b[31mVal<PERSON><PERSON><PERSON>r\u001b[39m: No objects to concatenate"]}], "source": ["import os\n", "import glob\n", "import jieba\n", "from collections import Counter\n", "import time\n", "\n", "data_home = \"/data/news\"\n", "def init():\n", "    stocks = get_stock_list(datetime.date(2024,12,2), code_only=False)\n", "    stocks = set(stocks.name)\n", "    for name in stocks:\n", "        jieba.add_word(name)\n", "\n", "    return stocks\n", "\n", "def count_words(news, stocks)->pd.DataFrame:\n", "    data = []\n", "    for dt, content, *_ in news.to_records(index=False):\n", "        if content is None or not isinstance(content, str):\n", "            continue\n", "\n", "        try:\n", "            words = jieba.cut(content)\n", "            word_counts = Counter(words)\n", "            for word, count in word_counts.items():\n", "                if word in stocks:\n", "                    data.append((dt, word, count))\n", "        except Exception as e:\n", "            print(dt, content)\n", "    df = pd.DataFrame(data, columns=['date', 'word', 'count'])\n", "    df[\"date\"] = pd.to_datetime(df['date'])\n", "    df.set_index('date', inplace=True)\n", "\n", "    return df\n", "\n", "def count_words_in_files(stocks, ma_groups=None):\n", "    ma_groups = ma_groups or [30, 60, 250]\n", "    # 获取指定日期范围内的数据\n", "    results = []\n", "\n", "    files = glob.glob(os.path.join(data_home, \"*.news.csv\"))\n", "    for file in files:\n", "        news = pd.read_csv(file, index_col=0)\n", "\n", "        df = count_words(news, stocks)\n", "        results.append(df)\n", "\n", "    df = pd.concat(results)\n", "    df = df.sort_index()\n", "    df = df.groupby(\"word\").resample('D').sum()\n", "    df.drop(\"word\", axis=1, inplace=True)\n", "    df = df.swaplevel()\n", "    unstacked = df.unstack(level=\"word\").fillna(0)\n", "    for win in ma_groups:\n", "        df[f\"ma_{win}\"] = unstacked.rolling(window=win).mean().stack()\n", "    \n", "    return df.sort_index(), unstacked.sort_index()\n", "\n", "stocks = init()\n", "factor, raw = count_words_in_files(stocks)\n", "factor.tail(20)"]}, {"cell_type": "markdown", "id": "4c72454d", "metadata": {}, "source": ["这里计算的仍然是原始数据。最终因子化要通过 factor[\"count\"]/factor[\"ma_30\"] 来计算并执行 rank，这里的 ma_30 可以替换为 ma_60, ma_250 等。\n", "\n", "跟以往的文章不同，这一次我们没有直接得到好的结果。我们的研究其实多数时候都是寂寞的等待，然后挑一些成功的例子发表而已。毕竟，发表不成功的例子，估计意义不大（很少人看）。\n", "\n", "但是这一篇有所不同。我们没有得到结果，主要是因为数据还在积累中。这篇文章从草稿到现在，已经半个月了，但是我仍然没能获取到 1 年以上的新闻数据，所以，无法准确得到每家公司的**『基底噪声』**，从而也就无法得到每家公司最新的信息熵。但要获得 1 年以上的数据，大概还要一个月左右的时间。所以，先把已经获得的成果发出来。\n", "\n", "尽管没有直接的结果，但是我们的研究演示了对文本数据如何建模的一个方法，也演示了如何使用TF-IDF，并且在因子化方向也比较有新意，希望能对读者有所启发。\n", "\n", "<!-- B<PERSON>IN IPYNB STRIPOUT -->\n", "我们已经抓取的新闻数据截止到今年的 8 月 20 日，每天都会往前追赶大约 10 天左右。这些数据和上述代码，可以在我们的 quantide research 平台上获取和运行。加入星球，即可获得平台账号。\n", "\n", "<div style='width:75%;text-align:center;margin: 0 auto 1rem'>\n", "<img src='https://images.jieyu.ai/images/hot/logo/zsxq.png'>\n", "<span style='font-size:0.6rem'></span>\n", "</div>\n", "<!-- END IPYNB STRIPOUT -->"]}], "metadata": {"kernelspec": {"display_name": "zillionare", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}