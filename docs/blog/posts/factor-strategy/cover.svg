<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="1200" height="630" fill="url(#bg-gradient)"/>
<defs>
<linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
<stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
<stop offset="100%" style="stop-color:#16213e;stop-opacity:1" />
</linearGradient>
<filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
<feGaussianBlur stdDeviation="15" result="coloredBlur"/>
<feMerge>
<feMergeNode in="coloredBlur"/>
<feMergeNode in="SourceGraphic"/>
</feMerge>
</filter>
</defs>

<!-- Abstract background shapes -->
<path d="M-100 100 Q 150 200 400 50 L 500 650 L -100 650 Z" fill="rgba(22, 33, 62, 0.5)"/>
<path d="M1300 530 Q 1050 430 800 580 L 700 -50 L 1300 -50 Z" fill="rgba(22, 33, 62, 0.5)"/>

<!-- Central Brain/AI element -->
<g transform="translate(600, 315) scale(1.2)">
    <path d="M0-120C-66-120-120-66-120 0s54 120 120 120 120-54 120-0-54-120-120-120z M0-100c55 0 100 45 100 100s-45 100-100 100-100-45-100-100 45-100 100-100z" fill="url(#brain-gradient)"/>
    <defs>
        <radialGradient id="brain-gradient">
            <stop offset="0%" stop-color="#0F9B8E"/>
            <stop offset="100%" stop-color="#3C4CAD"/>
        </radialGradient>
    </defs>

    <!-- Neural network nodes -->
    <circle cx="0" cy="0" r="15" fill="#E94560" filter="url(#glow)"/>
    <circle cx="-60" cy="-60" r="10" fill="#F0A500"/>
    <circle cx="60" cy="-60" r="10" fill="#F0A500"/>
    <circle cx="-70" cy="30" r="12" fill="#F0A500"/>
    <circle cx="70" cy="30" r="12" fill="#F0A500"/>
    <circle cx="0" cy="80" r="10" fill="#F0A500"/>

    <!-- Connections -->
    <line x1="0" y1="0" x2="-60" y2="-60" stroke="rgba(240, 165, 0, 0.5)" stroke-width="2"/>
    <line x1="0" y1="0" x2="60" y2="-60" stroke="rgba(240, 165, 0, 0.5)" stroke-width="2"/>
    <line x1="0" y1="0" x2="-70" y2="30" stroke="rgba(240, 165, 0, 0.5)" stroke-width="2"/>
    <line x1="0" y1="0" x2="70" y2="30" stroke="rgba(240, 165, 0, 0.5)" stroke-width="2"/>
    <line x1="0" y1="0" x2="0" y2="80" stroke="rgba(240, 165, 0, 0.5)" stroke-width="2"/>
    <line x1="-60" y1="-60" x2="-70" y2="30" stroke="rgba(240, 165, 0, 0.3)" stroke-width="1.5"/>
    <line x1="60" y1="-60" x2="70" y2="30" stroke="rgba(240, 165, 0, 0.3)" stroke-width="1.5"/>
</g>

<!-- Abstract stock chart lines -->
<path d="M50 400 C 150 300, 250 500, 350 450 S 550 200, 650 300 S 850 550, 950 400 S 1150 250, 1150 250" stroke="url(#line-grad1)" stroke-width="4" fill="none" stroke-linecap="round"/>
<path d="M80 250 C 180 350, 280 150, 380 200 S 580 450, 680 350 S 880 100, 980 250 S 1120 450, 1120 450" stroke="url(#line-grad2)" stroke-width="4" fill="none" stroke-linecap="round"/>

<defs>
    <linearGradient id="line-grad1">
        <stop offset="0%" stop-color="#16A085"/>
        <stop offset="100%" stop-color="#F4D03F"/>
    </linearGradient>
    <linearGradient id="line-grad2">
        <stop offset="0%" stop-color="#E74C3C"/>
        <stop offset="100%" stop-color="#8E44AD"/>
    </linearGradient>
</defs>

<!-- Title Placeholder -->
<text x="50" y="80" font-family="'Segoe UI', 'Helvetica Neue', sans-serif" font-size="48" font-weight="bold" fill="#FFFFFF">
    魔鬼都在细节里！如何实现强化学习交易模型
</text>


</svg>