---
title: 交割日魔咒？
slug: why-A-share-crash-at-mar-27
date: 2024-03-27
category: strategy
motto: 当其他企业家还在努力形成世界观时，他已经形成了宇宙观 -- 埃隆.马斯克传
lunar:
lineNumbers: true
tags: 
    - strategy
    - rsi
    - zigzag
    - 穹顶压力
---

周日莫斯科的恐袭，让所有的A股交易者捏了一把汗，怕不是我A又要买单？果然，短短三日，沪指跌去1.8%，中证1000跌去5.89%，亏钱效应还是非常明显的。

痛定之后，留下几个复盘问题，首先是，下跌的原因是什么？当然，我们求解的方法，都是量化思路。

## 交割日魔咒？

关于下跌的原因，最被人采信的说法是今天是ETF期权交割日。上一个交割日，2月28日，沪指大跌1.91%。

顺便，我们也把今年的几个重要的交割日分享一下：

<!--
<Table>
head: 月份,股指期货,ETF期权,A50
body:
  - 1月  , 1月19日  , 1月24日  , 无      
  - 2月  , 2月23日  , 2月28日  , 2月28日 
  - 3月  , 3月15日  , 3月27日  , 3月28日 
  - 4月  , 4月19日  , 4月24日  , 无      
  - 5月  , 5月17日  , 5月22日  , 5月30日 
  - 6月  , 6月21日  , 6月26日  , 6月27日 
  - 7月  , 7月19日  , 7月24日  , 无      
  - 8月  , 8月16日  , 8月21日  , 8月29日 
  - 9月  , 9月20日  , 9月25日  , 9月27日 
  - 10月 , 10月18日 , 10月23日 , 无      
  - 11月 , 11月15日 , 11月20日 , 11月28日
  - 12月 , 12月20日 , 12月25日 , 12月30日
</Table>
-->

![](https://images.jieyu.ai/images/2024/03/交割日.jpg)

股指期货的交割日为每月的第三周周五；ETF期权交割日为每月第四周的周三；A50交割日为相关月的倒数第二个交易日。

---

作为量化研究员（或者quant developer），我们需要自己有能力计算出上述交割日。


!!! tip
    在量化二十四课中，这个问题是作为一道练习题出现的（有参考答案）。不过，这里我们可以介绍一下解题思路。

核心是要使用calendar这个库。我们可以通过它的monthcalendar来构建每月的日历：

```python
calendar.monthcalendar(2024, 2)
```
<!--源码在cheese-course/24lectures/exercise/answers/lesson4.ipynb中-->
这会得到以下结果：

<!--
<Table>
head: 周一,周二,周三,周四,周五,周六,周日
body:
    - 0, 0, 0, 1, 2, 3, 4
    - 5, 6, 7, 8, 9, 10, 11
    - 12, 13, 14, 15, 16, 17, 18
    - 19, 20, 21, 22, 23, 24, 25
    - 26, 27, 28, 29, 0, 0, 0
</Table>
-->
![](https://images.jieyu.ai/images/2024/03/month-calendar.jpg)

表格中为0的部分，表明那一天不属于当月。因此，我们要知道每月的周五，只需要将上述输出转换为DataFrame，再取第4列，就可以得到所有的周五了。然后判断第三个周五是否为交易日，如果不是，则取第四个周五。


## 大摩的预言

另一个传闻则是一个未经证实的大摩的小作文，说是反弹到3090点以来，A股获得超过15%，可以看成兑现2024年利润预期。

实际上，这应该是一则谣言，原因是，除了今天之外，本周大小摩一直在加仓。

## 趋势分析方法
在量化二十四课中，我们介绍了一些趋势分析方法。实际上，运用这些方法，我们不需要去猜测大跌背后的直接原因；相反地，近期A股的走势已经预示了下跌的可能，随后的测试确认了下跌趋势成立。

首先是3100整数关口压力。我们在《左数效应 整数关口与光折射》那篇文章中介绍过左数效应和整数关口压力/支撑。上周二、周四确认了压力存在。

其次是对RSI的分析。本轮日线RSI的高点在2月23日打出，随后分别在3月5日、3月11日和3月18日形成顶背离。这期间在30分钟级别上，RSI得到多次调整，但在3月21日上午10点，30分钟的RSI也再次形成顶背离。在课程中，我们介绍RSI超过前期高点，是一个值得注意的见顶信号，如果此时均线是走平或者向下的，则基本可以确认。



如何寻找RSI的前期高点呢？我们介绍的方法是，通过zigzag库，寻找5日均线的上一个最高点，则它对应的RSI也就是前期的高点。下一次当RSI在数值上逼近或者超过前高时，也往往就是局部的高点。为什么不用zigzag来寻找本次的最高点？这是因为zigzag寻找高低点需要延时几个周期才能找到。如果我们使用移动平均线的话，还要再多几个周期的延时，这样等zigzag确认最近的高点已经出现时，往往已错过了最好的交易时机。改用RSI是否逼近前期高点，我们可以立即发出信号，并且在下一周期，就可以根据k线走势来确认这个信号是否得到市场认可。

通过zigzag来寻找局部最高点的方法如下：

```python
from zigzag import peak_valley_pivots
close = ...
ma = moving_average(close, 5)

pct_change = ma[1:]/ma[:-1] - 1
std = np.std(pct_change)

up_thresh, down_thresh = 2*std, -2*std

peak_valley_pivots(ma, up_thresh, down_thresh)
```

peak_valley_pivots需要我们传入两个threshold参数，以帮助它来确认一个局部极值是否可以作为峰谷。只有当该点大于左右侧up_thresh,down_thresh以上时，一个点才会被确认为峰。对谷的确认也是一样。



这里我们用了一点小小的统计技巧，即使用近期涨跌幅的2倍标准差来作为阈值。


如果一个点高于平均值的两倍标准差，也确实可以看作是一个离群值 -- 从几何意义上来讲，可以当成一个峰值或者谷值。这种处理方法，我们在布林带、或者seaborn的包络图中已经见识过了。

通过上述方法，在绝大多数情况下，我们可以很准确地找出过去出现的峰和谷：

![75%](https://images.jieyu.ai/images/2023/06/zigzag.png)

通过这些峰和谷，我们就能找出上一次RSI的最高点和最低点。当下一次RSI再次逼近这个值的时候，我们就可以发出信号，并且在下一周期检测信号质量。



最后是对均线拐头的判断。在上周五10时后，30分钟线的20周期均线已经拐头，且股价运行在均线下方，结合前面的整数关口、RSI高点，此时可以立即减仓。

![](https://images.jieyu.ai/images/2024/03/3月21下行均线.jpg)

随后在3月25日下午14时，三个30分钟线确认无法突破这条下行的均线，确认反弹失败。此时的20周期线就象一个屋顶一样盖下来。我们把这种均线的压力称为穹顶压力。传统的一些经典技术分析中，也把它当成抛物线来拟合和分析。在量化二十四课中，我们介绍了一种更鲁棒的方法来判断均线的走势。毕竟，多项式拟合在多数情况下是不可用的。



![](https://images.jieyu.ai/images/2024/03/dom-pressure.jpg)

## 方法的有效性分析

我们这里讲到的整数关口、穹顶压力和RSI前高，都有精准的定义，是完全可以量化的。读者也可以自行观察它们的有效性。

你可能要问，如果这些方法有效，那岂不是制造出来一台印钞机？

实际上，印钞机要复杂许多。戏法人人会变，各有巧妙不同。要做到实时跟踪这些信号，你得有一个稳定、高性能的量化框架。



其次，这里的方法更适用于大盘分析，但投资最终是要落实到个股上的。只有能选取出跟大盘走势高度相关的个股，大盘分析才能发挥作用。

此外，尽管我们使用了多种方法来提前捕捉信号，但信号发出到确认仍然有一个延时。这会使得我们逃顶和抄底都会晚一些，从而损失部分利润。如果趋势频繁翻转，这种情况下，利润损失加上交易成本，也有可能失败。但是，它显然能帮我们避免大的损失。

量化分析的作用不是保证你百分之百成功，但它能保证在应该成功的时候，你比别人更有可能成功。

## 资产全球配置

在A股下跌比较确定时，我们应该及时退出市场。也可以考虑一些跨境ETF。在这今年以来的行情中表现的特别明显。比如今天盘中沪指14点开始反弹时，某跨境ETF从高点下跌1.6%；而当14：15，沪指上攻到30分钟20周期均线遇阻后，该ETF立刻反弹，最后半小时上涨1.37%，最终收报3.67%。在一些极端时刻，这些跨境ETF与沪指形成了翘翘板效应。



