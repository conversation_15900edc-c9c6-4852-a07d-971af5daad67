---
title: 带你读论文：PCA、离散小波和 XGBoost构建交易策略
date: 2024-09-03
category: algo
slug: combining-pca-wavelet-and-xgboost
stamp_width: 60%
stamp_height: 60%
tags: [algo, xgboost,PCA,wavelet]
---

> 这是 Nobre, Neves 发表于 2019 年的 [一篇论文](https://www.sciencedirect.com/science/article/abs/pii/S0957417419300995?via%3Dihub)。在论文一起，生成了一个机器学习交易策略，取得了比 Buy-and-Hold 策略及另一个对照策略更好的回报。本文正文部分为原论文的摘要，在最后的 QuanTide 评论中，我提供了一些点评。

文章介绍了一种应用于金融领域的专家系统，该系统融合了主成分分析 (PCA)、离散小波变换 (DWT)、极限梯度提升 (XGBoost) 以及多目标优化遗传算法 (MOO-GA)，旨在为投资者提供最佳的买卖点信号，以期在较低的风险水平下实现较高的投资回报。

PCA 用于缩减金融输入数据集的维度，DWT 用于对每个特征进行降噪处理，经过处理的数据集随后输入到 XGBoost 二元分类器中，其超参数由 MOO-GA 进行优化。

结果显示，PCA 显著提升了系统性能；而将 PCA 与 DWT 联合应用后，该系统能够在五个金融市场中的三个中超越传统的买入持有策略，实现了平均 49.26%的投资组合回报率，相比之下，买入持有策略的平均回报率为 32.41%。

## 相关工作

### 主成分分析

在相关工作中，主成分分析（PCA）被用于减少高维数据集的维度，同时保留数据的主要特征。PCA 通过对原始数据集进行线性组合来形成一组新的主成分，这些主成分最大限度地保留了原始数据的信息，并且具有高方差的特点。通过去除那些变化较小的维度，PCA 不仅简化了数据集，还提高了计算效率。

### 离散小波变换

离散小波变换（DWT）是一种强大的工具，它提供了处理现实世界问题中时间序列数据的时间变化特性的方式，与传统傅立叶变换相比，尤其适用于非平稳信号，如股票市场价格。

传统傅立叶变换假设信号是周期性的并且在整个时间区间上定义，而小波变换则允许信号表示为时间局部化的基函数的叠加。这使得小波变换能够同时捕捉信号的时间和频率信息。

![](https://images.jieyu.ai/images/2024/09/dwt.jpg)

小波变换的基础是将任何函数表示为一组构成小波变换基函数的小波的叠加。这些小波是有限长度振荡波形（称为母小波）的缩放和平移副本，即子小波。选择最佳的小波基依赖于待分析的原始信号特性和预期的分析目的。最终的结果是一组具有不同分辨率的时间-频率表示，这就是为什么小波变换被称为多分辨率分析的原因。

在本文提出的方法中，仅使用离散小波变换（DWT）。DWT 将信号分解为一组正交的小波，与连续小波变换相比，它更适合于实际应用，因为它可以更容易地实现数字计算机上的离散处理。

小波变换在金融时间序列数据预处理中的应用主要是为了去噪，从而帮助提高后续模型的预测精度。

### 遗传算法

遗传算法是一种元启发式优化方法，受到自然界生物进化过程的启发，用于解决复杂空间中的优化问题。它通过模拟自然选择和遗传机制来寻找问题的近似最优解。

遗传算法从一个由随机生成的个体组成的初始群体开始，这些个体代表了问题解的候选者。每个个体，或者说染色体，包含了一系列的参数或变量，这些变量被称为基因。

在每一代中，根据个体适应度（即个体在给定优化任务中的性能评分），选择个体进行繁殖。适应度较高的个体更有可能被选择，进而通过配对交换基因片段（交叉操作）产生新的后代。

此外，还会随机地对某些后代执行基因变异操作，以此引入新的遗传信息。

这些操作反复进行多代，直到满足预定的停止标准，例如达到最大迭代次数或群体收敛于稳定状态。最终的目标是希望经过一系列演化过程后，能够获得足够好的解。

### XGBoost

XGBoost（Extreme Gradient Boosting）是一种优化的分布式梯度提升库，设计目的是为了实现高效、灵活和便携。它在传统梯度提升决策树的基础上做了改进，增加了一些增强性能的特性。具体来说，XGBoost 引入了正则化项来简化模型，帮助减少过拟合的可能性，并且支持并行处理，从而大大加快了训练的速度。

XGBoost 的一个重要特性是它允许用户自定义损失函数，并且能够自动处理缺失数据。它通过并行地构造多个树来提高计算效率，这与传统的梯度提升模型不同，传统的梯度提升模型通常是串行地建立每个树。此外，XGBoost 还可以自定义优化目标函数和评估指标，使得它非常适合于各种机器学习任务，包括在金融市场上预测股票价格的方向。

研究表明，当应用于股票市场价格方向预测时，XGBoost 作为分类器的表现超过了诸如支持向量机（SVM）和人工神经网络（ANN）等非集成方法。特别是在 60 天和 90 天的预测周期内，XGBoost 展示了更好的长期预测准确率。

## 提议中的方案
### 系统架构

![](https://images.jieyu.ai/images/2024/09/architecture-of-xgboost.jpg)

### 目标方程

在本文中，目标是预测第 t+1 天的收盘价`Closet+1`相对于第 t 天的收盘价`Closet`是否会有一个正向或者负向的变化。因此，提出了一种监督学习解决方案，具体来说是一个二分类器。要预测的目标变量`y`是第 t+1 天相对于第 t 天收盘价变化的信号，它遵循一个二项式概率分布`y ∈ {0, 1}`，其中：
- 如果收盘价变化为正，则`y`取值为`1`；
- 如果收盘价变化为负，则`y`取值为`0`。

这个目标可以数学地定义如下：

\[
y_t = 
\begin{cases} 
1 & \text{if } Closet_{t+1} - Closet_t \geq 0 \\
0 & \text{if } Closet_{t+1} - Closet_t < 0 
\end{cases}
\]

其中：
- `Closet_{t+1}` 是第 t+1 天的收盘价。
- `Closet_t` 是第 t 天的收盘价。

所有目标变量组成的数组命名为`Y`。金融输入数据集`X`是通过数据预处理模块输出的数据集，在其中应用了 PCA 和 DWT 技术于规范化后的数据集，该数据集包含了原始金融数据和技术指标。

### 技术分析模块

技术分析模块接收来自 Financial Data Module 的输出，并向其应用多个技术指标。使用技术指标的主要目的是，每个指标以彼此不同的方式提供有关过去原始数据的基本信息，因此将不同的技术指标组合在一起有助于检测金融数据中的模式，从而提高金融数据的性能预测系统。

该模块将创建数据集，该数据集将用作数据预处理模块的输入。该数据集由所使用的 26 个技术指标集和 5 个原始金融数据特征之间的组合组成，产生下表中所示的具有 31 个特征的数据集。技术指标的计算是使用 Python 库 TA-Lib 完成的。

![](https://images.jieyu.ai/images/2024/09/ta-moduel-output.jpg)

### 数据预处理模块
#### 数据标准化

略。

#### 主成分分析

PCA 模块接收归一化输入数据集，包含 26 个技术指标和 5 个原始金融数据特征，总共 31 个归一化特征，为了降低数据过度拟合的风险并降低系统的计算成本，PCA 模块会将具有 31 个特征的数据集转换为较低维度的数据集，同时仍保留大部分原始数据集方差。 

PCA 首先将其模型与归一化训练集进行拟合，以确定代表最大方差方向的分量。然后，主成分按照它们解释的方差量进行排序，并且仅保留那些加起来至少达到原始训练集方差 95% 的主成分。最后，将数据投影到主成分上，得到一个比原始数据集维度更低的数据集，因为它只保留了更好地解释特征之间关系的数据样本。然后将缩减后的数据集馈送到小波模块。

PCA 模块，使用了 Scikit-learn python 库。

#### 小波变换

虽然数据集在 PCA 模块中已经进行了简化，降低了维度，只保留了能更好地解释特征之间关系的数据，但一些不相关的数据样本可能会产生负数，对系统训练和预测性能的影响可能仍然存在。 

PCA 技术删除了特征子集中不相关的数据点，而 DWT 技术将对 PCA 减少的数据集中存在的每个特征在时域中执行降噪。这个过程减少了数据集中噪声的影响，同时尽可能保留每个特征的重要组成部分。

该系统中针对每个金融市场测试的小波基有：Haar 小波、Daubechies 小波和 Symlet 小波。对于 Daubechies 和 Symlet 小波，测试的阶数为 3、6、9、15 和 20。

尽管分解级别越高，可以消除的噪声就越多，从而可以更好地表示每个特征的趋势，但这也可能导致消除带有市场特征的波动。因此，在该系统中，测试的分解级别为 2、5 和 7，以找到每个金融市场的最优去噪分解级别。

DWT 首先指定小波基、阶数和所使用的分解级别。然后，对于训练集的每个特征，DWT 执行多级分解，这将产生一个近似系数和 j 个细节系数，其中 j 是所选的分解级别。

为了计算验证集和测试集的近似系数和细节系数，每次将一个数据点添加到训练集中，计算新信号的系数并保存与添加的数据点对应的系数，以便避免考虑未来信息的系数。执行此过程直到验证集和测试集中的所有点都计算出各自的系数。然后对获得的细节系数进行阈值处理并重建信号，从而产生每个原始数据集特征的去噪版本。应用 DWT 后，数据集被馈送到 XGBoost 模块。

PyWavelets 和 scikit-image 库被用于开发该系统中的 DWT 模块。

### XGBoost 模块

XGBoost 二元分类器负责系统的分类过程。

#### 二元分类器

分类器的输出 ^y 是给定当前观测值 x 的预测值，对应于实际日期 t。变量 ˆy 在 [0,1] 范围内，所有 ˆy 的集合对应于预测交易信号，该信号指示系统在下一个交易日是否应该持有多头或空头头寸。

在 XGBoost 二元分类器算法开始之前，必须选择一组参数。定义机器学习系统架构的参数称为超参数。由于每个时间序列都有其自身的特点，因此对于每个分析的时间序列都有一组不同的最优超参数，即使模型具有良好的泛化能力，从而在样本外数据中取得良好结果的超参数。因此，为了在每个分析的金融市场中获得最佳结果，必须找到最佳的超参数集。为此，使用了 MOO-GA 方法，如下一节所述。

数据预处理模块输出的预处理数据被馈送到 XGBoost 二元分类器，以及待预测的目标变量数组 Y，并且均被分为训练集、验证集和测试集。定义数据集和 XGBoost 超参数后，训练阶段就开始了。系统的泛化能力是通过系统处理未见数据的表现来衡量的。因此，在训练阶段结束后，使用样本外验证集来测试训练阶段获得的模型，以验证所获得模型的泛化能力。

该验证集在 MOO 过程中使用，有助于使用未见过的数据选择性能最佳的解决方案。训练和验证阶段结束后，将创建最终模型，即使用 MOO-GA 找到的最佳超参数集进行训练的模型，并将生成的输出与测试集进行比较，以验证预测的质量。如前所述，输出采用数据点属于 0 或 1 类之一的概率形式。

XGBoost 库用于开发该系统中的 XGBoost 二元分类器。

#### 多目标优化遗传算法

建机器学习模型时，模型架构有很多设计选择。大多数时候，用户事先并不知道给定模型的架构应该是什么样子，因此探索一系列可能性是可取的。 

XGBoost 二元分类器的超参数就是这种情况，这些参数定义了分类器的架构。这个寻找理想模型架构的过程称为超参数优化。

采用多目标优化方法而不是单目标优化方法，是因为我们的最终目标要实现一个高回报同时低风险的交易系统。因此，自然必须使用统计度量来评估系统相对于所做预测的性能 -- 在本例中是 Accuracy；但另一方面，也需要使用度量来评估系统实现良好回报的能力，同时最大限度地减少损失 -- 在这种情况下是 Sharpe Ratio。

因此，根据两个选定的目标函数来评估候选解决方案：Accuracy 和 Sharpe Ratio。每个解的集合代表了 MOO-GA 要优化的适应度函数，其目标是最大化每个目标函数。因此，给定 XGBoost 二元分类器、金融数据集 X 和目标变量数组 Y，MOO-GA 将搜索和优化 XGBoost 二元分类器超参数集，目标是最大化所获得的预测的准确性和夏普比率。

为了找到旨在最大化前面提到的两个目标函数的最佳超参数集，MOO-GA 方法基于非支配排序遗传算法-II (NSGA-II)。

由于 XGBoost 二元分类器中存在许多超参数，因此只有那些对二元分类器的架构有重大影响并因此对其整体性能影响较大的超参数才会被优化。选择要优化的超参数对偏差-方差权衡也有较大影响，它们是：学习率、最大树深度、最小子体重和子样本，这些超参数中的每一个都构成一个基因 MOO-GA 中的染色体。

在提出的 MOO-GA 中，使用了两点交叉算子，其中在父级字符串上选择两个点，并且两个选定点之间的所有内容都在父级之间交换。选择的突变率为 0.2，这意味着交叉算子生成的每个新候选解都有 20% 的概率发生突变。

我们还使用了超突变，这是一种在进化算法中将多样性重新引入群体的方法。在该系统中，在进化过程中调整变异率，以帮助算法跳出局部最优。

下图显示了重要的超参数：

![](https://images.jieyu.ai/images/2024/09/most-important-hyper-params.jpg)

DEAP python 库用于开发该系统中的 MOO-GA 模块。

### 交易模块

交易模块负责模拟真实的金融市场交易。其主要功能包括：

* 接收输入：从 XGBoost 模块获取交易信号和金融数据。
* 模拟市价订单：基于输入的交易信号，在金融市场中模拟市价订单。
* 状态机设计：采用多头、空头和持有三种状态的状态机来执行交易。具体而言，给定交易信号后，状态机会根据信号中的市场订单执行相应的操作。XGBoost 二元分类器的预测结果 ^y 表示类别 p(^y) 的预测概率。由于选定的两个类别分别表示第 t+1 天与第 t 天收盘价的变化情况，因此可以利用这些预测结果构建交易信号。

交易信号构建方法如下：

* 如果 p( ˆy) ≥ 0.5，且所选类别为 1，这意味着股票在 t + 1 天的收盘价（收盘价）预计将出现正变化，从而代表在 t 天有买入机会，即多头头寸被采取。该动作由训练信号中相应日期的位置 1 表示；
* 相反，如果 p( ˆy) < 0.5，且所选类别为 0，这意味着股票在 t + 1 天的收盘价（收盘价）预计将出现负变化，从而代表在 t 天的卖出机会，即采取空头头寸。该动作由相应日期的训练信号中的位置 0 表示。

因此，交易信号的值在 [0,1] 范围内，交易模块负责解释这些值并将其转换为交易动作。

## 实验结果

下图显示了训练过程的示意。

![](https://images.jieyu.ai/images/2024/09/xgboost-training-process.jpg)

### 案例 1 使用 PCA 的效果

在第一个案例研究中，分析了 PCA 技术对所实现系统性能的影响。

对照系统分为两种配置：

基本系统：输入数据集包含 31 个归一化的金融特征。
改进系统：输入数据集经过标准化处理。
在改进系统中，应用 PCA（主成分分析）技术来降低数据集的维度。具体来说，对包含 31 个金融特征的标准化数据集进行 PCA 处理后，每个金融市场保留了 6 个主成分。这意味着原始的 31 个金融特征被缩减为 6 个低维特征。

下图列出了每个系统的实验结果以及买入并持有策略的结果。每个金融市场获得的最佳结果用粗体突出显示。

![](https://images.jieyu.ai/images/2024/09/influence-with-cpa.png)

通过检查获得的结果可以得出结论，PCA 技术的使用在提高系统性能方面发挥着重要作用，因为它不仅可以获得更高的回报，而且还可以获得更高的准确度值。

PAC 降维使得 XGBoost 二元分类器能够生成复杂度较低的模型，具备良好的泛化能力，从而避免过拟合训练数据。这样，系统能够在玉米期货合约和埃克森美孚股票上实现比 Buy & Hold 策略更高的回报。

在其他三个分市场中，使用降维技术对于系统获得正回报至关重要，因为在基本系统中，泛化能力不足以正确分类测试集。如果没有应用 PCA，系统只能在埃克森美孚公司股票上实现比买入并持有策略更高的回报。

图 4 显示了在测试期间使用基本系统和带有 PCA 的系统所得回报的对比图。可以看出，正如前面提到的，引入 PCA 后，系统能够获得更高的回报。

![](https://images.jieyu.ai/images/2024/09/returns-compare-by-pca.png)

### 案例 2 使用离散小波分析的效果

在这个案例中，我们研究将 DWT 技术与 PCA 结合使用，以同时实现对输入数据的降维和降噪，看这两种技术一起应用是否能获得更好的结果。

下图列出了每个系统的结果及买入并持有策略的结果。每个金融市场获得的最佳结果用粗体突出显示。由于所有组合分析过于复杂，图中仅展示了两种最佳组合。

![](https://images.jieyu.ai/images/2024/09/with-pca-dwt.png)

通过检查获得的结果可以得出结论，PCA 和 DWT 去噪技术的结合使系统比仅使用 PCA 的系统获得更好的结果。

这是因为在该系统中，PCA 降低了金融输入数据集的维度，并且 DWT 对这个降低的数据集进行了去噪，这不仅有助于避免过度拟合训练数据，也有助于去除一些可能损害系统性能的不相关样本，从而帮助系统的学习过程并提高其泛化能力。

然而，这种性能的提高只有在使用适当的小波基、阶数和分解级别时才能得到验证，因为与仅使用 PCA 相比，错误地使用 DWT 会导致更差的系统性能，甚至更低的回报。

### 案例 3 性能评估

在本案例研究中，将所提出的系统的性能与 [Nadkarni,Neves](https://www.sciencedirect.com/science/article/abs/pii/S0957417418301519) 在 2018 年提取的一个策略进行比较，以验证使用所提出的方法与其他方法的优缺点。

下图显示了两个系统的性能对比。

![](https://images.jieyu.ai/images/2024/09/final-comparison-pca-xgboost.png)

## 结论

本文提出了一种结合 PCA 和 DWT 分别进行降维和降噪的系统，以及使用 MOO-GA 优化的 XGBoost 二元分类器，目的是实现一个尽可能的最大回报，同时最小化风险水平交易策略。实验结果支持了此方法的有效性。

## QuanTide 评论

本文提出了基于机器学习构建交易策略的一个完整框架，并创造性地使用了DWT和GA等先进算法。这个交易系统使用时序特征作为训练特征，对股票、期货和加密货币都有较好的覆盖。注意它不是一个资产定价类的策略。

论文中有几处地方值得商榷，也是构建机器学习交易策略的难点

### 数据标注问题

论文中实现的是一个监督学习算法，数据标注不可或缺。论文使用的标注方法是：

> 对于t期数据，如果pnl > 0，则标记为1，否则为0。

这里的问题在于，如果一支股票的 Pnl 绝对值是 0.5%以下，它实际上并不有很强的标签意义 -- 换言之，这个结果并不一定反映主力的操作意图，它很可能只是主力缺席之后，股价随市场正常波动的一个状态。这种情况下，无论是归为1，还是归为0，都可能是错误的。为了抵消这种错误标注的负面影响，往往需要我们生产更多正确的标注才行。

### 遗传算法

在寻找模型的超参数时，论文使用了遗传算法。使用遗传并非必须，它只是快速找到超参数的一个算法。我们也可以使用 GridSearch 或者 RandomSearch 的方法。

我们注意到论文中并没有像PCA和DWT那样，给出使用MOO-GA与不使用MOO-GA的对比。这也验证了我们的观点。

### 使用DWT来去除噪声

这部分有两点可以拿出来讨论。一是论文运用DWT的阶段。它是放在TA之后。如果你熟悉技术分析（TA）的话，就知道原始的行情数据经过TA处理成技术指标之后，它已经把原始数据在时序上的特征提取出来了，而这些技术指标序列，就不见得是一种波信号了。所以，此时使用DWT进行滤波，意义何在?难以解释。

文中也提到，不是每一次使用DWT都能获得好的结果，可能原因就在这里。

除了使用DWT的阶段之外，使用DWT的用处也值得讨论。多数论文都是使用DWT来进行滤波 -- 在这件事情上，无论他们给出的数据有多好，我始终认为，都不可能比得过SMA -- SMA有明确的金融含义，其它方法只有数学上的精巧。

但是，如果你认为 DWT 能够去除噪声 -- 那么一个显而易见的推论就是，它知道哪些是信号，哪些是噪声。既然如此，为什么我们不使用小波分析来提取信号特征来进行学习？

所以，更好的方案应该是将DWT作为与TA模块并生的一个模块，用来生成特征。

### 关于归一化算法

论文中提到，他们使用了Min-Max来进行归一化。这一点上是个灾难的选择。我们只能对取值范围固定的数据进行Min-Max归一化，而股价的值域从理论上看，是$(0, +\infty)$。

在我们学习量化的过程中，参考已发表的论文无疑是成长的捷径。但是，有些学者并身没有太多的实际交易经验，导致论文中也可能存在各种瑕疵，这无疑也会导致论文的结果无法在实战中运用。因此，我们开设了《因子投资与机器学习策略》的课程，如果对这一领域感兴趣，但不得其门而入的，可以加入我们一起学习。

该论文可在[这里](https://www.jieyu.ai/assets/ebooks/Combining-Principal-Component-Analysis-Discrete-Wavelet-Transform-and-XGBoost-to-trade-in-the-financial-markets.pdf)下载
