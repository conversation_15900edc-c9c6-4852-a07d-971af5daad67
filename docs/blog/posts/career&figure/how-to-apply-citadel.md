---
title: Datathon-我的Citadel量化岗之路！附历年比赛资料
date: 2024-08-11
category: factors
slug: how-to-apply-citadel
motto: 这世上所有的坚持 都源自足够的热爱
img: https://www.citadel.com/wp-content/uploads/2024/07/Citadel_Intenship_KenSpeakstoInterns_YT_v2.jpg
stamp_width: 60%
stamp_height: 60%
tags: [strategy]
---

Citadel是一家顶级的全球性对冲基金管理公司，由肯尼斯.格里芬(Kenneth Griffin)创建于1990年，是许多量化人的梦中情司。


Citadel为应届生提供多个岗位，但往往需要先进行一段实习。

Citadel的实习资格也是比较有难度，这篇文章就介绍一些通关技巧。

我们会对主要的三类岗位，即**投资**、**量化研究**和**软件研发**都进行一些介绍，但是重点会在量化研究岗位上。

我们将介绍获得量化研究职位的一条捷径，并提供一些重要的准备资料。

<!--
## wonderlic 测试


Citadel 在线评估测试，也称为 Citadel Wonderlic 测试，基本上是一种心理测量评估，旨在衡量各种技能，否则无法通过查看候选人的简历或在工作中所做的事情来衡量各种技能。 - 人物访谈。这些技能包括；决策、解决问题、学习新信息的能力以及适应不断变化的工作环境的能力。

Citadel 还使用此测试来简化招聘过程。这是因为作为一家具有这种能力的公司，它每年都会收到数千份为其提供建议的所有职位的申请。因此，使用 Wonderlic 这样的工具从一开始就淘汰掉不合格的候选人，而不是花费大量的时间和资源进行面对面的面试，这是很有意义的。


50个问题，12分钟，每个问题14秒。可以在网上找一些备考课程。一般在500到1000之间

Wonderlic Select 并不是唯一的 Citadel 评估。如果您正在尝试 Citadel 软件工程师实习，您可能会面临 HackerRank 编码评估。如果您申请成为 Citadel 交易实习生，您将被要求参加 Citadel 金融概念评估休息 (Citadel FCAT)。

-->

## 投资类岗位

投资类岗位可能是最难的，但待遇十分优渥。

2025年的本科或者硕士实习生将拿到5300美元的周薪，并且头一周都是住在四季酒店里，方便新人快速适应自己的同伴和进行社交。

在应聘条件上面，现在的专业和学校背景并不重要，你甚至可以不是经济学或者金融专业的，但需要对股票的估值感兴趣。


但他们的要求是“非凡(Extraordianry)” -- 这个非凡的标准实际上比哈佛的录取标准还要高一些。Citadel自称录用比是1%，哈佛是4%。如果要对非凡举个例子的话，Citadel会介绍说，他们招过NASA宇航员。

所以，关于这个岗位，我很难给出建议，但是Citadel在面试筛选上，是和Wonderlic合作的，如果你确实很想申这个岗位，建议先报一个Wonderlic的培训，大约$50左右。Wonderlic Select会有认知、心理、文化和逻辑方面的测试，参加此类培训，将帮助你刷掉一批没有准备的人。


![](https://images.jieyu.ai/images/2024/08/11-weeks-of-extraordianry-growth.jpg)
<cap>11 weeks of extraordinary growth program</cap>

一旦入选为实习生，Citadel将提供一个11周的在岗实训，实训内容可以帮助你快速成长。通过实训后，留用的概率很大。

## 软件研发类

这个岗位比较容易投递，Citadel有一个基本的筛选，很快就会邀请你参加 HackerRank测试。由于HackerRank是自动化的，所以，几乎只要申请，都会得到邀请。



HackerRank有可能遇到LeetCode上困难级的题目，但也有人反映会意外地遇到Easy级别的题目。总的来说，平时多刷Leetcode是会有帮助的。并且准备越充分，胜出的机会就越大。

你可以在glassdoor或者一亩三分地（1point3acres）上看到Citadel泄漏出来的面试题，不过，多数信息需要付费。

## 量化研究和Datathon

参加Datathon并争取好的名次，是获得**量化研究岗位**实习的捷径。从历年比赛数据来看，竞争人数并不会很多（从有效提交编号分析），一年有两次机会。


这个比赛是由correlation one承办的。c1是由原对冲基金经理Rasheed Sabar发起的，专注于通过培训解决方案帮助企业和发展人才。

它的合作对象有DoD, Amazon, Citadel, Point 72等著名公司。可能正是因为创始人之前的职业人脉，所以它拿到了为Citadel, Point 72举办竞赛和招募的机会。在它的网站上有一些培训和招募项目，也可以看一看。



![](https://images.jieyu.ai/images/2024/08/correlation-one.jpg)
<cap>Correlation One</cap>

Datathon只针对在校生举办，你得使用学校邮箱来申请。通过官网在线报名后，你需要先进行一个90分钟的在线评估。这个评估有心理和价值观的、也有部分技术的。

评估结果会在比赛前三天通知。然后进入一个社交网络阶段（network session），在此阶段，你需要组队，或者加入别人的队伍。Datathon是协作性项目，一般要求4人一队参赛。



正式开始后，你会收到举办方发来的问题和数据集（**我们已搜集历年测试问题、数据集及参赛团队提交的答案到网站，地址见文末**），需要从中选择一个问题进行研究，并在7天内提交一个报告，阐明你们所进行的研究。

这个过程可能对内地的学生来讲生疏一些，但对海外留学生来讲，类似的协作和作为团队进行presentation是很平常的任务了。所以，内地的学生如果想参加的话，更需要这方面的练习，不然，会觉得7天时间不够用。

## 女生福利

女生除可以参加普通的Datathon之外，还有专属的Women's Datathon，最近的一次是明年的1月10日，现在开始准备正是好时机。

不过，这次Women's Datathon是线下的，仅限美国和加拿大在读学生参加。

## Datathon通关技巧

Datathon看起来比赛的是数据分析能力，是硬技巧，但实际上，熟悉它的规则，做好团队协作也非常重要。而且从公司文化上讲，Citadel很注重协作。



1. 组队时，一定要确保团队成员使用相同的编程语言，否则工作结果是没有办法聚合的。
2. 尽管Citadel没有限制编程语言和工具软件，但最终提供的报告必须是PDF, PPT或者HTML。并且，如果你提交的PDF包含公式的话，还必须提供latex源码。考虑到比赛只有7天，所以你平时就必须很熟悉这些工具软件。或者，当你组队时，就需要考虑，团队中必须包含一个有类似技能的人。
3. Datathon是在线的虚拟竞赛，所以，并没有现场的presentation环境。因此，一定要完全熟悉和遵循它的提交规范。
4. 也正是因为上一条，Report一定要条理清晰，一定要从局外人的身份多读几次，看看项目之外的人读了这份报告，能否得到清晰的印象。
5. 尽可能熟悉Jupyter Notebook和pandas（如果你使用Python的话）。这也是官方推荐，通过Notebook可以快速浏览竞赛所提供的数据集。
6. 补充数据是有益的，这能反映你跳出框架自己解决问题的能力。所以平常要多熟悉一些数据集。如果一些数据要现爬的话，那需要非常熟悉爬虫。因为爬虫与后面的数据分析是串行的。在数据拿下来之前，其它工作都只能等待。
7. Visualization非常重要。如果你习惯使用Python，平时可以多练习matplotlib和seaborn这两个库。
   
我们已经搜集了2017年以来所有的竞赛题目，包括数据、问题，以及一些团队提交的报告和代码。如果你需要准备Datathon，这会是一个非常好的参考。

在这里，我们对2024年夏的Datathon做一个简单介绍。



## 2024年 Summer Datathon

![](https://images.jieyu.ai/images/2024/08/datathon-2024-summer-ps.jpg)
<cap>Problem Statement of 2024 Summer Datathon</cap>

2024年的Datathon于8月5日刚刚结束。这次的题目是关于垃圾食品的，要求从提供的数据集中，得出关于美国食品加工的一些结论。除了指定数据集之外，也允许根据需要自行添加新的数据集。不过，这些数据集也提交给评委，并且不得超过2G。

论题可以从以下三个之中选择，也可以自行拟定：

1. 能够从肉类生产来预测餐馆的股价吗？
2. 糖的价格会影响年青人对含糖饮料的消费吗？如果存在影响，这种影响会有地区差异吗？
3. 肉制品生产低谷与失业人数相关吗？



有的团队已经将竞赛数据集、问题及他们的答案上传到github，下表是我们搜集的部分repo列表。其中包含了一些当年夺得过名次的solution，非常值得研究。如果你在练习中能达到此标准，那么就有较大概率在自己的比赛中取得名次。

| year                                                                         | rank | files              | 说明                   |
| ---------------------------------------------------------------------------- | ---- | ------------------ | ---------------------- |
| [2024 summer](https://github.com/arjashok/2024-Summer-Datathon)              | NA   | data, code, report | 两个团队的报告，可运行 |
| [2024 spring](https://github.com/chtang-hmc/Spring-Invitation-Datathon-2024) | NA   | data, code, report | 目录清晰，报告质量高   |
| [2023](https://github.com/redders7/datathon2023)                             | NA   | report, code       |                        |
| [2022](https://github.com/Bennyoooo/citadel_datathon_2022)                   | 3rd  | report, code       | 报告质量高，可视化效果 |
| [2021 summer](https://github.com/joshuali99/Citadel-Summer-Datathon-2021)    | 1st  | data, src, report  | 包含airbnb数据         |
| [2021 spring](https://github.com/evilpegasus/datathon-spring-2021)           | NA   | data,code,report   |                        |
| [2020]                                                                       | 3rd  | report             |                        |
| [2018](https://github.com/wlong0827/citadel-datathon-2018)                   | 1st  | data,code,report   | 两个团队的报告         |
| [2017]                                                                       | NA   | report,code,data   |                        |


这些竞赛的资料也都上传到了我们的课程环境。

![](https://images.jieyu.ai/images/2024/08/datathon-screenshot.jpg)
<cap>Datathon 历年资料</cap>



如果你想立即开始练习，可以申请使用我们的课程环境，这样可以节省你下载数据、安装环境的时间。我们已帮你调通了2024年夏季比赛的代码，可以边运行边学习他人的代码。
