---
title: 高薪金领都用啥编程语言？SQL、Python领航，附排名！
slug: the-most-important-coding-language-in-fintech
date: 2024-06-10
motto: 瞄准月亮射去吧，即使你错过了，也将落在星辰之中 - Les Brown
img: https://images.jieyu.ai/images/2024/06/fencing.jpg
category:
  - 人物
tags:
  - 人物
  - 职场
---

最近两天，收到私信咨询，想进入量化领域难吗？

提问者没有介绍任何自己的背景，也没有明确说明具体要从事什么岗位。因此这是一个无法回答的问题。不过，我会持续跟进这个问题，并给出一些参考资料。

今日焦点：金融界最需要什么样的编程技能。数据来源于Revelio Labs和eFinacialCareers。前者是人力情报数据分析公司，后者是金融专业求职平台。

---


根据Revelio Labs 的数据，在金融服务业最受欢迎的编程语言前十排名如下：

![](https://images.jieyu.ai/images/2024/06/coding-language-in-finance.png)

你可能想不到，SQL在金融领域有着王者般的地位。在整个科技领域，SQL与岗位的相关性只占18%，但在金融招聘领域中，却有25%左右的岗位要求掌握SQL。

当然，也有许多人并不把SQL视为编程语言。老实说，SQL只能视为一种数据库查询语言，我们无法用它来完成数据查询之外的工作。

因此，Python才是真正意义上的王者，不仅仅是在金融领域，根据TIOBE 6月的排名，它仍然位居榜首，并且受欢迎程度在上升中。

---

![](https://images.jieyu.ai/images/2024/06/tiobe-ranking-2024-6.png)


但是，金融行业对C++和Rust这样的互联网热门编程语言的需求并不大，尽管这两种语言在高频交易中不可或缺，但毕竟高频交易比较小众、无法吸纳大资金，因此行业的重心不会在这里。

越是低频交易，越能吸纳大量资金。因此，像Python这样尽管性能不佳，但开发快速灵活的语言，在金融界被广泛使用。此外，SQL的大量使用，也证明了金融业对大数据处理能力的要求并不高，大量的数据处理场景仍然可以使用SQL来实现。这也是完全可以理解的，因为在中低频交易广泛使用的财务数据，数据量并不大。

Java位列三甲也是意料之中。大量的事务系统，包括公司网站仍然会使用Java来开发。

---

Javascript能上榜，很可能也是因为这样的用途。投资公司为了保持神秘和高科技形象，他们的网页也常常做的比较酷炫。比如Millennium（千禧年）的官网上，就常常使用Javascript炫技。在他们最新的主页上，展示了js制作的磁力线效果和各种reveal特效。

<div style='width:"75%";text-align:center;margin-bottom:1rem'>
<img src='https://images.jieyu.ai/images/2024/06/millennium.png'>
<span style='font-style:italic;font-size:0.8rem'>千禧官网</span>
</div>

这界面丑是丑了点。但理工男的形象是立住了。所以，前端做得好，也是有机会进金融行业的。

R排在第4名也不意外。R语言在数据科学领域被广泛使用，R语言天然有很多统计方案的模块，它的语法简单，支持管道操作，对于临时性的数据分析处理非常友好。SAS上榜的原因也是一样。SAS与R对因子分析都天然支持得很好。统计模块及因子分析，都是金融行业数据分析的基础。

---

比较令人意外的是VBA。实际上VBA上榜，蕴含了在金融行业必须熟练掌握Excel的意味。所谓熟练掌握Excel，决不是仅仅是指掌握了它基于图形界面的功能，而是要求在关键时刻、复杂功能及快速处理大量数据时，能够使用VBA来编写脚本。

具有强大的编程能力，对于金融领域求职有多重要？我们看一下eFinancial Careers最近发布的一个全球信贷和可转换债券主管岗位，这是一个年薪\$50万到\$80万之间的工作：


!!! quote
    A renowned investment firm is looking for an Head of global credit and convertible bonds to report to their CIO function ...

    Experience

    - Fixed income in preferably a sell-side company
    - Front-office
    - Experience with arbitrage
    - Experience with credit, equities, convertibles, CDS
    - **Strong programming skills (Python or R, SQL, Excel or VB)**
    - Strong communication skills and ability to work in a team

---

如果你是一名在校生，可能多少掌握一点SQL和Python，但究竟要达到什么样的程度才算精通呢？

关于Python，在工程技巧方面可以参考我的新书《Python高效编程实战指南》（预定7月开售），算法方面可以多刷leetcode、kaggle的题，或者简街、千禧的puzzle专栏，我们的专栏也不时会有一些性能优化的技巧。

![50%](https://images.jieyu.ai/images/hot/book-cover-with-bridge.jpg)

关于SQL，如果是开发岗，需要了解数据分区规划、索引优化、SQL优化等，如果是数据分析岗，则只需要了解如何构建复杂的查询即可。这些查询主要是窗口函数应用、子查询与自连接、复杂条件筛选与分组等。
