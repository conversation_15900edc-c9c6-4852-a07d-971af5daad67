---
title: 月亮和Pandas - Wes Mckinney的传奇故事
slug: legend-of-wes-mckinney
date: 2024-04-10
category: career
motto: 不要因寻找地上的六便士，而错过了头顶的月亮
lineNumbers: true
img: https://images.jieyu.ai/images/2024/04/wes-mckinney-cover.jpg
tags: 
    - 人物
---

正如死亡和税收不可避免，Pandas对量化人而言，也具有同样的地位 -- 每个人都不可避免地要与之打交道。而Wes Mckinney正是Pandas的创建者。Pandas是有史以来，最成功的Python库之一，以一已之力，开拓了Python的生存空间。

---

毛姆的《月亮和六便士》讲述了这样一个故事，一个富有的英国股票经纪人，思特里克兰德，为了追求艺术和美，放弃自己的生活和家庭，来到巴黎，以艺术家的身份过着贫穷却目中无人的充足的生活，最终留下无数画作，享誉世界。

这本书表达了这样的意义，不要因终日寻找地上的六便士，而错过了头顶的月亮。金融和量化界充满着浮躁和虚荣，许多人追逐着快钱，然而easy come, easy go，最终却是一无所获。

20世纪初的金融人思特里克兰德，勘破了俗世红尘，转而潜心研究艺术，尽管后半生在物质上不如之前安逸，但**能做自己喜欢的事，才是幸福**。

我们今天要介绍的Wes Mckinney，其经历也与思特里克兰德有异曲同工之处。不过，Wes Mckinney在经济上只有很小一段时间没有收入，随后很快就取得了事业上的成功。

![](https://images.jieyu.ai/images/2024/04/wes4.jpg)

---

Wes Mckinney, 大学毕业于MIT，后来拿到了杜克大学的数学和统计学两个博士学位。

他于2007年加入投资管理公司AQR。这是一家巨头公司，员工人数有1000人左右，管理规模达到了1400多亿美元。他们为机构客户提供另类和传统投资工具。

在AQR任职期间，大量的基础数据分析和统计工作是基于Microsoft Excel 手工完成的，这种工作极其繁琐和低效率。于是，Wes Mckinney从2008年开始，尝试开发Pandas，并选中了Python作为开发语言。

<div>
<img src="https://images.jieyu.ai/images/2024/04/pandas-logo.png">
<p style="font-size:12px;text-align:center">来源：pandas logo</p>
</div>


在当时Python还不是一种很流行的数据分析和统计语言，不过Wes Mckinney很快就让它流行开来，Pandas在某种意义上，成为了“立王者”的角色 -- 当然Python最终取得今天这般地位，还要借助AI的风力，才能够青云直上。

---

Pandas非常成功，但带来的问题是，Wes Mckinney的业余时间已经不能满足开发的需求。于是，他像思特里克兰德一样，离开了金融行业（并且退学。似乎每一个成功的美国创业者，都必须有dropout的经历），专心一意开发Pandas。

Pandas是开源产品，最初并不能提供任何收入，Wes Mckinney靠着之前工作的丰厚收入，以及兼职来养活自己。这期间，他还出版了一本名为《Python for Data Analysis》的书，获得了一些稿费。


随着Pandas的成功，第二年，Wes Mckinney就从AQR招募了两名同事，与他一起探索Python和Pandas在金融行业的创业机会，随后， 他和Chang一起创建了DataPad公司。

现在， Pandas由超过2500名贡献者共同维护。 Wes Mckinney不再参与日常的pandas开发。但社区授予他终身仁慈独裁者（BDFL）的荣誉，拥有对Pandas未来发展的决定权，就像Guido之于Python一样。

2016年，Wes Mckinney与他人一起共同创建了Apache Arrow项目，现在Arrow是跨编程语言共享科学数据的主要格式。Arrow仍然是开源的项目，但Wes Mckinney提出一个非营利行业联盟的想法，成功地吸引到Two Sigma(就是大家熟悉的量化投资巨头)作为赞助商，此后，又吸引到NVidia, Intel, bloomberg等大公司的赞助，从而能够支持一个有六名全职开发人员的分布式团队。

---

从AQR开始，到主要以推广开源数据分析和数据科学工具作为职业生涯的主题，Wes Mckinney 经历了华丽的转身。他现在是Usra Labs的负责人，利用他的声誉，为Usra Labs获得资金，以促使开源和创新持续向前发展。

与坐在办公室里日复一日地数着绿纸相比，他显然更享受现在在田纳西纳什维尔的生活，因为他的目标是为这世界增添一份美好：do as much good in the world as I can。

Wes Mckinney的经历是一个传奇，他的成功也为现在想要进入金融行业的新人提供了一个范例：并不一定亲自下场做为金钱厮杀的角斗士，解决行业在软件方面的痛点，也许更容易成功。这世界并不缺少量化研究员，但Pandas和Arrow的创建者，永远只有一个。

**如果你盯着月亮看，最终至少也能获得满天的清辉，它的光芒，远超六便士。**

最后是一点来自 Wes Mckinney的小福利。

如果要推荐一本讲解Pandas的书，毫无疑问，没人任何书籍能比[《Python for Data Analysis》](https://wesmckinney.com/book/)更权威了。因为它是由Pandas的创建者 Wes McKinney 撰写的！这本书现在提供有网页版供开放访问。读者也可点击[此链接](https://wesmckinney.com/book/)阅读。在2023年4月进行更新后，它现在支持到了pandas 2.0版本。

---

![75%](https://wesmckinney.com/book/images/cover.png)
