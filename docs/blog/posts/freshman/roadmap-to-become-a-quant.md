---
title: "问薪无愧！<br>自学量化大纲有这75页就够了"
slug: roadmap-to-be-a-quant-arb
date: 2024-07-19
category: resources
img: https://images.jieyu.ai/images/university/simmons-hall.jpg
imgCopyRight: wiki/public domain
motto: 红云随步起 一箭中青霄 鹿行千里远 争知去路遥
tags:
  - freshman
  - resources
  - career
lineNumbers: true
---


题图为 MIT 的 Simmons University。版权声明 wiki/public domain。

该建筑属于 MIT 的下属学院 Simmons University，由裁缝 John Simmons 捐建。Simmons Hall 被认为是 Boston 最美的一座建筑。Simmons University 对女性友好，该校的 MBA 课程是全球第一个专门为女性设计的 MBA 课程。到目前为止，其本科教育还是以女生为主，研究生阶段则是男女混合。


这是 Stat Arb 给自学量化的人开的一份清单。他的博客有 9000 多名付费用户。清单是 pdf 格式，74 页，内容非常全面。


[【下载链接】](https://www.jieyu.ai/assets/ebooks/RoadmapUltimateEdition.pdf)

Stat Arb这个名字来自 Statistical Arbitrage（统计套利），真实身份其实正在摸鱼的业内人士。不过这不重要，关键看他给的资源好不好。

我拿到的这个清单是 2024~2025 版，刚刚发布半个月。之前他每年都有发布这样一个清单，并且一直在根据情况变化，及时修订。内容非常全面，不同职业规划目标的读者，都能从中受益。

![](https://images.jieyu.ai/images/2024/07/stat-arb-roadmap.png?2)

我个人最喜欢的是它的数据资源部分。相当一部分资源是免费的，尤其是 crypto 项目和 kaggle 上的数据集，大约几十个G。它也提供了一部分付费数据转储的，但可能访问时需要密码，这些密码在❌（🐦）上可以找到。

求职者可能更喜欢它的第17章内容，这部分是关于职业规划、面试准备的。我们也在陆续介绍大学和投资机构的情况，以备你将来可能需要投递这些学校或者机构。




## 章节内容

![](https://images.jieyu.ai/images/2024/07/roadmap-chap-2.jpg)



![](https://images.jieyu.ai/images/2024/07/roadmap-chap8.jpg)



![](https://images.jieyu.ai/images/2024/07/roadmap-chap12.jpg)



![](https://images.jieyu.ai/images/2024/07/roadmap-careers.jpg)



![](https://images.jieyu.ai/images/2024/07/roadmap-pair-trading.jpg)



![](https://images.jieyu.ai/images/2024/07/roadmap-blog.jpg)



## chap01
介绍了机器学习和算法交易相关的教科书，包括基础教材和进阶教材，如《Quantitative Trading 2nd edition》《Algorithmic Trading》《Machine Trading》等，并对一些模型和方法进行了评价。

## chap02
衍生品和波动率交易方面的教科书，推荐了一些关于衍生品和波动率交易的教科书，如《Hull Options Futures and other Derivatives》《Option Trading & Volatility Trading》等，并介绍了一些相关的知识和资源，如动态对冲、随机波动率等。

## chap03 
YouTube 视频资源，推荐了一些 YouTube 视频，包括 Ben Felix、Patrick Boyle、Leonardo Valencia 等博主的视频，涵盖了波动率、算法交易、信号处理等多个主题。
课程：介绍了 Coursera 上 Robert Shiller 的《Financial Markets》课程，以及 Andrew Ng 的机器学习和深度学习课程。还提到了一些与量化交易相关的课程和项目，如 RobotJames、HangukQuant 和 Euan Sinclair 的课程。
## chap04
推荐了一些播客资源，如 Tick Talk、Flirting with models、Mutiny fund 等，认为播客是非常重要的学习资源，能提供很多实用信息。
## chap05
交易平台和经纪公司：介绍了股票和其他资产类别的交易平台和经纪公司，如 IBKR、TD Ameritrade 等，以及数字资产领域的交易平台和经纪公司，如 Binance、Okx、Bybit 等。

##chap06
神经网络/机器学习/炒作：认为神经网络在量化交易中不是核心领域，不建议深入研究，推荐学习回归、非参数方法、树、GAMs 等。

##chap07
关键数学概念：强调了数学在算法交易中的重要性，推荐了一些学习数学的资源，如 Measure Theory、Econometrics、Stochastic Calculus、Probability Theory 等。
## chap08

优化（确定性和随机）：介绍了优化在量化交易中的应用，如估计隐含波动率、分布、自动化阿尔法发现、投资组合优化等。
## chap09
高频交易和做市：推荐了一些关于高频交易和做市的教科书和资源，如《High - Frequency Trading: A practical guide to algorithmic strategies and trading systems 2nd edition》《Inside The Black Box》等。
## chap10

其他波动率/衍生品资源：推荐了一些关于波动率和衍生品的资源，如 https://moontowerquant.com/select - content - from - the - quant - and - vol - community、Options Starter Pack 等。
## chap11

编码语言评论和资源：推荐使用 Python 进行量化交易研究，若想进行高频交易或做市，则需要学习 C/C++（在加密领域，Rust 更受欢迎）。
## chap12
项目：介绍了一些与深度学习相关的项目，但提醒读者要避免过度复杂化，应专注于回归和实用的方法。
## chap13
数据：介绍了数据的来源，包括付费数据的转储、交易所的免费数据和竞赛数据等，并推荐了一些数据提供商。
## chap14
GitHub 存储库：推荐了一些与算法交易相关的 GitHub 存储库，如 https://github.com/stefan - jansen/machine - learning - for - trading、https://github.com/barter - rs/barter - rs 等。
## chap15
轻松阅读：推荐了一些与金融行业相关的书籍，如《Liars Poker》《Flash Boys》《Irrational Exuberance》等。
## chap16
职业：介绍了量化交易的通常职业路径，包括就读顶尖大学、参加实习、学习面试问题等，并推荐了一些资源。
## chap17
套利指南：主要聚焦于数字资产领域的套利，介绍了不同类型的套利，如资金套利、三角套利、现货套利等，并提供了一些扫描套利机会的网站和资源。
## chap18
做市指南：介绍了做市的要点，包括边缘（准确预测中间价格和低延迟反应事件的能力）、价差（根据资产体积调整价差）和风险（避免复杂的库存平衡方程，因为模型中的相关性不一定成立）。
## chap19
配对交易指南：介绍了一些关于配对交易的资源，如@systematicls 的文章、作者博客上的相关文章、Github 上的配对交易策略等。
## chap20
季节性指南：提供了作者博客上关于季节性策略的概述链接，以及与 HangukQuant 共同撰写的关于季节性策略的论文和 PNL 曲线。
## chap21
动量指南：提供了作者博客上关于动量策略的资源链接。
## chap22
博客阅读：推荐了一些与量化交易相关的博客，如 The Quant Stack、The Quant Playbook、HangukQuant 等。
## chap23
Twitter 账号关注：列出了一些值得关注的 Twitter 账号，如 Vertox_DF、Ninjaquant_、BeatzxBT 等。

##chap24
如何学习这些材料：强调了学习方法的重要性，建议读者筛选相关内容、实施和讨论所学知识、避免构建不必要的工具或虚荣项目，以及注重从实践中学习。
## chap25
其他路线图：推荐了一些其他的量化交易资源列表，如 Vertox 的资源列表、Moontower 的资源列表（尤其是期权方面）、Moontower 的博客和在线作家、Options Starter Pack、QM 的教科书、QuantGuide 等。
