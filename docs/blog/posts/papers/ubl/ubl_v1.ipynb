{"cells": [{"cell_type": "code", "execution_count": 26, "id": "15067bf2", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from pandas.core.window import rolling\n", "\n", "def calculate_shadow_ratio(bars):\n", "    \"\"\"计算上下影线因子(归一化)\n", "    \n", "    按研报要求，标准化蜡烛上影线为当日上影线/过去5日上影线均值。标准化蜡烛下影线同。\n", "    \"\"\"\n", "    high = bars['high']\n", "    low = bars['low']\n", "    open_price = bars['open']\n", "    close = bars['close']\n", "\n", "    # 为避免除零错误，这里我们使用了一个技巧，即通过mask来排除可能除零的计算\n", "    # 无法计算时，设置为0，表明无信号\n", "    up_shadow_ratio = pd.Series(0, index=bars.index)\n", "    down_shadow_ratio = pd.Series(0, index=bars.index)\n", "\n", "    up_shadow = high - np.maximum(open_price, close)\n", "    rolling_up_shadow = up_shadow.rolling(5).mean()\n", "    mask = (rolling_up_shadow > 1e-8) & (~rolling_up_shadow.isna())\n", "    up_shadow_ratio[mask] = up_shadow[mask] / rolling_up_shadow[mask]\n", "\n", "    down_shadow = np.minimum(open_price, close) - low\n", "    rolling_down_shadow = down_shadow.rolling(5).mean()\n", "    mask = (rolling_down_shadow > 1e-8) & (~rolling_down_shadow.isna())\n", "    down_shadow_ratio[mask] = down_shadow[mask] / rolling_down_shadow[mask]\n", "\n", "    return up_shadow_ratio, down_shadow_ratio\n", "\n", "\n", "def calculate_williams_r_ratio(bars):\n", "    \"\"\"\n", "    计算变种威廉指标\n", "    \"\"\"\n", "    high = bars['high']\n", "    low = bars['low']\n", "    close = bars['close']\n", "    \n", "    wr_up = high - close\n", "    wr_down = close - low\n", "\n", "    rolling_wr_up = wr_up.rolling(5).mean()\n", "    rolling_wr_down = wr_down.rolling(5).mean()\n", "\n", "    up_mask = rolling_wr_up > 1e-8\n", "    down_mask = rolling_wr_down > 1e-8\n", "\n", "    # 与蜡烛上下影线的默认值不同，0.5更能表明无信号的含义\n", "    wr_up_ratio = pd.Series(0.5, index=bars.index)\n", "    wr_down_ratio = pd.Series(0.5, index=bars.index)\n", "\n", "    wr_up_ratio[up_mask] = wr_up[up_mask] / rolling_wr_up[up_mask]\n", "    wr_down_ratio[down_mask] = wr_down[down_mask] / rolling_wr_down[down_mask]\n", "\n", "    return wr_up_ratio, wr_down_ratio\n", "\n", "start = datetime.date(2009,1,1 )\n", "end = datetime.date(2020,4,30)\n", "barss = load_bars(start, end, 10)\n", "\n", "def calc_monthly(daily_factor, aggfunc, win=20):\n", "    dates = barss.index.get_level_values('date').unique().sort_values()\n", "    month_ends = dates.to_frame(name = \"date\").resample('BME').last().values\n", "\n", "    dfs = []\n", "\n", "    for date in month_ends:\n", "        date_ts = pd.Timestamp(date.item())\n", "        iend = dates.get_loc(date_ts)\n", "        istart = max(0, iend - win + 1)\n", "        start_ = pd.Timestamp(dates[istart])\n", "        end_ = date_ts\n", "        window_data = daily_factor.loc[start_: end_]\n", "\n", "        df = (window_data.groupby(level=\"asset\")\n", "                        .agg(aggfunc)\n", "                        .to_frame(\"factor\")\n", "        )\n", "        df[\"date\"] = date_ts\n", "        dfs.append(df)\n", "\n", "    df = pd.concat(dfs)\n", "    return df.set_index([\"date\", df.index]).sort_index()\n", "\n", "def calc_candle_up_std_factor(barss, win = 20):\n", "    up_shadow = barss.groupby(\"asset\", group_keys=False).apply(lambda x: calculate_shadow_ratio(x)[0]).sort_index()\n", "\n", "    return calc_monthly(up_shadow, \"std\", win)\n", "\n", "def calc_wr_down_factor(barss, win = 20):\n", "    wr_down = barss.groupby(\"asset\", group_keys=False).apply(lambda x: calculate_williams_r_ratio(x)[1]).sort_index()\n", "\n", "    return calc_monthly(wr_down, \"mean\", win)\n", "\n", "def calc_ubl_factor(barss, win = 20):\n", "    from scipy.stats import zscore\n", "\n", "    up_std = calc_candle_up_std_factor(barss, win)\n", "    wr_down = calc_wr_down_factor(barss, win)\n", "\n", "    # 截面zscore\n", "    z_scored_up_std_factor = up_std.groupby(\"date\").transform(zscore)\n", "    z_scored_wr_down = wr_down.groupby(\"date\").transform(zscore)\n", "\n", "    return z_scored_up_std_factor + z_scored_wr_down"]}, {"cell_type": "code", "execution_count": 48, "id": "0d50e442", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>factor</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>asset</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">2019-01-31</th>\n", "      <th>A</th>\n", "      <td>0.975</td>\n", "    </tr>\n", "    <tr>\n", "      <th>B</th>\n", "      <td>0.500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  factor\n", "date       asset        \n", "2019-01-31 A       0.975\n", "           B       0.500"]}, "metadata": {}, "output_type": "display_data"}], "source": ["dates = pd.bdate_range('2019-01-01', '2019-01-31')\n", "cols = [\"open\", \"high\", \"low\", \"close\"]\n", "df1 = pd.DataFrame([(2, 3, 1, 2)] * len(dates), index=dates, columns=cols)\n", "df1[\"asset\"] = \"A\"\n", "\n", "df2 = pd.DataFrame([(2, 3, 0, 0)] * len(dates), index=dates, columns=cols)\n", "df2[\"asset\"] = \"B\"\n", "\n", "barss = pd.concat([df1, df2]).set_index(\"asset\", append=True)\n", "barss.index.set_names([\"date\", \"asset\"], inplace=True)\n", "barss.sort_index(inplace=True)\n", "\n", "# display(calc_candle_up_std_factor(barss, 20))\n", "display(calc_wr_down_factor(barss, 20))"]}, {"cell_type": "code", "execution_count": 49, "id": "b46a42da", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.975)"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["arr = [1] * 20\n", "arr[0] = 0.5\n", "np.mean(arr)\n"]}, {"cell_type": "markdown", "id": "4068c78c", "metadata": {}, "source": ["## Alphatest"]}, {"cell_type": "code", "execution_count": 3, "id": "ce617cc9", "metadata": {}, "outputs": [], "source": ["# 研报回测期\n", "start = datetime.date(2009,1,1 )\n", "end = datetime.date(2020,4,30)\n", "\n", "def calc_upper_shadow_factor(bars: pd.DataFrame, win: int=20):\n", "    \"\"\"根据研报，UBL因子最终选择了上影线标准差作为成分。为了对比，这里我们也选标准差\"\"\"\n", "    up, _ = calculate_shadow_ratio(bars)\n", "\n", "    return up.rolling(win).std() * -1\n", "\n", "# alphatest(50, start, end, calc_upper_shadow_factor, (20,))"]}, {"cell_type": "code", "execution_count": 2, "id": "014a15ad", "metadata": {}, "outputs": [], "source": ["def calc_ubl_factor(barss: pd.DataFrame, win: int = 20):\n", "    \"\"\"计算ubl因子\n", "    \n", "    根据研报，ubl因子为zscore(std(up_shadow_ratio)) + zscore(std(wr_down_ratio))\n", "    \"\"\"\n", "    pass"]}, {"cell_type": "code", "execution_count": 4, "id": "6cf38fd4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Q1</th>\n", "      <th>Q2</th>\n", "      <th>Q3</th>\n", "      <th>Q4</th>\n", "      <th>Q5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2009-02-01</th>\n", "      <td>0.345573</td>\n", "      <td>0.230387</td>\n", "      <td>0.228000</td>\n", "      <td>0.124089</td>\n", "      <td>0.139869</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2009-03-01</th>\n", "      <td>0.049935</td>\n", "      <td>0.009118</td>\n", "      <td>-0.026737</td>\n", "      <td>-0.035118</td>\n", "      <td>-0.007954</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2009-04-01</th>\n", "      <td>0.110477</td>\n", "      <td>0.115158</td>\n", "      <td>0.117250</td>\n", "      <td>0.107950</td>\n", "      <td>0.205011</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2009-05-01</th>\n", "      <td>0.031488</td>\n", "      <td>0.073551</td>\n", "      <td>0.092930</td>\n", "      <td>0.118229</td>\n", "      <td>0.061097</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2009-06-01</th>\n", "      <td>0.047686</td>\n", "      <td>0.045705</td>\n", "      <td>0.372182</td>\n", "      <td>0.311275</td>\n", "      <td>0.042926</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2019-11-01</th>\n", "      <td>0.025295</td>\n", "      <td>0.091982</td>\n", "      <td>0.060381</td>\n", "      <td>0.148900</td>\n", "      <td>0.076818</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2019-12-01</th>\n", "      <td>-0.014981</td>\n", "      <td>-0.009306</td>\n", "      <td>-0.003421</td>\n", "      <td>-0.004252</td>\n", "      <td>0.012805</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-01-01</th>\n", "      <td>0.200449</td>\n", "      <td>0.145341</td>\n", "      <td>0.126821</td>\n", "      <td>0.207154</td>\n", "      <td>0.101577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-02-01</th>\n", "      <td>-0.087275</td>\n", "      <td>-0.018490</td>\n", "      <td>-0.037929</td>\n", "      <td>-0.084410</td>\n", "      <td>-0.115677</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-03-01</th>\n", "      <td>0.064967</td>\n", "      <td>0.077091</td>\n", "      <td>0.015217</td>\n", "      <td>0.076270</td>\n", "      <td>0.048838</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>134 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                  Q1        Q2        Q3        Q4        Q5\n", "2009-02-01  0.345573  0.230387  0.228000  0.124089  0.139869\n", "2009-03-01  0.049935  0.009118 -0.026737 -0.035118 -0.007954\n", "2009-04-01  0.110477  0.115158  0.117250  0.107950  0.205011\n", "2009-05-01  0.031488  0.073551  0.092930  0.118229  0.061097\n", "2009-06-01  0.047686  0.045705  0.372182  0.311275  0.042926\n", "...              ...       ...       ...       ...       ...\n", "2019-11-01  0.025295  0.091982  0.060381  0.148900  0.076818\n", "2019-12-01 -0.014981 -0.009306 -0.003421 -0.004252  0.012805\n", "2020-01-01  0.200449  0.145341  0.126821  0.207154  0.101577\n", "2020-02-01 -0.087275 -0.018490 -0.037929 -0.084410 -0.115677\n", "2020-03-01  0.064967  0.077091  0.015217  0.076270  0.048838\n", "\n", "[134 rows x 5 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "\n", "sys.path.insert(0, \"./docs/blog/posts/papers/ubl\")\n", "\n", "from monthly_factor_backtest import *\n", "\n", "barss = load_bars(start, end, 50)\n", "factor_data = (barss.groupby(level=\"asset\")\n", "                    .apply(lambda x: calc_upper_shadow_factor(x, 20))\n", "                    .droplevel(level=0)\n", ")\n", "\n", "returns = monthly_factor_backtest(factor_data, barss)\n", "returns"]}, {"cell_type": "code", "execution_count": 5, "id": "8b0dd194", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>年化收益率</th>\n", "      <th>年化波动率</th>\n", "      <th>夏普比率</th>\n", "      <th>最大回撤</th>\n", "      <th>胜率</th>\n", "      <th>月均收益率</th>\n", "      <th>月收益率标准差</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Q1</th>\n", "      <td>0.121581</td>\n", "      <td>0.343037</td>\n", "      <td>0.354427</td>\n", "      <td>-0.662513</td>\n", "      <td>0.522388</td>\n", "      <td>0.010132</td>\n", "      <td>0.099026</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Q2</th>\n", "      <td>0.160135</td>\n", "      <td>0.328977</td>\n", "      <td>0.486766</td>\n", "      <td>-0.649800</td>\n", "      <td>0.507463</td>\n", "      <td>0.013345</td>\n", "      <td>0.094967</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Q3</th>\n", "      <td>0.246906</td>\n", "      <td>0.406864</td>\n", "      <td>0.606853</td>\n", "      <td>-0.723604</td>\n", "      <td>0.552239</td>\n", "      <td>0.020576</td>\n", "      <td>0.117451</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Q4</th>\n", "      <td>0.158411</td>\n", "      <td>0.356178</td>\n", "      <td>0.444753</td>\n", "      <td>-0.570105</td>\n", "      <td>0.537313</td>\n", "      <td>0.013201</td>\n", "      <td>0.102820</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Q5</th>\n", "      <td>0.208155</td>\n", "      <td>0.315544</td>\n", "      <td>0.659670</td>\n", "      <td>-0.493057</td>\n", "      <td>0.574627</td>\n", "      <td>0.017346</td>\n", "      <td>0.091090</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       年化收益率     年化波动率      夏普比率      最大回撤        胜率     月均收益率   月收益率标准差\n", "Q1  0.121581  0.343037  0.354427 -0.662513  0.522388  0.010132  0.099026\n", "Q2  0.160135  0.328977  0.486766 -0.649800  0.507463  0.013345  0.094967\n", "Q3  0.246906  0.406864  0.606853 -0.723604  0.552239  0.020576  0.117451\n", "Q4  0.158411  0.356178  0.444753 -0.570105  0.537313  0.013201  0.102820\n", "Q5  0.208155  0.315544  0.659670 -0.493057  0.574627  0.017346  0.091090"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["calculate_group_statistics(returns)"]}, {"cell_type": "code", "execution_count": 6, "id": "a9c1d04e", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_cumulative_returns(returns)"]}], "metadata": {"kernelspec": {"display_name": "zillionare", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}