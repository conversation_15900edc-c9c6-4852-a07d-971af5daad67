---
title: "[0728] QuanTide Weekly"
seq: 第 2 期
slug: quantide-weekly
date: 2024-07-28
motto: 
category: others
tags:
  - others
  - story
  - weekly
lineNumbers: true
aspectRatio: 10/16    
---

## 本周要闻回顾

* 高频交易费率或将提升 10 倍
* 巴黎奥运开幕，奥运概念板块新鲜出炉
* 广东私募自查，量化交易、基金规模是否达标成是重点
* 纳斯达克大跌 3.64%，日经指数创 2021 年 10 月来最长连跌记录

## 下周重要日历

* 下周“降息”大潮开启
* 全球股市迎来超级央行周
* 本周将有三支...

## 本周精选

* 圣杯依然闪耀！基于短时 RSI 的均值回归策略
* 看得到，吃不到！7 年 2500 倍的多空策略
* 量化投资顶刊有哪些(一)？

<!--
今日有消息指出，监管起草了高频交易买卖费用从当前每笔 0.1 元提升至 1 元。对此，记者从业内获悉，确有量化私募收到指令，达到高频交易认定标准的流量费提高 10 倍，即从原费率每笔委托 0.1 元提升至 1 元。有量化私募表示，这笔费用由交易所统计，月度下发券商，每个季度再由券商向客户收取。当前已经有量化私募提前部署控制了相关风控参数。另有量化私募交易人士表示，手续费超过 5 倍以上，当下策略就没有超额了，从而达到限制高频量化的目的。早在今年 7 月 12 日，市场已有高频量化相关收费标准在市场流传，包括“双边换手率<4 倍/月，且撤单率＜40%，流量费每笔委托（不含撤单）1 元，每笔撤单费 5 元等”内容。彼时亦有量化私募参加征求意见相关会议。

【广东证监局组织辖内私募基金开展自查，量化交易、基金规模是否达标成自查重点】 7 月 22 日，中国证券监督管理委员会广东监管局（简称“广东证监局”）发布《关于组织开展辖区私募投资基金 2024 年自查工作的通知》（以下简称《通知》）。具体来看，《通知》要求私募基金管理机构自查的重点包括：私募基金管理机构在宣传推介、资金募集、投资运作等业务环节是否合规，登记备案、信息报送、信息披露等是否真实、准确、完整、及时内部管理和风险控制是否完善，是否存在基金产品逾期，是否开展量化交易，是否存在异地经营情形，是否存在不能持续符合登记备案条件的情形，是否从事与私募基金管理相冲突或者无关的业务等。记者注意到，近年来，广东证监局多次组织辖内私募投资基金开展自查工作，自查内容涵盖“募投管退”各业务环节及可能存在风险隐患的领域。（21 财经）

据上海证券报记者统计，在与巴黎奥运会相关的设施器材、配套设备、体育营销、文化衍生品等领域，均有 A 股企业参与“夺金”，其中就包括中集集团、舒华体育、艾比森、洲明科技、元隆雅图等多家上市公司。
星辉娱乐：西班牙人俱乐部球员将参加 2024 年巴黎奥运会】有投资者问，本俱乐部有球员参加巴黎奥运会吗？星辉娱乐在互动平台表示，西班牙人俱乐部致力于为西班牙奥运队输送优秀球员，俱乐部球员霍安·加西亚已入选西班牙国奥队大名单，将随队征战 2024 年巴黎奥运会。

泡泡玛特：印度尼西亚首家门店开业 境外门店数量已达到 100 家】 7 月 22 日，记者从公司方面获悉，泡泡玛特印度尼西亚的首家门店日前在雅加达南部商圈 Gandaria City 开业，该门店也成为泡泡玛特在海外及港澳台地区的第 100 家门店。今年以来，泡泡玛特全球化步伐正在加速，其相继在越南、意大利、印尼等国家与地区实现突破。据悉，泡泡玛特的法国巴黎卢浮宫店将在奥运期间开业。

纳斯达克 100 指数下跌 3%，创下自 2022 年 12 月以来最大跌幅。成分股特斯拉跌超 10%、Arm 控股跌 7.2%，英伟达跌 5.7%，谷歌 A 跌超 5%，Meta 跌 4.3%。

<!--

一位诗人在历史上是神圣的，在隔壁则是荒谬的。我们崇拜远方的神祇，却从不相信身边的奇迹

人生很短。不要用你的一生，去过别人的生活。

hinton

张益唐

-->

---

## 本周要闻回顾

* 财联社消息，监管起草了高频交易买卖费用从当前每笔 0.1 元提升至 1 元。有量化私募交易人士表示，手续费超过 5 倍以上，当下策略就没有超额了，从而达到限制高频量化的目的。<remark>当前对高频交易的认定是，每秒超过 300 笔或者全天申报及撤单不超过 2 万笔。</remark>
* 巴黎奥运会开幕，与此相关的设施器材、配套设备、体育营销、文化衍生品等领域，均有 A 股企业参赛，包括中集集团、舒华体育、艾比森、洲明科技、元隆雅图等多家上市公司。东方财富、同花顺等顺势推出奥运概念股。
* 21 财经消息，广东证监局组织辖内私募基金开展自查：私募基金管理机构在宣传推介、资金募集、投资运作等业务环节是否合规，是否存在基金产品逾期，是否开展量化交易，是否存在异地经营情形等，量化交易、基金规模是否达标成自查重点。
* 7 月 25 日，纳斯达克 100 指数下跌 3%，创下自 2022 年 12 月以来最大跌幅。日经指数本周继续下跌 5.9%，连跌 8 个交易日，创 2021 年 10 月来最长连跌记录。不过，美股周五全线上涨，道指更是周线 4 连涨。
* 北向资金持续卖出。近 2 周以来累计已减持 307 亿元，月内净卖额达 288 亿元。
* 央行罕见加场MLF操作 稳定月末流动性。7月25日，中国人民银行在月末时点新增一次中期借贷便利（MLF）操作，中标利率为2.3%，较前次下降20个基点。
* 证监会研究谋划进一步全面深化资本市场改革开放的一揽子举措。吴清开展专题座谈会，与 10 家在华外资机构、QFII 代表深入交流，听取意见建议。
  
<claimer>信息来源：财联社等，通过 Tushare.pro 接口综合汇编。</claimer>

---

## 下周重要日历

* 多家银行下周起降息。7月26日招商银行、平安银行调整了人民币存款挂牌利率表，最高下调幅度为30个基点。广发银行也将在下周下调银行存款。广发银行对记者表示，下周银行存款下调的幅度会与四大行调整幅度保持一致。除了存款之外，大额存单也在下周同步下调。
* 下周全球投资者将迎来超级周。首先，三大央行将公布利率决议。美联储将在周三召开议息会议。此外，几大科技巨头如微软、META、苹果等都将在这一周发布财报。
* 7月31日（周三）统计局将发布PMI指数。前值读数为49.5。8月1日，财新发布制造业PMI。

<claimer>信息来源：财联社等，通过 Tushare.pro 接口综合汇编。</claimer>
---

# 基于短时 RSI 的均值回归

<claimer>这篇文章发表于周二公众号上。获得超过 4000 次阅读。</claimer>

## 策略概要

所有人应该都已经熟悉 RSI 指标了。我们常用的 RSI 指标是基于 6,12 和 24 周期计算出来的。

$$
RS = \frac{\text{SMMA}(U,n)}{\text{SMMA}(D,n)}
$$

$$
RSI = 100\cdot\frac{\text{SMMA}(U,n)}{\text{SMMA}(U,n) + \text{SMMA}(D,n)} = 100 - { 100 \over {1 + RS} }
$$

但是 Larry Connors 认为，2 周期的 RSI 可以更好的反映市场趋势，很可能是技术指标中的圣杯。他把这个观点发表在 2008 年出版的《华尔街的顶级交易员》一书中。在此后的 Connor's RSI 指标中，streak 的 RSI 正是使用 2 周期来计算的。

基于这样的 RSI,Connors 给出了以下均值回归策略：

1. 标普 500 指数超过 200 日均线；
2. 标普 500 指数的 2 周期 RSI 低于 5；
3. 信号发出时，按收盘价买入；
4. 当标普 500 高于 5 日均线时卖出。

---

用交易员的话来讲，这是一个在**牛市**（指数大于 200 日均线）**短线回调**（RSI 低于 5）时的买入策略。

## 因子检验

首先 quantitativo 进行了单因子检验。检验方法是对对所有 2 天 RSI 收于 5 以下的标的进行买入并持有 5 天，再计算收益。

![75%](https://images.jieyu.ai/images/2024/07/factor-rsi-2-buy-and-hold-5.jpg)

这个统计包含了超过 21000 个标的，超过 250 万次事件（标普 500 收于 200 天均线之上）。测试中的亮点：

1. 当任何给定股票的 2 天 RSI 低于 5 并在牛市中持有 5 天时，买入其平均回报率为 3.3%;
2. 其中 60% 的事件获得正回报，每笔交易的预期回报为 9.8%;
3. 40%的交易是负面的，每笔交易的预期回报率为-6.6%;
4. 分布呈正偏。

quantitativo 还统计了反过来的情形，即如果我们在每只标的的 2 天 RSI 收于 5 以上时买入每只股票，并在牛市中持有 5 天的数据。我们将得到：

---

1. 当任何给定股票的 2 天 RSI 高于 5 时，买入其预期回报率为 0.3%;
2. 交易转为正值的可能性为 52%，预期回报率为 5.5%;
3. 交易转为负值的可能性为 48%，预期回报率为 -5.1%。

quantitativo 还对两次测试是否属于同一分布进行了假设检验，结果 p 值远低于 0.05，证明两个分布显著不同。因此，第一次测试中的因子是存在 Alpha 的。

## 策略回测

接下来 quantitativo 进行了策略回测。这里策略设计如下：

1. 使用 SPY 作为测试标的。
2. 当以下条件满足时，在下一次开盘时买入 SPY：
   1. 标普指数的 RSI(2) 收于 5 之下
   2. SPY 高于 200 日均线
3. 退出条件
   1. 当 SPY 收盘价高于前一天高点，则在下一次开盘时退出。
   2. 如果 SPY 收盘价低于 200 日均线

可以看出，回测策略与 Connor 提出的策略略有不同。为什么要进行这样的差异化？

这里的差异化，其实就是回测与实盘的差异。Connor 给出的策略更理想化，而 quantitativo 的验证策略则更加接近实盘。这是我们做策略时一定要考虑的。

首先，尽管我们可以用标普 500 来回测，但在实盘中，更切合实际的方法是购买对应的 ETF。这里的 SPY 就是以标普 500 为标的的 ETF。

---

第二，Connor 的策略是以收盘价买入。如果你的回测系统不够精确的话，最好是以次日开盘价买入。当然，如果你的回测系统和行情数据精确到分钟级，那么在国内，也可以利用集合竞价前一分钟的收盘价计算信号，再以集合竞价买入。

退出条件的差异，可以看成是 quantitativo 对原策略的一个优化。不过，我并没有看出来这个优化的意义。它的背后似乎并不存在任何交易上的原理支撑。看起来，这更像是 quantitativo 通过数据做出的过拟合。

!!! question
    quantitativo 在本次实验中，使用了长达 25 年的数据进行测试。如果经过这么长的时间回测，数据表现仍然很好，是否就可以说不存在过拟合？我很想知道你们怎么看。

那么，quantitativo 的实验结果又是如何呢？

在 SPY 上的测试简直就是灾难。在整个回测期（25 年）中，使用 SPY 交易此策略提供了 67% 的回报。主要原因是交易次数太少，仅执行了 157 笔交易。

![75%](https://images.jieyu.ai/images/2024/07/performance-qqq-tqqq.jpeg)

---

接着 quantitativo 改用了纳斯达克 100 指数 ETF（QQQ）和三倍杠杆纳斯达克 100ETF（TQQQ）。结果表明，在 TQQQ 上表现不错（见前图）:

夏普比率分别达到了 2.3（QQQ）和 1.92（TQQQ），对指数标的而言，是相当不错的指标（特别是与 A 股对比）。

## 改进策略：增加因子

在前一次实验的基础上，quantitativo 增加了资产组合。

他们把资金分成十份 (10 slots)，用于购买前一天 RSI 收于 5 以下的标的；如果 universe 中有 10 支以上的标的触发了入场信号，将按市值进行排序，优先考虑市值较小的股票。退出条件改为收盘价低于标的 200 日均线。

此外，他们还限制只交易流动性较好的标的：
1. 只交易过去 3 个月内完全没有停牌的标的
2. 如果标的过去 3 个月日均成交量中位数不足资金份额的 20 倍，则不纳入

![75%](https://images.jieyu.ai/images/2024/07/new-experiments-1.jpg)

---

!!! tip
    实际上，在这里 quantitativo 已经引入了另一个因子，小市值因子，只不过它的权重比较低--是在 RSI 触发之后才应用权重因子。

这次的结果很不错，整体年回报率达到 17.8%，是基准的 3 倍。但也存在问题，即所有这些都是在前 8-9 年实现的。该策略在 2008 年后停止执行，并从那时起输给基准。

原因何在？通过分析 25 年回测期间进行的 11,380 笔交易，quantitativo 发现了很多退市。这种幼稚方法的问题在于，该策略优先考虑小盘股（在通过流动性过滤器后），它们退市的概率为 +70%。

## 第二次改进：降低退市风险

![75%](https://images.jieyu.ai/images/2024/07/new-experiment-2.jpg)

quantitativo 再次改进了策略，这次的改进是将 universe 限制在只交易大型和超大盘股，这些股票的退市概率较低（分别为 35%和 9%）。

---

这一次效果非常明显。策略的年化回报达到了 23.9%，是同期基准的 4 倍，夏普达到 1.23%，最大回撤为 32%，几乎只有标普的一半。但是，之前就存在的一个问题，交易次数过于频繁仍然存在。现在一年仍然会交易 461 次。

<!-- quantitativo 在对 25 年回测期间进行的 11,380 笔交易进行分析之后，发现了很多退市，但是，这个时间本可以避免 -->
## 第三次改进：减少 slots

之前的实验使用了 10 个 slots，这可能是导致交易次数过多的主要原因。于是，quantitativo 将同时持有的标的数减少到 2 支。

现在，交易笔数由 461 次/年下降到 90 笔/年。并且实现 30.3%的年回报率，是基准的 5 倍。

## 结论

这篇文章介绍了一个基于短时 RSI 的均值回归策略，并在最后，给出了年化达 30.3%的一个实现（未考虑滑点和交易手续费）。

这个策略的内核是短时 RSI，尽管这个指标发明以来已经超过 45 年，但回测结果表明，只要你研究一件事足够深入，就很可能取得成功。

!!! quote
    It's not that I'm so smart; it's just that I stay with problems longer. -- Albert Einstein

这个方案已经值回你的阅读时间，但更为重要的是，我们讨论了策略发现的一般流程和优化角度。

---

我们再梳理一次，作为本篇的结束语：

1. RSI 代表着潮汐和回归，其背后是人性，因此它永远不会过时。
2. 多因子策略也可以以某个因子为主，在交易过程中，以限制条件的方式引入其它因子。
3. 文中给出了判断标的流动性强弱的方法，你也可以将其作为一个因子。
4. 研发策略的步骤往往从单因子检验开始，再编写简单回测，然后根据回测结果一步步优化。
5. 在优化过程中，quantitativo 先是使用 ETF，然后改用了 10 个 slots，最终回到 2 个 slots 的方案。

---

# 看到吃不到！7 年涨了 2500 倍

这一周我们继续探索 Alphalens 因子分析框架的使用。因子与收益之间的关系很少是线性的，但Alphalens本身就是一个线性分析框架，以揭示因子与收益之间的线性关系见长。它支持的三种分析方法是：

* Regression （回归法）
* IC/Rank-IC （相关系数、秩相关）
* Tiered Regression (分层回溯)

都是基于线性回归或者准线性（相对于svm、nn等而言）。因此，如何让Alphalens在因子检验中，能够真正揭露出因子与收益之间的关系，是一个比较有技巧的活儿。

我们在7月26日发表的视频号中，探索了如何运用 plot_quantile_statistics_table 和 mean period wise return by Factor quantile图来抽丝剥茧，重构因子，最终完美地揭示因子与收益之间的线性关系。

这里我们也对视频内容进行一个摘要。

在实验中，我们先是使用了400个ticker，1000天的数据。在实验方向确定后，我们扩大到2000个ticker, 2000天的数据，以排除偶然性。

---

我们要探索的因子是6期的RSI。作为因子，一般要求因子值越大，收益越高；但RSI恰好是反向的，一般认为，RSI越大，就越需要减仓。因此，我们实际构建的因子是:

$$

factor = 100 - RSI

$$

## 第一次实验


简单用Alphalens的默认值跑一下，我们得到以下结果：



![](https://images.jieyu.ai/images/2024/07/cum-returns-by-quantile.jpg)
<cap>多空组合的收益情况</cap>

![](https://images.jieyu.ai/images/2024/07/cut-by-quantile.jpg)
<cap>默认参数下的因子分层情况</cap>

---

![](https://images.jieyu.ai/images/2024/07/nprw-by-quantiles.jpg)
<cap>by quantiles分层下的mpwr图</cap>

看收益看不出什么问题。如果你是做纯数据挖掘，就会觉得这也没什么，反正这个因子也是盈利了呀，对不对？

但如果我们仔细看分层图，就会发现，这次实验没有任何意义。因为第一层的因子值从0跨到78.9，最后一层因子值，从22跨越到了97。

看分层收益均值图，会发现因子与收益之间没有线性关系。Alphalens只能做线性分析，在这种情况下，Alphalens又如何能揭示因子与收益之间的因果关系呢？

## 第二次实验

Alphalens的默认分层方式是by quantiles。我们能运用这种方式来分层的前提，是因子本身是均匀分布的。而RSI不是。

除了RSI之外，还有很多例子不适用by quantiles分层。比如，如果因子是语料情感类的，那么因子大概是0~5之样的标签。这种情况下，也不适合按quantiles分层。

---

Alphalens提供另一种分层方式，即by bins。我们提供的分箱是[0,10,20,30,70,80,90,100]。这里我们有一个假设，即我们对中间的RSI不感兴趣，认为它们不会提供交易信号。

现在分层的结果就很合理了：

![](https://images.jieyu.ai/images/2024/07/cut-by-bins.jpg)
<cap>by bins分层情况</cap>

但是我们如果看一下 mean period wise return by Factor quantile图，就会发现，因子与收益之间镜像的线性关系：

![](https://images.jieyu.ai/images/2024/07/nprw-by-bins.jpg)


如果Alphalens能做多第4组，做空第1组，这样的策略结果应该不错。但是Alphalens并不能这么做。需要我们修改因子。

---

我们的方法是，抛弃掉因子值大于50的那一部分，再让Alphalens来分析因子与收益之间是否存在关系。

## 第三次实验

这次我们得到了非常完美的线性关系：

![](https://images.jieyu.ai/images/2024/07/rsi-filtered-50-mpwr.jpg)
<cap>过滤掉部分因子</cap>

在这种情况下，可以认为，Alphalens的分析结果是有效的。

当然收益也很好看。

![](https://images.jieyu.ai/images/2024/07/boost-returns.jpg)

过于好看了。能够超过这个策略的，只有涨停板因子。

---

## 关于收益的讨论

一般来说，当我们看到这么好的收益时，就应该认为是实施步骤出现了问题。 在我们的实验中，是否存在实施问题呢？因子分析比较复杂，这就是为什么我们要尽可能地使用框架的原因。用对了框架，再离奇的结论也要接受。

那么，这个收益我们能否兑现？只有极少数人能够兑现这个收益。因为它的大部分收益是通过做空第一组来实现的。在我们的算法中，第一组实际上就是RSI大于90的那一组。

这一组的股票，往往是涨停股了，平常融券不易，此时融券就更难。但是，从今年以来披露的一些违规案例来看，有些机构先拉涨停板，再融券做空，这是充分利用了我们这里因子分析的结果：即RSI>90时，做空胜算很大。

所以，只有极少数人能兑现这么高的收益。

随着各种交易规则的完善，之前被这些人割走的利润，现在将回到市场中。

## 结论

Alphalens无法揭示复杂的非线性关系，这是由于它的因子检验方法所决定的。所以，对一些因子与收益之间不存在简单线性关系的因子，我们需要对它进行适当的变换，以适合分析框架。

否则，无论你的因子是好是坏，都不可能得到正确的结果。


---

# 量化投资顶刊有哪些(一)？

1. <red>Journal of Finance</red> 创刊于1946年，是由美国金融协会（American Finance Association）主办的一份顶级学术期刊。它不仅吸引了世界各地顶尖学者的投稿，也是衡量学术成就和职业发展的重要指标。许多诺贝尔经济学奖得主的早期工作都曾在该期刊上发表，比如作为现代金融基石的现代资产组合理论（Markowitz）、资本资产定价模型(William Sharpe)、MM定理（Merton H. Miller）、有效市场假说（Eugene Fama）等。
2. <red>Journal of Financial Economics</red>. Rolf Banz， 1981年发表《The relationship between return and market value of common stocks》（即小市值因子），就是选择的这一家。2015年Famma发表五因子模型，也是这家期刊。
3. <red>Review of Financial Studies</red>，由牛津出版，是一本同行评审学术期刊。它在2020年的影响因子为5.814，在商业、金融类排名5/110。《Market Liquidity and Funding Liquidity》一文最早就发表在这里。在google学术上，它有超过6500次引用。
4. <red>Journal of Portfolio Management</red>，专注于投资组合管理的实践和理论，包括量化策略和风险管理。
5. <red>Journal of Financial and Quantitative Analysis</red>，简写JFQA，发表金融经济领域理论和实证研究。
6. <red>Journal of Empirical Finance</red>，强调实证研究，包括市场效率、资产定价和投资策略的实证分析。
7. <red>The Journal of Trading</red>，关注交易策略、市场动态和交易技术的期刊，不过已停刊。同类型的杂志有<red>《The Traders' Magazine》</red>。
