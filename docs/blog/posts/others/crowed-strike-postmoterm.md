---
title: 全球Windows机器蓝屏，作为量化人，我的检讨来了
slug: crowed-strike-postmoterm
date: 2024-07-20
img: https://images.jieyu.ai/images/2024/07/bsod.jpg
category: others
tags:
  - others
  - story
lineNumbers: true
---

昨天下午，微软给大家放了个假。Windows又双叒死机了。不过，这一次不是几台机器，而是全球大范围宕机。这一刻，大家都是“正蓝旗”。

现在根本原因已经找到，绝大多数人的机器都已修复。根本原因在于，一家名为CrowdStrike的安全公司在例行更新时，部署了错误的配置文件到Windows上。

这个错误也是由于另一个比较少见的错误引起的。CrowdStrike软件使用了微软部署在美国中部的云服务Azure，但刚好这个数据中心出现了异常。

![](https://images.jieyu.ai/images/2024/07/crowd-strike.jpg)

这个异常就导致了CrowdStrike在获取配置时，导入了错误的配置信息。这些错误信息下发后，软件终端基于错误的配置运行，就会在Windows终端额外加一个名为csagent.sys的驱动，而这个驱动存在bug，会导致系统陷入蓝屏死循环。

这个错误导致航司、银行和交易所受到重大影响，后面是否会面临巨额赔偿，还不清楚，但微软股价已受严重冲击。

目前还不清楚国内有多少从事交易的机构和个人受影响，但是，这也给我们量化人提了一个醒，你构建的交易系统，它安全可靠吗？我们应该如何构建自己的量化交易系统，使得它即使遇到类似的问题，也能保持稳定运行？

这里提几条建议。

![](https://images.jieyu.ai/images/2024/07/CI-CD-process.png)

第一，一定要达成高覆盖的单元测试和CI/CD流程。一定要意识到任何软件、硬件系统都是有bug的。在你构建量化交易系统时，一定要达成高覆盖单元测试和CI/CD流程。

单元测试不仅仅是在我们开发阶段帮助我们确保各个模块的功能正确，更重要的是，它设置了一组基准，可以帮助我们确定在时刻发生变化的环境下，系统的各项功能仍然满足基准运行要求。

正如这次CrowdStrike案例所显示的那样，即使你的交易系统并没有升级（就像这次的Windows）,但交易系统依赖的第三方组件（比如数据源，Pandas或者Numpy等）仍然可能升级。我们的交易系统在接受任何升级前，都要确保升级后的系统，仍然完全能通过我们所有的测试用例。

像CrowedStrike这样的软件，其实他们平常的测试也是很严格的，但为什么还会出现这样的故障？这里固然有比较偶然的原因（这次是Azure的故障引起），但是，很可能CrowedStrike的测试没有经过CI/CD的覆盖。只有实现了CD，这样才能保证连部署也被测试覆盖到，才会尽量减少错误。

传统上，量化团队都是金融专业的人领导的，他们可能缺乏软件工程的经验，不太懂测试、CI/CD这些专业知识，正因为这样，我写完《Python高效编程实践》这本书之后，特地请了两位金融界的大咖来推荐。因为自己做量化金融有好多年了，知道这个领域，非常需要系统化的软件工程方法来确保软件质量。

![](https://images.jieyu.ai/images/hot/mybook/girl-reading.png)

第二，关闭一切自动更新。生产环境下一切自动更新都是非常危险的，必须关闭。只有经过严格测试的更新，才能应用。

第三，更新系统时一定要使用灰度部署。

在部署上，CrowedStrike这次也犯了一大错误，就是没有实现灰度部署。实际上，安全软件权限很高，一旦出错，往往就会引起很严重的故障。因此，灰度部署就格外重要。

如果CrowedStrike实现了灰度部署，比如，一开始只部署1%的机器，并且监控升级后的情况（收集数据是灰度发布的一部分），然后在没有错误报告的情况下，再逐步扩大推送范围，就完全可以避免出现这么重大的事故。

灰度发布同样适用于量化系统。2012年8月1日，骑士资本在纳斯达克交易所部署了一个新的交易软件，但是由于没有充分测试，该软件在激活时触发了一系列错误的交易指令，导致公司在45分钟内损失了约4.4亿美元。最终导致了它被Jefferies Group收购。

<div style='width:75%;text-align:center;margin-bottom:1rem'>
<img src='https://images.jieyu.ai/images/2024/07/knight-capital-case.jpg' style="box-shadow:0 2px 5px rgba(0,0,0,0.3)">
<span style='font-style:italic;font-size:0.8rem'>骑士资本案例报告</span>
</div>

事后分析，如果正确地实施了灰度发布，完全可以避免这样的错误。

>如果有杠精：这件事比较复杂。一言以蔽之，不是没有实施灰度发布，而是没有正确地实施灰度发布。


据说做期货的，往往是90%的时候都在赚钱，但就是不到1%的极端情况，让你跳了楼。

第三，构建可控的系统。

![](https://images.jieyu.ai/images/2024/07/i-robot.jpg)

如果你的交易信号系统构建在AI模型之上，那么，风控模型就一定不要构建在黑盒子之上，一定要设置熔断机制，到点无条件地止损（当然这会引起其它家的量化也跟进止损，但换个角度，如果你跑得太晚，那么被埋的就是你自己）。

第四，再先进的系统，也不能无人值守。即使有了全自动的量化系统，也不要把手工交易员都裁了。如果你去看三峡大坝的发电厂，你会发现，发电是高度自动化的，但电脑显示屏前的值守人员，仍然会严格倒班。



