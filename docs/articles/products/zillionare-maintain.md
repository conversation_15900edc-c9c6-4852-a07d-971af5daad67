---
title: Zillionare 2.0维护指南
slug: zillionare-2.0-maintaining-guid
---

## 行情数据同步原理

在Zillionare安装后，由于多方面的原理，数据库中的数据是不齐全的，需要经过一个维护过程。这个维护过程及其工作原理如下：

在Zillionare的docker容器构建时，我们只打包了2022年1月到2023年2月的30分钟及以上级别的数据，供回测研究使用。因此，在您安装之后，数据库中的数据未能覆盖全面。一个全面覆盖的数据，应该包括从2005年1月起的所有分钟线及以上级别（即5,15,30,60分钟、日线、周线、月线）。

![](https://images.jieyu.ai/images/2024/02/data-sync-pointer.jpg)

我们通过epoch_start，sync head，sync tail指针（在redis数据库中，db1）及当前时间来确定已经同步的数据范围，和需要同步的数据范围，见上图。在我们发布zillionare的容器时，我们将sync head设置为了epoch_start，因此，一旦您配置了聚宽账号，数据同步将优先补齐从2022年12月30日之后到现在的数据。等到这部分数据追赶完成之后，您可以修改redis数据库中的头指针，使之与influxdb中数据的实际起点重合。这样，Zillionare将利用您每天剩余的quota来向后补齐数据，只到数据补齐到epoch_start(即2005年1月4日为止)。您也可以在同步一定量的数据之后，如果不需要更早的数据了，可以将该指针再次改回到epoch_start以结束同步。

!!! info
    这样设计的原因是因为要发挥Zillionare回测框架的优势，必须使用分钟线行情数据进行撮合。但分钟线实在太大了。

这是您需要知道的关于数据维护的第一件事。其次您需要知道的是Zillionare是如何存储数据和读取的。

为了实时和准确地更新数据，Zillionare的数据同步分两部分，一是已经收盘的数据，这些数据被认为是准确的、不再修改的，因此记录在influxdb中，是持久化的。未收盘的数据都是通过已收盘的更低级别周期的数据实时合成的。

!!! info
    为了保证已收盘数据是准确的，Zillionare的数据同步时间设置在夜里2点左右 -- 一般来说，上游数据此时已经完全了校准 -- 开始，并且同样的数据会拉取两次。只有两次的数据完全相同，才会保存。

也就是，以交易日15：00之前来看，昨天的日线在influxdb中，上一周的周线、上一个月的月线在influxdb中。但今天的日线、这周的周线等，都需要实时计算和更新。更新来自1分钟数据，1分钟数据缓存在redis中。这个1分钟数据会在盘中实时更新，并触发其它周期的unclosed数据的计算。

如果安装zillionare的时间在盘后，则在初次运行时，redis中是没有分钟线及所有分钟级别的行情数据的。此时zillionare-omega在日志上会报一些错误。这些错误可以忽略。从部署之日后的午夜（需要交易日）起，会有多个下载任务，待数据补齐后，就不再会出现这些问题。

部署后的第一个交易日完成后，上述错误应该消失，但此时week, month的unclosed bay能否计算正确，取决于quota。如果quota够同步1周的分钟和日线，则week bar正确；如果能同步1个月的分钟和日线，则月bar正确。否则，还需要等待。

