---
title: 大富翁量化24课编排说明
slug: how-the-course-are-composed
date: 2024-01-04
category: 课程
tags: 
    - 课程
---

## 01 课程内容
本课程涵盖了从获得数据，到数据预处理、因子提取与分析、回测、可视化到实盘的全流程，介绍了众多量化必备库的用法，包括：
### 如何获取数据
我们会介绍akshare, tushare, jqdatasdk这些常用库，也会介绍机构在用什么数据库
### Python金融数据分析
    - numpy
    - pandas
    - scipy（重点stats和signal）
    - scikit-learn （preprocessing, metrics等）
    - statsmodel（ECDF，经验概率分布）
    - statistics （python内置统计库）
    - zigzag （顶底分析，机器学习标注及形态检测）
    - ckwraps（一维数据聚类，用以平台检测等）
    - talib和技术指标（RSI, ATR，均线）

### 因子分析方法
    - alphalens
    - jqfactor

### 绘图与可视化
    - matplotlib
    - plotly
    - pyecharts
    - seaborn
### 回测
    - backtrader（回测框架）
    - Zillionare (回测框架)
    - empyrical （策略指标分析）
    - quantstats (回测指标可视化)
### 实盘
    - easytrader（基于键鼠模拟）
    - ptrade（ptrade实盘接口）
    - emt（东财文件单）
    - qmt（qmt实盘接口）
### 其它
    - sympy （符号数学运算，论文神器）
    - pyfolio (风险、因子分析)
    - ta (时序因子库)

内容之广，深度之深为市面上仅见。学完这门课，您将掌握量化交易实施的全过程，并且拥有坚实的数据分析能力、单因子分析技能、技术形态分析能力，具备机器学习预备知识、概率与统计基础知识，具备阅读研报和学术论文并复现的能力。

## 课程特点

### 内容特点

一是实战性强，尽管课程有一定的理论深度（溯源了多个策略、因子的理论来源、涉及统计概率和行业金融学），在讲解理论背景后，重点在于联系实际，告诉学员论文中的数学公式，在Python中由哪个库、哪个方法来实现，绘制出来的图是什么，一般在什么量化场景下使用，能够解决什么问题。

技术形态分析和择时也是实战的一个重点。课程中讲解的算法，基于统计概率和机器学习理论，算法的自适应能力强，形态捕捉准确，有较强的实用价值，对于波浪理论的程序化有非常实际的价值。


第二，代码质量高，注重规范和文档，对性能挖掘到极致（授课人开发的开源量化框架，完全使用Python，实现了30亿条数据的高速存储与访问），多数代码可以直接使用。

第三，授课人知识面广，能融汇贯通。比如在介绍大富翁的行情数据，在解释为什么我们使用32位浮点数而不是64位浮点数来存储OHLC数据时，我们联系了深度学习性能优化方面的最新进展：

!!! quote
    我们使用np.float32来存储OHLC这些数据，只对成交额、成交量使用np.float64。使用更小的字节数来存储数据，是当前AI性能优化的重要方向。大约2020年学界开始使用int16来压缩深度学习模型，而最近对大模型的压缩中，甚至开始使用int8。

又比如，很多朋友读过我们[基于深度学习的量化策略如何实现归一化](http://www.jieyu.ai/blog/2023/12/16/%E5%9F%BA%E4%BA%8E%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E7%9A%84%E9%87%8F%E5%8C%96%E7%AD%96%E7%95%A5%E5%A6%82%E4%BD%95%E5%AE%9E%E7%8E%B0%E5%BD%92%E4%B8%80%E5%8C%96/)的文章。多数人认为这篇文章颇有深度。这篇文章实际上来自于我们课程的第十三课。

### 编排特点
课程一般以思维导图开始，勾连出全课知识点，比如：

![](https://images.jieyu.ai/images/2023/07/lesson13-outline.png)

### 实验环境

课程在线实验环境由一个256GB内存，192cpu的集群提供，学生无须准备实验环境和数据。在线实验环境提供了30亿条行情数据、实时行情数据和在线回测能力，还可以进行模拟盘测试。

一个好的策略，往往需要通过几轮牛熊周期的测试。在我们的实验环境里，准备了18年的数据。这是任何通过CSV来准备数据的课程做不到的。


