---
hide:
    - title
title: 课程简介
slug: introduction-to-24-lectures
category: 课程
tags: 
    - 课程
---

## 1. 简介

本课程是量化交易的入门课程，它面向打算进入量化交易领域的学生、程序员和正在从事主观交易的机构投资者和个人投资者。

课程涵盖了量化交易的全流程，即如何获取数据，如何考察数据的分布、关联性，因子和模式的发现和提取，如何编写策略、进行回测和评估，最终将策略接入实盘。

学习完成本课程之后，您将会对量化交易有全面和系统的了解，能够独立实施量化策略的开发、调试、回测及实盘交易，并且能评估和改进自己的策略。您将有能力复现论文、经典量化交易策略或者实现策略思想。如果自己已经有了成功的交易经验，则将有如虎添翼之感。

课程媒介为录播视频、Notebook 文稿和私课答疑辅导。文字稿部分约 40 万字节。本课程将为学员提供可运行这些 Notebook 的实验环境，在该实验环境中：

* 192 核 CPU 和 256GB 内存（学员共享）
* Jupyter Lab 策略开发环境
* 2005到2023年的A股全部分钟级数据（超过 30 亿条，全部为商用正版数据）
* 回测服务。您可以立即编写策略并运行回测
* 仿真交易。本环境中可提供仿真交易，供您检验自己的策略

这是一门比较硬核的课程，我们在内容编排上做到了顺序讲述、层层递进、前后照应、取舍得当。在内容上，还有许多其它地方难得一见的知识点，我们列举一二：

!!! question
    1. A 股报价的最小单位是分。很多情况下，我们需要将小数四舍五入到百分位。2 元以下的个股出现舍入误差时，我们将承受 0.5%的损失。如果每天进行一次这样的交易，年化损失会达到惊人的 247%！可是，Python的四舍五入方法，它是正确的吗？
    2. 有人说回测中要使用后复权，这个结论是正确的吗？你将如何证明？
    3. 如果您的策略在回测中得到夏普是 2，一般而言，这是个不错的策略。但在实盘中，它开始回撤了，夏普也在变差。当回撤达到多少时，就可以断定，策略的运行基础已不存在，必须中止实盘（其它人可能告诉您，量化程序一旦运行，就必须扛过去）？
    4. 如果此时沪指下跌 4%。能否基于过去 1000 个交易日的数据，从统计学的原理推断出继续下跌的概率为多大？换言之，此时是否可以抄底买入了？

## 2. 课程大纲及编排说明

![](https://images.jieyu.ai/images/2023/10/cheese-course-brochure-1.png)

![](https://images.jieyu.ai/images/2023/10/cheese-course-brochure-2.png)

![](https://images.jieyu.ai/images/2023/10/cheese-course-brochure-3.png)

![](https://images.jieyu.ai/images/2023/10/cheese-course-brochure-4.png)

![](https://images.jieyu.ai/images/2023/10/cheese-course-brochure-5.png)

![](https://images.jieyu.ai/images/2023/10/cheese-course-brochure-6.png)

!!! tip
    更详细的课程大纲见 [这里](articles/coursea/24lectures/intro.md)

## 3. 量化知识体系与本课程定位

量化交易不仅在国内是新生事物，就连从它在华尔街占据主流地位到现在，也不过 20 多年历史。因此，关于量化交易，很少看到体系化的知识结构梳理。

我们根据自己的经验，结合国内外同类课程、同行交流的结果，通过对主流量化框架、量化常用库的梳理和对重要论文的阅读梳理，总结出如下学习<span id="roadmap">路线图</span>：

![](https://images.jieyu.ai/images/2023/10/cheese-course-roadmap.png)

在这个路线图中，最下面一层可以看作是学习量化的前置条件。关于 Python 基础，我们有较好的英文教材可以提供。该教材比较简练，不需要花很多时间就能学完，并且与我们的课程能很好衔接。工具部分可以在学习过程中，边练边学。数学部分只要达到过这个基础即可，在我们的课程中，会带领大家复习相关的内容。

最上面一层，您可以在入行之后，根据自己的目标，有选择性地深入学习。

## 4. 课程注册流程

* 联系 quantfans_99 （宽粉）进行咨询，获得下单链接
* 通过平台购买课程
* 购买完成当日，助教（宽粉）开通课程环境账号，将课程登录网址、账号密码发送给学员
* 助教建立课程辅导群，邀请老师和学员加入
* 学员开始学习，学习中遇到问题，通过微信群提问，私课班当天答疑。其它班通过微信群答疑。
* 课程视频仍可永久观看。服务器资源根据套餐决定。

课程实验环境如下：

 ![66%](https://images.jieyu.ai/images/hot/course/academy.jpg) 
