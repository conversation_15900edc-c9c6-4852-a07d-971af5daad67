<!DOCTYPE html><html><head>
      <title>课程简介</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:////Users/<USER>/.vscode/extensions/shd101wyy.markdown-preview-enhanced-0.8.13/crossnote/dependencies/katex/katex.min.css">
      
      
      
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:<PERSON>solas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}@font-face{font-family:"Material Icons";font-style:normal;font-weight:400;src:local("Material Icons"),local("MaterialIcons-Regular"),url("data:application/x-font-woff;charset=utf-8;base64,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") format("woff")}.admonition{box-shadow:0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12),0 3px 1px -2px rgba(0,0,0,.2);position:relative;margin:1.5625em 0;padding:0 1.2rem;border-left:.4rem solid rgba(68,138,255,.8);border-radius:.2rem;background-color:rgba(255,255,255,.05);overflow:auto}.admonition>p{margin-top:.8rem}.admonition>.admonition-title{margin:0 -1.2rem;padding:.8rem 1.2rem .8rem 3.6rem;border-bottom:1px solid rgba(68,138,255,.2);background-color:rgba(68,138,255,.1);font-weight:700}.admonition>.admonition-title:before{position:absolute;left:1.2rem;font-size:1.5rem;color:rgba(68,138,255,.8);content:"\E3C9"}.admonition>.admonition-title:before{font-family:Material Icons;font-style:normal;font-variant:normal;font-weight:400;line-height:2rem;text-transform:none;white-space:nowrap;speak:none;word-wrap:normal;direction:ltr}.admonition.abstract,.admonition.summary,.admonition.tldr{border-left-color:rgba(0,176,255,.8)}.admonition.abstract>.admonition-title,.admonition.summary>.admonition-title,.admonition.tldr>.admonition-title{background-color:rgba(0,176,255,.1);border-bottom-color:rgba(0,176,255,.2)}.admonition.abstract>.admonition-title:before,.admonition.summary>.admonition-title:before,.admonition.tldr>.admonition-title:before{color:#00b0ff;content:"\E8D2"}.admonition.hint,.admonition.tip{border-left-color:rgba(0,191,165,.8)}.admonition.hint>.admonition-title,.admonition.tip>.admonition-title{background-color:rgba(0,191,165,.1);border-bottom-color:rgba(0,191,165,.2)}.admonition.hint>.admonition-title:before,.admonition.tip>.admonition-title:before{color:#00bfa5;content:"\E80E"}.admonition.info,.admonition.todo{border-left-color:rgba(0,184,212,.8)}.admonition.info>.admonition-title,.admonition.todo>.admonition-title{background-color:rgba(0,184,212,.1);border-bottom-color:rgba(0,184,212,.2)}.admonition.info>.admonition-title:before,.admonition.todo>.admonition-title:before{color:#00b8d4;content:"\E88E"}.admonition.check,.admonition.done,.admonition.success{border-left-color:rgba(0,200,83,.8)}.admonition.check>.admonition-title,.admonition.done>.admonition-title,.admonition.success>.admonition-title{background-color:rgba(0,200,83,.1);border-bottom-color:rgba(0,200,83,.2)}.admonition.check>.admonition-title:before,.admonition.done>.admonition-title:before,.admonition.success>.admonition-title:before{color:#00c853;content:"\E876"}.admonition.faq,.admonition.help,.admonition.question{border-left-color:rgba(100,221,23,.8)}.admonition.faq>.admonition-title,.admonition.help>.admonition-title,.admonition.question>.admonition-title{background-color:rgba(100,221,23,.1);border-bottom-color:rgba(100,221,23,.2)}.admonition.faq>.admonition-title:before,.admonition.help>.admonition-title:before,.admonition.question>.admonition-title:before{color:#64dd17;content:"\E887"}.admonition.attention,.admonition.caution,.admonition.warning{border-left-color:rgba(255,145,0,.8)}.admonition.attention>.admonition-title,.admonition.caution>.admonition-title,.admonition.warning>.admonition-title{background-color:rgba(255,145,0,.1);border-bottom-color:rgba(255,145,0,.2)}.admonition.attention>.admonition-title:before{color:#ff9100;content:"\E417"}.admonition.caution>.admonition-title:before,.admonition.warning>.admonition-title:before{color:#ff9100;content:"\E002"}.admonition.fail,.admonition.failure,.admonition.missing{border-left-color:rgba(255,82,82,.8)}.admonition.fail>.admonition-title,.admonition.failure>.admonition-title,.admonition.missing>.admonition-title{background-color:rgba(255,82,82,.1);border-bottom-color:rgba(255,82,82,.2)}.admonition.fail>.admonition-title:before,.admonition.failure>.admonition-title:before,.admonition.missing>.admonition-title:before{color:#ff5252;content:"\E14C"}.admonition.bug,.admonition.danger,.admonition.error{border-left-color:rgba(255,23,68,.8)}.admonition.bug>.admonition-title,.admonition.danger>.admonition-title,.admonition.error>.admonition-title{background-color:rgba(255,23,68,.1);border-bottom-color:rgba(255,23,68,.2)}.admonition.danger>.admonition-title:before{color:#ff1744;content:"\E3E7"}.admonition.error>.admonition-title:before{color:#ff1744;content:"\E14C"}.admonition.bug>.admonition-title:before{color:#ff1744;content:"\E868"}.admonition.example,.admonition.snippet{border-left-color:rgba(0,184,212,.8)}.admonition.example>.admonition-title,.admonition.snippet>.admonition-title{background-color:rgba(0,184,212,.1);border-bottom-color:rgba(0,184,212,.2)}.admonition.example>.admonition-title:before,.admonition.snippet>.admonition-title:before{color:#00b8d4;content:"\E242"}.admonition.cite,.admonition.quote{border-left-color:rgba(158,158,158,.8)}.admonition.cite>.admonition-title,.admonition.quote>.admonition-title{background-color:rgba(158,158,158,.1);border-bottom-color:rgba(158,158,158,.2)}.admonition.cite>.admonition-title:before,.admonition.quote>.admonition-title:before{color:#9e9e9e;content:"\E244"}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<p>这门课面向的对象是专业的量化研究员、或者打算向这个方向转岗求职、或者尽管是其它职业，但决心以专业、严谨的态度探索量化研究的学习者。</p>
<p>学完这门课程并完全掌握其内容，你将具有熟练的因子分析能力、掌握领先的机器学习策略构建方法，成为有创新研究能力和比较竞争优势的量化研究员。</p>
<h2 id="1-课程目标">1. 课程目标 </h2>
<p>在学完本课程之后，你将会获得以下能力（或工具）：</p>
<ol>
<li>掌握 Alphalens 因子分析框架，并在工作中运用。</li>
<li>懂得如何阅读 Alphalens 的分析报表，根据报表判定因子有效性。</li>
<li>懂得如何运用 Alphalens 挖掘因子的价值。</li>
<li>在你的量化兵器库中，加入 Alpha101 因子库、Ta-lib 因子库、TsFresh 因子库及其它我们介绍的过的 10 余中因子。</li>
<li>掌握因子挖掘方法论，通过变更交易品种、交易周期、因子参数等方法，扩充自己的因子库（需要完全掌握统计基础知识）。</li>
<li>能实现统计套利 (Pair Trading) 交易策略，有能力在众多标的中搜索出可以配对的品种。</li>
<li>掌握 XGBoost 模型，以及如何基于 XGBoost 构建股票价格预测模型、交易模型和资产组合。这将很可能是你今后工作的起点和竞争优势。</li>
</ol>
<h2 id="2-先修要求">2. 先修要求 </h2>
<p>在学习本课程之前，学员需要掌握的 Python 编程基础，包括：</p>
<ol>
<li>Python 基础语法和常用库，包括时间日期、字符串、文件 IO、列表、字典、模块导入、typing 等等。</li>
<li>统计学知识。需要有大学基础的统计学知识，对一些基础概念有初步了解，这部分内容在《量化 24 课中有详细讲解》。</li>
<li>Jupyter Notebook。我们提供了《Notebook 入门》和《Notebook 高级技巧》供大家学习。</li>
<li>Numpy 和 Pandas。需要有入门级的知识。课程中使用了大量 Numpy 和 Pandas 高级技巧，如果没有事先掌握，会增加阅读示例代码（包括听课）的难度。建议在上本课时，同时学习我们的《量化交易中的 Numpy 和 Pandas》课程（免费）。我们也会在讲课中讲解一些语法知识，但不是课程重点。</li>
<li>对机器学习、神经网络有一定的认识。在我们讲解机器学习核心理论时，这会帮助你跟上进度。</li>
</ol>
<p>如果不满足前两个条件，学习本课程将比较有困难。如果不满足后面三个条件，你仍然可以学习本课程，但需要多花一些时间来熟悉这些内容。</p>
<h2 id="3-课程内容">3. 课程内容 </h2>
<p>课程内容涵盖了因子挖掘、因子检验到构建机器学习模型的过程。课程内容主要由三部分组成：</p>
<h3 id="31-因子检验方法">3.1. 因子检验方法 </h3>
<p>只有掌握了因子检验的方法，我们才能判断挖掘出的因子是否有效。因此，因子检验方法是本课程的起点，从第 2 章开始，到第 7 章结束，共 6 个章节。</p>
<div style="width:33%;float:left;padding: 0.5rem 1rem 0 0;text-align:center">
<img src="https://images.jieyu.ai/images/2024/01/alphalens.jpg">
<span style="font-size:0.6rem">Alphalens Logo</span>
</div>
<p>我们将从介绍因子检验的原理入手，手动实现因子检验的各个流程；再介绍开源因子分析框架 Alphalens。我们不仅会介绍如何把 Alphalens 用起来，还会重点介绍如何解读它的报表、如何进行参数调优和排错。这部分包含了大量业界经验、正反例对比，内容之深、之新，是你目前在网络上无法看到的。</p>
<p>当你懂得如何通过报表来判断因子的好坏、如何灵活运用 Alphalens 以揭露隐藏在大量数据之下的因子与收益的关系的时候，你就真正成长为因子分析高手。</p>
<h3 id="32-因子挖掘">3.2. 因子挖掘 </h3>
<p>第 8 章到第 12 章构成了课程的第二部分。</p>
<p>因子挖掘，或者说特征工程，是构建交易策略的重要一环，也是量化研究员最日常的工作项。我们将介绍 Alpha 101 因子、Ta-lib 和技术指标因子、行为金融学因子、基本面因子、另类因子。</p>
<p>如果掌握这些因子还嫌不够，我们还将在第 12 章介绍因子挖掘方法论。你可能是从我们发表在网上的各种因子与策略挖掘的精彩文章吸引，从而找到我们的，在这里，我们将把掌握的资源和方法论全部教授给你。</p>
<div style="width:50%;text-align:center;margin: 0 auto 1rem">
<img src="https://images.jieyu.ai/images/2024/08/factor-nav.jpg">
<span style="font-size:0.8em">各类因子</span>
</div>
<p>在介绍 Alpha 101 因子时，我们把重点放在如何理解它的数据格式和实现算子上。这是理解 Alpha 101 的基础，掌握了这些算子，你就完全能够读懂和实现全部 Alpha 101 因子。然后，我们会介绍其中的几个因子。我们会告诉你如何阅读它复杂的表达式，如何理解作者的思路和意图。</p>
<p>在实现 Alpha 101 因子上，由于已经有许多开源的实现存在，因此，我们不打算重新发明轮子，而是向你介绍一个我们认为实现最完整、正确率最高的一个开源库，并在我们的附录中可以直接上手使用它。此后，你可以把它纳入你的量化兵器库。</p>
<div style="width:50%;text-align:center;margin: 0 auto 1rem">
<img src="https://images.jieyu.ai/images/2024/08/better-sing-wave-indicator.jpg">
<span style="font-size:0.6rem">Hilbert Sine Wave</span>
</div>
<p>在第 9 章，我们将介绍 Ta-lib 库以及它实现的一些技术指标。我们将介绍均线、Overlap、Momemtum、成交量和波动性等 20 个左右的指标。有一些你可能已经比较熟悉了，比如均线，也有一些你可能不太熟悉，比如基于希尔伯特变换的趋势线和 Sine Wave Indicator（如 [](#Hilbert Sine Wave) 所示）。和其它章节一样，我们仍然会保持足够的研究深度，会介绍像冷启动期、如何将老的技术指标翻新应用（以 RSI 为例）等等。</p>
<p>在第 10 章，我们将介绍基本面因子和另类因子。由于数据获取的难度和质量问题，我们将以介绍原理为主，不一定都给出代码实现。</p>
<p>在第 11 章，我们介绍不属于任何归类，但仍然很重要的因子，比如小概率事件（黑天鹅）因子；我们会引入导数概念，介绍两个有效的一阶导、二阶导动量因子；时频变换构造频哉因子；我们还将介绍一些行为金融学因子，这是当前金融学的热门概念，在短线交易中非常有用。</p>
<div style="width:50%;text-align:center;margin: 0 auto 1rem">
<img src="https://images.jieyu.ai/images/2024/08/fft-decomposite.jpg">
<span style="font-size:0.6rem">通过 FFT 提取频域因子</span>
</div>
<h3 id="33-构建基于机器学习的交易策略">3.3. 构建基于机器学习的交易策略 </h3>
<p>这一部分我们先快速介绍机器学习的核心概念（第 14 章）。我们会介绍损失函数、目标函数、度量函数、距离函数、偏差、方差、过拟合与正则化惩罚等核心概念。这些概念是机器学习核心概念中偏应用层面一些的概念，是我们不得不与之打交道的概念。</p>
<div class="admonition tip">
<p class="admonition-title">Tip</p>
<p>如果你需要深入理解机器学习和神经网络、自己能发明新的网络模型和机器学习算法，那么你还需要补充线性代数、梯度优化、反向传播和激活函数等知识。不过，对掌握我们这门课程，达到熟练运用已知的算法模型并会调参调优，掌握我们介绍的概念就足够了。</p>
</div>
<div style="width:33%;float:left;padding: 0.5rem 1rem 0 0;text-align:center">
<img src="https://images.jieyu.ai/images/2024/08/sklearn-logo.png">
<span style="font-size:0.6rem"></span>
</div>
<p>本课程选择的机器学习库是 sklearn。sklearn 是一个非常强大的机器学习库，以丰富的模型和简单易用的接口赢得大家的喜爱。在第 15 章，我们先向大家介绍 sklearn 的通用工具包 -- 用来处理无论我们采用什么样的算法模型，都要遇到的那些共同问题，比如数据预处理、模型评估、模型解释与可视化和内置数据集。</p>
<p>第 16 章我们会介绍模型优化方法，这是多数从事机器学习与人工智能的人所能掌握的核心技能，也是我们做出一个优秀的机器学习交易模型的关键之一。我们将演示如何使交叉验证、如何使用网格搜索 (GridSearch)、随机搜索 (RandomizedSearch) 等方法。</p>
<p>量化领域的机器学习有它自己的特殊性，比如在交叉验证方面，我们实际上要使用的是一种称为 Rolling Forecasting（也称为 Walk-Forward Optimization 的方法）。我们将在第 16 章的最后部分，详细介绍这种方法以及它的实现。</p>
<p>接下来我们介绍一个聚类算法（第 17 章）。在量化交易中，Pair Trading 是一类重要的套利策略，它的先决条件是找出能够配对的两个标的。这一章我们将介绍先进的 HDBSCAN 聚类方法，演示如何通过它来实现聚类，然后通过 statsmodels 中的相关方法来执行协整对检验，找到能够配对的标的。最后，我们还将演示如何将这一切组成一个完整的交易策略。</p>
<p>在第 18 章，我们将介绍 XGBoost，这是一种梯度提升决策树模型。由于金融数据高噪声的特性、以及难以获得大量有效标注数据原原因，使得梯度提升决策树模型目前仍然是在量化交易领域应用最广泛、效果最好的机器学习模型。</p>
<div style="width:50%;text-align:center;margin: 0 auto 1rem">
<img src="https://images.jieyu.ai/images/2024/08/scheme-of-xgbost-model.jpg">
<span style="font-size:0.6rem"></span>
</div>
<p>我们会先从决策树模型讲起，介绍 XGBoost 一路走来的优化历程。然后以一个详尽的例子，介绍如何使用 XGBoost，训练一个模型并深入到它的内部：我们将可视化这个模型的重要特征、绘制出它的模型树。最后我们以介绍在 XGBoost 如何进行交叉验证和调优结束。</p>
<p>在做了大量理论与实操的学习之后，我们已经完成了所有铺垫，现在是时候学习如何构建基于 XGBoost 的量化交易策略了。我们将抛弃几乎是无效的端到端训练的方式（即输入价格，希望能预测出下一个价格），改用一些探索性、但更加有效的策略示例。</p>
<p>我们将在第 19 章里，介绍基于 XGBoost 回归模型，如何构建一个价格预测模型。我们会介绍策略原理、实现和优化方案。尽管我们构建的是一个价格预测模型，但它决非你在网上看到的那种端到端的 toy 类型的模型！</p>
<p>另一个模型将在第 20 章里介绍，它是基于 XGBoost 的分类模型构建的一个交易模型。换句话说，它不负责预测价格，但能告诉你应该买入、还是卖出。在本章中，我们还要介绍如何制作标注工具</p>
<p>这两个示例指出了在目前条件下，应该如何使用机器学习构建策略的根本方法 -- 由于金融数据饱含噪声，所以我们不能指望端到端的模型能够工作。但如果我们能清晰地定义问题，找出有效特征，机器学习就会非常强大！这将是你在市场上战胜他人的利器。</p>
<p>第 21 章我们会对 XGBoost 模型进行更深入一些的拷问。我们介绍另一个梯度提升决策树模型的实现，即由微软开发的 LightGBM。一般认为，它在性能上要强于 XGBoost，内存占用更小，在其它指标上与 XGBoost 相比各有千秋。</p>
<div style="width:50%;text-align:center;margin: 0 auto 1rem">
<img src="https://images.jieyu.ai/images/2024/08/lightGBM-by-Hossain-medium.jpeg">
<span style="font-size:0.6rem">LightGBM, by Hossain@medim</span>
</div>
<p>我们已经介绍了三个非常实用的例子，涵盖了套利交易、价格预测和交易模型。但是，资产管理中还有一个重要的课题，就是组合管理。基于机器学习，我们如何实现组合管理？我们也将在这一章回答这个问题。</p>
<p>我们的课程将结束于第 22 课。我们将介绍深度学习的先驱--CNN 网络在 K 线模型识别上的应用。我不认为 CNN 网络在 K 线模型识别上有任何优势，我会详细地介绍为什么。但是，也许你有兴趣解决这个问题，所以，我还是会介绍 CNN 在 k 线识别上的一般做法。</p>
<p>比起深度学习，我更看好强化学习在交易方面的应用。在加密货币、商品期货上，重要的不是选择投资品种，而是根据市场的变化决定交易时机、仓位、杠杆，这天然就是一个强化学习问题。我将介绍强化学习的基本概念、相关学习资源。</p>
<p>最后，还有两个无法归入到上面所有这些类别 -- 无论是机器学习、深度学习还是强化学习，但仍然非常重要的智能算法 -- Kalman Filter 和 Genentic Algorithm。</p>
<p>整个课程的大纲可以在 <a href="https://www.jieyu.ai/articles/coursea/factor-ml/syllabus.html">这里</a> 查阅。</p>
<h2 id="4-课程编排">4. 课程编排 </h2>
<p>课程内容由正文、习题和补充材料三部分组成。</p>
<p>课程正文内容以应用为主，对机器学习的核心理论，只讲到在应用中必须接触的部分。在几乎每一章都提供了大量拓展阅读材料和注释，供希望在具体细节或者底层体系上深入研究的同学。对这部分内容，没有时间的同学可以跳过，不影响课程学习效果。</p>
<p>课程附有大量习题。习题的目的是：</p>
<ol>
<li>部分示例中涉及一些编程技巧，在量化研究中比较常用，所以编入习题强化记忆。</li>
<li>部分话题的结论是开放性的、探索性的，不适合作为正式内容讲授。<br>
我们为本课程精心准备了大量的习题。你应该充分利用这些习题来巩固和拓展自己学到的知识与技能。这些习题多数是自动批改的，因此你可以及时了解到自己知识掌握的程度。</li>
</ol>
<p>习题分发、提交作业和获取老师批阅结果流程都是自动化的，通过 Nbgrader 来实现。如果你之前没有接触过 Nbgrader，可以在 <a href="../%E8%AF%BE%E7%A8%8B%E9%A1%BB%E7%9F%A5.ipynb">课程须知</a> 的关于作业一节中掌握它的使用方法。</p>
<p>本课程只涵盖了量化交易中的部分知识。如果要独立从事交易或者做完量化全栈工作，建议补充学习 <a href="https://www.jieyu.ai/articles/coursea/24lectures/intro/">《量化 24 课》</a>。本课程与《量化 24 课》的区别是，本课程内容更为专精，《量化 24 课》内容更广泛，涵盖更全面。</p>
<div class="admonition tip">
<style>
    ol>li {
        font-size: 12px;
        margin: 0;
        line-height: 1.2rem;
    }
</style>
<p class="admonition-title">《因子投资与机器学习策略》喊你上课啦！</p>
<p>面向策略研究员的专业课程，涵盖<b>因子挖掘</b>、<b>因子检验</b>和基于<b>机器学习</b>的策略开发三大模块，构建你的个人竞争优势！</p>
<ol>
<img src="https://images.jieyu.ai/images/hot/quantfans.png" style="width:150px;float:right;">
<li>全网独家精讲 Alphalens 报告阅读，助你精通因子检验和调优。</li>
<li>超 400 个独立因子（代码实现 350+），分类精讲底层逻辑。</li>
<li>三大<b>实用模型</b>，奠定未来研究<sup>1</sup>：聚类算法搜索配对交易标的（中性策略核心）、基于 XGBoost 的资产定价、趋势交易模型。<p>
</p></li><li>100多篇内容相关拓展阅读材料（论文、研报、开源项目链接），你的研究从这里开始！</li>
<li>领先教学手段：SBP（Slidev Based Presentation）、INI（In-place Notebook Interaction）和基于 Nbgrader（UCBerkley 使用中）的作业系统。</li>
<li>课程核心价值观：Learning without thought is labor lost</li>
</ol>
<hr style="border-bottom:1px solid #ccc;height:1px;width:20%">
<p style="font-size:10px !important">1. 示例模型思路新颖。未来一段时间，你都可以围绕这些模型增加因子、优化参数，构建出领先的量化策略系统。</p>
</div>

      </div>
      <div class="md-sidebar-toc">
<div class="md-toc">
<div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:18px">
          <a href="#1-课程目标" class="md-toc-link">
            <ol>
<li>课程目标</li>
</ol>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:18px">
          <a href="#2-先修要求" class="md-toc-link">
            <ol start="2">
<li>先修要求</li>
</ol>

          </a></div><details style="padding:0;;padding-left:0px;" open="">
        <summary class="md-toc-link-wrapper">
          <a href="#3-课程内容" class="md-toc-link"><ol start="3">
<li>课程内容</li>
</ol>
</a>
          </summary>
        <div>
          <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#31-因子检验方法" class="md-toc-link">
            <p>3.1. 因子检验方法</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#32-因子挖掘" class="md-toc-link">
            <p>3.2. 因子挖掘</p>

          </a></div><div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:42px">
          <a href="#33-构建基于机器学习的交易策略" class="md-toc-link">
            <p>3.3. 构建基于机器学习的交易策略</p>

          </a></div>
        </div>
      </details>
    <div class="md-toc-link-wrapper" style="padding:0;;display:list-item;list-style:square;margin-left:18px">
          <a href="#4-课程编排" class="md-toc-link">
            <ol start="4">
<li>课程编排</li>
</ol>

          </a></div>
</div>
</div>
      <a id="sidebar-toc-btn">≡</a>
    
    
    
    
    
    
<script>

var sidebarTOCBtn = document.getElementById('sidebar-toc-btn')
sidebarTOCBtn.addEventListener('click', function(event) {
  event.stopPropagation()
  if (document.body.hasAttribute('html-show-sidebar-toc')) {
    document.body.removeAttribute('html-show-sidebar-toc')
  } else {
    document.body.setAttribute('html-show-sidebar-toc', true)
  }
})
</script>
      
  
    </body></html>