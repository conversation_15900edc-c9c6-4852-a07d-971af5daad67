---
slug: factor-ml-faq
---

## 报名流程和学习环境

!!! abstract "课程怎么学？"
    课程已全部录播完成，上传到了荔枝微课。

    课程教材以 Jupyter Notebook 文档方式提供，由我们提供的服务器 host。在您购买课程后，我们将为您分配账号，创建专属您个人的计算环境。在该环境中可以阅读、运行我们提供的 Notebook，您也可以创建自己的 Notebook。学员享有独立运行环境，您的 Notebook 其它人无法看到。

    购买请点击 [链接](https://weike.fm/xw1UD19dca)

!!! abstract "学这门课需要多久？"
    主要取决于你自己的时间。如果每天一课，应该在三周内能上完<br><br>
    课程视频会永久免费收看。专属课件服务器使用限为 6 个月，6个月后，您仍然可以在两年内登录，不过将使用共享服务器。<br><br>课件服务器仅供学习使用，不能用作云服务器。

!!! abstract "哪些人适合学习这门课/前置条件？"
    请见 [课程介绍](./intro.md) 中的先修条件。

!!! abstract "你们提供学习环境吗？"
    我们提供一个由 192CPU 核、256GB 内存构成的服务器集群为学员服务，学员通过浏览器登录后，即可在线学习和运行我们的示例代码。在该环境中，我们提供了供因子分析用的日线行情数据。其它数据，大家可以通过我们购买的高级别 Tushare 账号来获取。

!!! abstract "你们的学习环境有何优势，自己搭环境可以吗？"
    我们的课程示例使用了专门的数据接口。如果要在本地搭建环境，需要修改这些接口，自行提供数据。这些接口很简单，有详细的教程。

!!! abstract "你们的数据可以下载到本地使用吗"
    课程环境中使用的数据均为我们向第三方**采购**，按协议不得分发和转售。所以我们不能承诺提供数据下载。

!!! abstract "我想了解更多关于课程的信息"
    我们提供课程预览环境，其中有这门课的部分课时（视频、notebook及练习题）。你可添加助教 quantfans_99 的微信，获取链接。

## 其它问题


!!! tip "我该如何申请量化交易权限？有哪些门槛？"
    一般新开户可以同时申请量化交易权限。可以跟宽粉（quantfans_99）咨询，帮您找到门槛最低、资费最优的券商。

!!! tip "可以介绍下老师吗？"
    <div style="width:150px; position: relative;float:right">
        <img src="https://images.jieyu.ai/images/hot/me.png" style="width: 120px; display:inline-block"/>
        <p style="text-align:center;width:120px"> Aaron </p>
    </div>

    -   IBM/Oracle 高级软件研发经理

    -   海豚浏览器（红杉资本投资）副总裁

    -   格物致知（量化投资）联合创始人
  
    -   匡醍(Quantide)量化创始人

    -   Zillionare 开源量化框架发起人

    -   《Python 高效编程实践指南》作者（机械工业出版社出版）。
