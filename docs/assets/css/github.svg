<!--使用svg为Github Readme引入样式-->
<svg fill="none" viewBox="0 0 400 400" width="400" height="400" xmlns="http://www.w3.org/2000/svg">
    <foreignObject width="100%" height="100%">
        <div xmlns="http://www.w3.org/1999/xhtml">
            <style>
<head>
<link href="assets/css/bootstrap.min.4.0.css" rel="stylesheet" />
<link href="assets/css/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet" />
<meta name="viewport" content="width=device-width, initial-scale=1">

<style>
.md-typeset h1,
.md-content__button {
    display: none;
}

.md-typeset hr {
    display: none;
}

.md-sidebar--primary {
    display: none;
}
.md-sidebar--secondary {
    display: none important!;
}

.as-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(360px, 1fr));
}



@media (min-width: 768px) { 
    .card-columns {
        column-count: 2;
    }
 }

@media (min-width: 1200px) { 
    .card-columns {
        column-count: 3;
    }
 }

a .card-title {
    color: rgb(55, 58, 60);
}

a .card-text {
    color: rgb(55, 58, 60);
}

a:hover {
    color: inherit;
    text-decoration: inherit;
}

nav a {
    font-size: 0.8rem !important;
    color: white;
}
</style>
        </div>
    </foreignObject>
</svg>
