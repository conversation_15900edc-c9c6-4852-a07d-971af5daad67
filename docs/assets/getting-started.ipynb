{"cells": [{"cell_type": "markdown", "id": "76f04e58-ed6d-491e-960e-87be12146af4", "metadata": {}, "source": ["## 概览\n", "\n", "zillionare通过omicron提供了行情数据、时间运算、基础算法、策略编写、回测等功能。在本环境中，自带了2022年全年的数据（从30分钟到周线）。\n", "\n", "关于 omicron 的用法，请参见 https://zillionare.github.io/omicron\n"]}, {"cell_type": "code", "execution_count": null, "id": "0b1de2c9-4ae9-466e-b820-e23554fbf4cc", "metadata": {}, "outputs": [], "source": ["import datetime\n", "import logging\n", "import sys\n", "import warnings\n", "from typing import Dict, List, Optional, Union\n", "\n", "import cfg4py\n", "import numpy as np\n", "import omicron\n", "import pandas as pd\n", "import talib as ta\n", "from coretypes import Frame, FrameType\n", "from IPython.display import clear_output\n", "from omicron import tf\n", "from omicron.core.backtestlog import BacktestLogger\n", "from omicron.extensions import (\n", "    array_math_round,\n", "    array_price_equal,\n", "    bars_since,\n", "    count_between,\n", "    fill_nan,\n", "    find_runs,\n", "    math_round,\n", "    price_equal,\n", "    smallest_n_argpos,\n", "    top_n_argpos,\n", ")\n", "from omicron.models.board import Board, BoardType\n", "from omicron.models.security import Security\n", "from omicron.models.stock import Stock\n", "from omicron.plotting.candlestick import Candlestick\n", "from omicron.plotting.metrics import MetricsGraph\n", "from omicron.talib import moving_average\n", "from traderclient import TraderClient\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "logger = BacktestLogger.getLogger(\"notebook\")\n", "\n", "cfg4py.init(\"/etc/zillionare\")\n", "\n", "await omicron.init()\n", "Board.init(\"omega\")"]}, {"cell_type": "markdown", "id": "07e4ed84-e84a-413f-8026-21c3b1143c76", "metadata": {}, "source": ["## 行情数据\n", "### 个股行情\n", "\n", "我们可以通过get_bars来获取行情数据。"]}, {"cell_type": "code", "execution_count": 7, "id": "28fb38b3-fd3f-4809-a204-db6075ea3815", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([('2023-01-30T00:00:00', 15.6 , 15.74, 14.89, 15.15, 1.37431750e+08, 2.09686765e+09, 123.90969),\n", "       ('2023-01-31T00:00:00', 15.24, 15.51, 14.96, 14.99, 1.03049784e+08, 1.56863933e+09, 123.90969),\n", "       ('2023-02-01T00:00:00', 15.03, 15.08, 14.51, 14.7 , 1.65342148e+08, 2.42647197e+09, 123.90969),\n", "       ('2023-02-02T00:00:00', 14.74, 14.78, 14.27, 14.6 , 1.57790039e+08, 2.28074488e+09, 123.90969),\n", "       ('2023-02-03T00:00:00', 14.45, 14.55, 14.16, 14.32, 1.24638298e+08, 1.78210589e+09, 123.90969),\n", "       ('2023-02-06T00:00:00', 14.1 , 14.13, 13.86, 14.  , 1.31761010e+08, 1.83842871e+09, 123.90969),\n", "       ('2023-02-07T00:00:00', 14.06, 14.32, 14.03, 14.21, 8.69460460e+07, 1.23529776e+09, 123.90969),\n", "       ('2023-02-08T00:00:00', 14.24, 14.32, 14.03, 14.04, 6.57892570e+07, 9.28708934e+08, 123.90969),\n", "       ('2023-02-09T00:00:00', 14.01, 14.3 , 13.96, 14.13, 8.30350020e+07, 1.17436196e+09, 123.90969),\n", "       ('2023-02-10T00:00:00', 14.1 , 14.12, 13.86, 13.98, 8.47283660e+07, 1.18428339e+09, 123.90969)],\n", "      dtype=[('frame', '<M8[s]'), ('open', '<f4'), ('high', '<f4'), ('low', '<f4'), ('close', '<f4'), ('volume', '<f8'), ('amount', '<f8'), ('factor', '<f4')])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["bars = await Stock.get_bars(\"000001.XSHE\", 250, FrameType.DAY)\n", "bars[-10:]"]}, {"cell_type": "markdown", "id": "d896e841-913f-4b7d-85a3-2d1556265e3b", "metadata": {}, "source": ["这样获取的数据是Numpy structured array数据结构。我们可以通过以下方法将其转换为pd.DataFrame"]}, {"cell_type": "markdown", "id": "a56cbab0-4d6c-4259-838e-64bd48dcea11", "metadata": {}, "source": ["### 板块信息"]}, {"cell_type": "code", "execution_count": 38, "id": "868b3e3e-122a-40c3-9c8e-1b852987339f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>frame</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "      <th>factor</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-09-01</td>\n", "      <td>1077.703003</td>\n", "      <td>1080.427002</td>\n", "      <td>1066.473999</td>\n", "      <td>1067.234009</td>\n", "      <td>226989220.0</td>\n", "      <td>1.099670e+09</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-09-02</td>\n", "      <td>1069.370972</td>\n", "      <td>1083.811035</td>\n", "      <td>1069.370972</td>\n", "      <td>1082.511963</td>\n", "      <td>236729820.0</td>\n", "      <td>1.258059e+09</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-09-05</td>\n", "      <td>1080.014038</td>\n", "      <td>1091.135986</td>\n", "      <td>1077.687988</td>\n", "      <td>1090.916992</td>\n", "      <td>237900350.0</td>\n", "      <td>1.127750e+09</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022-09-06</td>\n", "      <td>1093.052979</td>\n", "      <td>1112.338013</td>\n", "      <td>1092.463013</td>\n", "      <td>1111.156982</td>\n", "      <td>260565040.0</td>\n", "      <td>1.407028e+09</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-09-07</td>\n", "      <td>1108.711060</td>\n", "      <td>1114.146973</td>\n", "      <td>1100.338013</td>\n", "      <td>1109.458008</td>\n", "      <td>201213880.0</td>\n", "      <td>1.290435e+09</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2022-09-08</td>\n", "      <td>1107.348999</td>\n", "      <td>1117.490967</td>\n", "      <td>1098.937012</td>\n", "      <td>1099.796021</td>\n", "      <td>293380040.0</td>\n", "      <td>1.352487e+09</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2022-09-09</td>\n", "      <td>1096.170044</td>\n", "      <td>1103.285034</td>\n", "      <td>1087.983032</td>\n", "      <td>1100.604980</td>\n", "      <td>202762210.0</td>\n", "      <td>9.239262e+08</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2022-09-13</td>\n", "      <td>1101.515015</td>\n", "      <td>1109.812012</td>\n", "      <td>1100.319946</td>\n", "      <td>1108.181030</td>\n", "      <td>179904920.0</td>\n", "      <td>1.017504e+09</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2022-09-14</td>\n", "      <td>1092.937988</td>\n", "      <td>1094.963989</td>\n", "      <td>1080.584961</td>\n", "      <td>1088.295044</td>\n", "      <td>191233000.0</td>\n", "      <td>1.018259e+09</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2022-09-15</td>\n", "      <td>1091.406982</td>\n", "      <td>1092.852051</td>\n", "      <td>1059.657959</td>\n", "      <td>1065.496948</td>\n", "      <td>251875980.0</td>\n", "      <td>1.458851e+09</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       frame         open         high          low        close       volume  \\\n", "0 2022-09-01  1077.703003  1080.427002  1066.473999  1067.234009  226989220.0   \n", "1 2022-09-02  1069.370972  1083.811035  1069.370972  1082.511963  236729820.0   \n", "2 2022-09-05  1080.014038  1091.135986  1077.687988  1090.916992  237900350.0   \n", "3 2022-09-06  1093.052979  1112.338013  1092.463013  1111.156982  260565040.0   \n", "4 2022-09-07  1108.711060  1114.146973  1100.338013  1109.458008  201213880.0   \n", "5 2022-09-08  1107.348999  1117.490967  1098.937012  1099.796021  293380040.0   \n", "6 2022-09-09  1096.170044  1103.285034  1087.983032  1100.604980  202762210.0   \n", "7 2022-09-13  1101.515015  1109.812012  1100.319946  1108.181030  179904920.0   \n", "8 2022-09-14  1092.937988  1094.963989  1080.584961  1088.295044  191233000.0   \n", "9 2022-09-15  1091.406982  1092.852051  1059.657959  1065.496948  251875980.0   \n", "\n", "         amount  factor  \n", "0  1.099670e+09     1.0  \n", "1  1.258059e+09     1.0  \n", "2  1.127750e+09     1.0  \n", "3  1.407028e+09     1.0  \n", "4  1.290435e+09     1.0  \n", "5  1.352487e+09     1.0  \n", "6  9.239262e+08     1.0  \n", "7  1.017504e+09     1.0  \n", "8  1.018259e+09     1.0  \n", "9  1.458851e+09     1.0  "]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["from omicron.models.board import Board, BoardType\n", "\n", "concepts = await Board.board_list()\n", "concepts[:10]\n", "\n", "df = pd.DataFrame(bars)\n", "df[:10]"]}, {"cell_type": "markdown", "id": "c7ff84a2-14a2-4703-9ea8-1ea0ec7fc727", "metadata": {}, "source": ["### 获取板块行情"]}, {"cell_type": "code", "execution_count": null, "id": "93d831e9-c91a-4976-831c-5748fb1c8d92", "metadata": {}, "outputs": [], "source": ["start = datetime.date(2022, 9, 1)  # 起始时间， 可修改\n", "end = datetime.date(2023, 3, 1)  # 截止时间， 可修改\n", "board_code = '881128' # 汽车服务， 可修改\n", "bars = await Board.get_bars_in_range(board_code, start, end)\n", "bars[-3:] # 打印后3条数据"]}, {"cell_type": "markdown", "id": "ebeadb91-4dd4-4fee-abbf-13d3ae6e1ffe", "metadata": {}, "source": ["## 绘制K线图\n", "\n", "我们可以通过 omicron.plotting.candlestick中的类 Candlestick 来绘制k线图："]}, {"cell_type": "code", "execution_count": 39, "id": "2c9c7c7e-855f-47e1-8f38-ae87335e7ce8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.7, 0.15, 0.15]\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"close": [1067.23, 1082.51, 1090.92, 1111.16, 1109.46, 1099.8, 1100.6, 1108.18, 1088.3, 1065.5, 1041.49, 1040.36, 1074.7, 1079.79, 1060.06, 1040.61, 1009.8, 1022.42, 1000.52, 993.13, 981.51, 959.32, 969.28, 988.31, 1000.19, 1019.45, 1031.11, 1035.06, 1031.08, 1022.39, 1009.58, 987.51, 996.38, 1008.32, 1020.94, 976.77, 997.94, 1028.22, 1047.52, 1051.75, 1072.04, 1073.76, 1064.36, 1072.13, 1075.19, 1079.5, 1067.79, 1082.17, 1081.12, 1091.05, 1068.59, 1080.15, 1079.4, 1099.12, 1081.22, 1077.6, 1071.7, 1088.92, 1117.63, 1102.26, 1094.21, 1120.37, 1119.34, 1110.06, 1102.04, 1089.96, 1076.37, 1078.8, 1074.42, 1083.37, 1070.57, 1048.57, 1051.34, 1042.4, 1031.56, 1020.98, 1037.09, 1038.65, 1022.85, 1013.3, 1023.96, 1041.4, 1044.65, 1047.42, 1054.55, 1051.12, 1063.06, 1057.07, 1047.49, 1059.57, 1060.87, 1066.37, 1061.58, 1061.36, 1083.83, 1103.04, 1092.67, 1123.75, 1116.51, 1119.52, 1117.39, 1118.66, 1116.63, 1122.66, 1128.27, 1131.03, 1130.92, 1129.2, 1108.02, 1105.75, 1123.51, 1130.44, 1128.75, 1126.63, 1121.17, 1109.53, 1121.02, 1128.13], "decreasing": {"fillcolor": "#3DAA70", "line": {"color": "#3DAA70"}}, "high": [1080.427001953125, 1083.81103515625, 1091.135986328125, 1112.3380126953125, 1114.14697265625, 1117.490966796875, 1103.2850341796875, 1109.81201171875, 1094.9639892578125, 1092.85205078125, 1065.7509765625, 1050.6190185546875, 1081.22900390625, 1085.4639892578125, 1078.821044921875, 1065.72900390625, 1032.56201171875, 1023.5900268554688, 1023.0070190429688, 1012.8980102539062, 994.489990234375, 984.833984375, 970.5170288085938, 988.676025390625, 1007.0499877929688, 1026.4610595703125, 1031.6209716796875, 1044.0240478515625, 1041.9549560546875, 1034.623046875, 1026.8900146484375, 1021.4299926757812, 1000.4910278320312, 1016.4929809570312, 1029.383056640625, 1014.4110107421875, 1004.3309936523438, 1028.958984375, 1052.9010009765625, 1063.240966796875, 1073.72802734375, 1081.7509765625, 1073.220947265625, 1082.1920166015625, 1083.2259521484375, 1096.9840087890625, 1081.166015625, 1082.16796875, 1092.4119873046875, 1092.6219482421875, 1089.4840087890625, 1083.4029541015625, 1090.5450439453125, 1104.1529541015625, 1094.7110595703125, 1082.906982421875, 1075.7679443359375, 1090.8609619140625, 1125.0849609375, 1128.779052734375, 1102.6280517578125, 1120.3719482421875, 1120.97802734375, 1123.885986328125, 1110.447998046875, 1103.64697265625, 1087.885009765625, 1090.9560546875, 1080.758056640625, 1087.8929443359375, 1083.550048828125, 1075.22802734375, 1055.385986328125, 1052.373046875, 1050.2490234375, 1031.803955078125, 1039.1600341796875, 1039.383056640625, 1033.2740478515625, 1022.135009765625, 1025.197998046875, 1041.509033203125, 1046.614013671875, 1047.5069580078125, 1057.0159912109375, 1055.4599609375, 1066.510009765625, 1062.7449951171875, 1050.053955078125, 1062.5040283203125, 1064.426025390625, 1069.47998046875, 1064.8280029296875, 1062.6009521484375, 1085.9429931640625, 1104.697021484375, 1093.844970703125, 1124.08203125, 1122.9560546875, 1122.8929443359375, 1121.60498046875, 1122.4539794921875, 1123.1910400390625, 1123.9639892578125, 1129.7320556640625, 1132.5970458984375, 1140.6199951171875, 1133.718017578125, 1134.3709716796875, 1116.6409912109375, 1124.18505859375, 1136.0970458984375, 1131.56396484375, 1137.2030029296875, 1127.072021484375, 1124.364013671875, 1121.3380126953125, 1128.22900390625], "increasing": {"fillcolor": "rgba(255,255,255,0.9)", "line": {"color": "#FF4136"}}, "line": {"width": 1}, "low": [1066.4739990234375, 1069.3709716796875, 1077.68798828125, 1092.4630126953125, 1100.3380126953125, 1098.93701171875, 1087.9830322265625, 1100.3199462890625, 1080.5849609375, 1059.657958984375, 1041.488037109375, 1032.3280029296875, 1046.29296875, 1051.4449462890625, 1058.4219970703125, 1036.0799560546875, 1008.625, 1003.739990234375, 1000.5230102539062, 988.3820190429688, 981.2639770507812, 955.7659912109375, 952.6589965820312, 952.7230224609375, 981.8690185546875, 1011.1719970703125, 1011.2059936523438, 1031.72802734375, 1026.9560546875, 1011.5490112304688, 1008.7680053710938, 983.8280029296875, 972.875, 996.5070190429688, 1008.77197265625, 975.2219848632812, 977.7730102539062, 1000.5759887695312, 1034.2969970703125, 1043.583984375, 1050.7979736328125, 1063.7449951171875, 1059.23095703125, 1064.5419921875, 1064.85595703125, 1078.791015625, 1061.85400390625, 1063.593994140625, 1075.791015625, 1075.010009765625, 1067.199951171875, 1063.6400146484375, 1071.4090576171875, 1079.0579833984375, 1076.16796875, 1069.718017578125, 1055.5489501953125, 1072.9169921875, 1084.262939453125, 1100.717041015625, 1090.4150390625, 1097.5009765625, 1109.1309814453125, 1106.31005859375, 1099.623046875, 1085.14794921875, 1072.7650146484375, 1075.58203125, 1069.697998046875, 1071.0269775390625, 1067.06201171875, 1045.39697265625, 1041.1839599609375, 1037.6529541015625, 1029.1199951171875, 1013.2249755859375, 1020.302001953125, 1025.4949951171875, 1017.5280151367188, 1012.4849853515625, 1015.1179809570312, 1019.4910278320312, 1035.5870361328125, 1041.032958984375, 1042.239990234375, 1047.2330322265625, 1040.5140380859375, 1053.3470458984375, 1044.385986328125, 1052.8599853515625, 1056.0789794921875, 1059.9520263671875, 1060.3990478515625, 1056.3060302734375, 1070.489990234375, 1094.9110107421875, 1088.2449951171875, 1105.06494140625, 1113.2110595703125, 1107.3809814453125, 1113.364013671875, 1114.697021484375, 1115.0159912109375, 1112.1199951171875, 1119.032958984375, 1124.970947265625, 1126.739013671875, 1124.987060546875, 1100.2659912109375, 1102.6949462890625, 1102.637939453125, 1122.35302734375, 1125.6180419921875, 1120.991943359375, 1114.623046875, 1108.740966796875, 1109.8270263671875, 1116.6199951171875], "name": "K线", "open": [1077.7030029296875, 1069.3709716796875, 1080.0140380859375, 1093.052978515625, 1108.7110595703125, 1107.3489990234375, 1096.1700439453125, 1101.5150146484375, 1092.93798828125, 1091.406982421875, 1065.7509765625, 1032.7249755859375, 1046.3310546875, 1066.3189697265625, 1070.116943359375, 1060.9560546875, 1032.56201171875, 1010.0189819335938, 1020.677978515625, 1005.791015625, 991.6690063476562, 982.2739868164062, 961.2899780273438, 971.5250244140625, 983.5650024414062, 1011.1719970703125, 1011.2059936523438, 1033.1719970703125, 1029.9739990234375, 1025.2320556640625, 1021.8270263671875, 1012.0800170898438, 989.739013671875, 997.2899780273438, 1009.5960083007812, 1014.4110107421875, 977.7730102539062, 1000.5759887695312, 1034.509033203125, 1043.583984375, 1050.7979736328125, 1067.56298828125, 1072.06103515625, 1066.02099609375, 1071.323974609375, 1091.31201171875, 1078.5899658203125, 1067.6529541015625, 1081.373046875, 1077.0050048828125, 1089.4840087890625, 1072.3389892578125, 1080.7490234375, 1079.0579833984375, 1091.0689697265625, 1077.2220458984375, 1065.958984375, 1072.9169921875, 1087.3280029296875, 1124.0870361328125, 1101.77294921875, 1098.2960205078125, 1115.737060546875, 1110.552001953125, 1107.677978515625, 1100.6009521484375, 1087.885009765625, 1075.58203125, 1077.35400390625, 1073.7349853515625, 1078.5870361328125, 1069.6080322265625, 1045.3299560546875, 1049.4530029296875, 1047.5489501953125, 1024.426025390625, 1020.302001953125, 1037.2530517578125, 1033.1400146484375, 1019.239013671875, 1017.8579711914062, 1020.9299926757812, 1040.762939453125, 1045.31005859375, 1046.237060546875, 1049.5040283203125, 1052.251953125, 1058.6710205078125, 1044.385986328125, 1052.8599853515625, 1056.0789794921875, 1069.47998046875, 1062.156982421875, 1058.740966796875, 1073.26904296875, 1096.7669677734375, 1092.25, 1105.06494140625, 1122.85302734375, 1115.614990234375, 1115.031982421875, 1121.1719970703125, 1120.864013671875, 1115.31396484375, 1121.06005859375, 1130.4639892578125, 1139.0040283203125, 1132.386962890625, 1128.9520263671875, 1107.802001953125, 1108.60498046875, 1123.4019775390625, 1129.31494140625, 1130.56005859375, 1127.072021484375, 1117.748046875, 1112.2459716796875, 1122.2330322265625], "type": "candlestick", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "yaxis": "y"}, {"line": {"color": "#1432F5", "width": 1}, "name": "ma5", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, 1092.2559999999999, 1098.77, 1102.388, 1105.84, 1101.268, 1092.476, 1080.814, 1068.766, 1062.0700000000002, 1060.3680000000002, 1059.28, 1059.104, 1052.992, 1042.5359999999998, 1026.682, 1013.2959999999999, 1001.4759999999999, 991.38, 980.752, 978.31, 979.722, 987.31, 1001.6679999999999, 1014.8239999999998, 1023.3779999999999, 1027.818, 1025.8439999999998, 1017.1239999999998, 1009.3879999999998, 1004.8359999999998, 1004.5459999999998, 997.9839999999997, 1000.0699999999997, 1006.4379999999998, 1014.2779999999998, 1020.4399999999998, 1039.494, 1054.658, 1061.8859999999997, 1066.8079999999998, 1071.4959999999999, 1072.988, 1071.7939999999999, 1075.356, 1077.154, 1080.3259999999998, 1078.144, 1080.616, 1080.062, 1083.662, 1081.696, 1083.498, 1081.808, 1083.712, 1087.414, 1091.622, 1094.944, 1104.6779999999999, 1110.762, 1109.248, 1109.204, 1108.354, 1099.5539999999999, 1091.446, 1084.318, 1080.584, 1076.706, 1071.146, 1065.654, 1059.25, 1048.8880000000001, 1038.97, 1036.6740000000002, 1034.1360000000002, 1030.2260000000003, 1026.5740000000003, 1027.17, 1028.0320000000002, 1029.2320000000002, 1034.1460000000002, 1042.3960000000002, 1047.8280000000002, 1052.16, 1054.644, 1054.6580000000001, 1055.662, 1057.6119999999999, 1058.274, 1059.176, 1061.9499999999998, 1066.802, 1075.2359999999999, 1080.4959999999999, 1092.93, 1103.9599999999998, 1111.098, 1113.968, 1119.166, 1117.742, 1118.9720000000002, 1120.7220000000002, 1123.45, 1125.902, 1128.416, 1125.488, 1120.9840000000002, 1119.48, 1119.384, 1119.294, 1123.016, 1126.1000000000001, 1123.304, 1121.42, 1121.296], "yaxis": "y"}, {"line": {"color": "#EB52F7", "width": 1}, "name": "ma10", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, null, null, null, null, null, 1092.366, 1089.7920000000001, 1085.577, 1083.9550000000002, 1080.818, 1075.8780000000002, 1069.959, 1060.8790000000001, 1052.303, 1043.525, 1036.288, 1030.29, 1022.1860000000001, 1011.6440000000001, 1002.4960000000001, 996.509, 994.393, 996.524, 997.788, 1000.8439999999999, 1003.77, 1006.5769999999999, 1009.396, 1012.106, 1014.107, 1016.182, 1011.914, 1008.597, 1007.913, 1009.557, 1012.493, 1018.739, 1027.364, 1034.162, 1040.543, 1045.9679999999998, 1056.2409999999998, 1063.2259999999999, 1068.6209999999999, 1071.981, 1075.9109999999998, 1075.5659999999998, 1076.2049999999997, 1077.7089999999998, 1080.408, 1081.011, 1080.821, 1081.212, 1081.887, 1085.538, 1086.659, 1089.2210000000002, 1093.2430000000002, 1097.237, 1098.3310000000001, 1100.4130000000002, 1101.6490000000001, 1102.1160000000002, 1101.104, 1096.7830000000001, 1094.8940000000002, 1092.5300000000004, 1085.3500000000004, 1078.5500000000004, 1071.7840000000003, 1064.7360000000006, 1057.8380000000004, 1053.9100000000005, 1049.8950000000004, 1044.7380000000005, 1037.7310000000004, 1033.0700000000004, 1032.3530000000005, 1031.6840000000004, 1032.1860000000004, 1034.4850000000004, 1037.4990000000005, 1040.0960000000002, 1041.9380000000003, 1044.4020000000003, 1049.0290000000002, 1052.7200000000003, 1055.2170000000003, 1056.9100000000003, 1058.3040000000003, 1061.2320000000004, 1066.4240000000004, 1069.3850000000004, 1076.0530000000006, 1082.9550000000006, 1088.9500000000005, 1094.6020000000005, 1099.8310000000008, 1105.336000000001, 1111.4660000000008, 1115.9100000000008, 1118.7090000000007, 1122.5340000000008, 1123.0790000000009, 1122.230000000001, 1120.8530000000007, 1121.4650000000008, 1122.643000000001, 1123.8550000000012, 1124.252000000001, 1123.542000000001, 1121.392000000001, 1120.402000000001, 1120.295000000001], "yaxis": "y"}, {"line": {"color": "#C0C0C0", "width": 1}, "name": "ma20", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1064.3269999999998, 1060.041, 1053.8815, 1047.7994999999999, 1041.657, 1036.1935, 1032.1760000000002, 1028.7015, 1025.0455, 1022.1845, 1020.029, 1018.4335, 1015.791, 1011.875, 1008.3015, 1006.3455, 1003.1535, 1002.5605, 1002.8505, 1005.2004999999999, 1008.1315, 1012.6579999999999, 1018.3799999999998, 1023.1339999999997, 1027.3249999999996, 1031.0749999999996, 1034.0774999999996, 1035.9114999999997, 1038.2669999999996, 1040.7689999999998, 1044.2019999999998, 1047.1524999999997, 1051.7844999999995, 1055.9354999999996, 1060.4754999999996, 1063.4894999999995, 1068.5309999999995, 1072.2189999999994, 1075.2539999999995, 1078.7594999999994, 1081.2849999999994, 1082.3934999999994, 1084.7239999999995, 1087.4729999999995, 1089.3694999999996, 1090.7119999999993, 1091.2349999999994, 1091.6639999999993, 1091.4954999999993, 1091.1604999999993, 1090.7764999999993, 1090.8754999999992, 1089.2964999999992, 1087.893499999999, 1085.057499999999, 1082.574499999999, 1079.7434999999991, 1078.012999999999, 1075.499499999999, 1070.7604999999992, 1066.312499999999, 1062.799999999999, 1058.851499999999, 1055.116999999999, 1051.9849999999992, 1049.610499999999, 1047.668499999999, 1047.002999999999, 1045.9164999999991, 1044.569999999999, 1043.379999999999, 1042.894999999999, 1043.784999999999, 1044.2969999999991, 1045.244999999999, 1047.8584999999991, 1051.9614999999992, 1054.7404999999992, 1058.995499999999, 1063.678499999999, 1068.9894999999992, 1073.6609999999991, 1077.5239999999992, 1081.1229999999991, 1084.884999999999, 1088.5709999999992, 1092.5664999999992, 1095.9594999999993, 1099.5659999999993, 1102.5924999999993, 1104.9014999999993, 1108.0334999999993, 1111.2369999999992, 1114.5954999999992, 1117.8589999999992, 1119.7259999999992, 1120.0504999999991, 1121.4679999999992, 1121.6869999999992], "yaxis": "y"}, {"line": {"color": "#882111", "width": 1}, "name": "ma30", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1044.1413333333333, 1042.2196666666666, 1039.0529999999999, 1035.9016666666664, 1032.4736666666665, 1029.523, 1025.4219999999998, 1021.9999999999999, 1019.3346666666665, 1017.9753333333333, 1017.5169999999999, 1018.5353333333333, 1019.6486666666666, 1019.304, 1019.0486666666666, 1019.553, 1020.8493333333333, 1022.7823333333333, 1024.7740000000001, 1027.4606666666666, 1030.7246666666665, 1033.6273333333334, 1037.655, 1041.3256666666666, 1045.0193333333334, 1047.7203333333334, 1049.6586666666667, 1051.0116666666668, 1052.807, 1055.692, 1058.3543333333334, 1061.1753333333334, 1065.604, 1069.7026666666668, 1073.094, 1075.7973333333334, 1079.5703333333333, 1082.1846666666668, 1083.8706666666667, 1084.7673333333335, 1085.8213333333333, 1085.7723333333333, 1084.9326666666668, 1084.4986666666668, 1083.5076666666669, 1082.0533333333333, 1080.1026666666667, 1079.0793333333334, 1077.6286666666667, 1075.6863333333333, 1073.0946666666666, 1071.607, 1070.3153333333332, 1069.157, 1067.4336666666666, 1066.5446666666664, 1065.6619999999998, 1065.3739999999998, 1064.3123333333333, 1061.9743333333333, 1060.5513333333333, 1059.44, 1057.64, 1055.7146666666667, 1054.0913333333333, 1053.4843333333333, 1053.9203333333332, 1054.4636666666665, 1055.962, 1057.365, 1058.5700000000002, 1060.1306666666667, 1062.467, 1064.6433333333334, 1067.3186666666668, 1070.5423333333333, 1074.2106666666666, 1077.3383333333334, 1080.3566666666666, 1083.1956666666665, 1086.2773333333332, 1089.5956666666666, 1092.5636666666664, 1095.3669999999997, 1098.007333333333, 1100.2279999999998, 1102.175, 1104.107, 1106.4756666666665], "yaxis": "y"}, {"line": {"color": "#5E8E28", "width": 1}, "name": "ma60", "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x", "y": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1051.2478333333333, 1051.6975, 1052.3285, 1052.8021666666668, 1052.7838333333334, 1052.6601666666668, 1052.4961666666668, 1052.0923333333335, 1051.602666666667, 1051.3713333333335, 1051.669166666667, 1052.1538333333335, 1052.290666666667, 1051.9013333333337, 1051.278166666667, 1050.803166666667, 1050.4760000000003, 1050.9308333333336, 1051.2013333333337, 1051.5735000000004, 1051.9096666666671, 1052.617166666667, 1053.985166666667, 1055.2413333333338, 1056.2265000000004, 1057.1325000000004, 1057.6603333333337, 1058.1928333333337, 1058.559666666667, 1058.833166666667, 1059.4528333333337, 1060.307666666667, 1061.6220000000005, 1062.708666666667, 1063.5926666666671, 1064.6408333333338, 1066.7453333333337, 1068.324166666667, 1069.9163333333338, 1071.066166666667, 1072.195666666667, 1072.9515000000004, 1073.6998333333336, 1074.5710000000004, 1075.413166666667, 1076.2978333333335, 1077.156666666667, 1078.2088333333336, 1078.9926666666668, 1079.4410000000003, 1079.6860000000001, 1080.6013333333335, 1081.4395000000002, 1082.2620000000002, 1082.7205000000001, 1083.3863333333334, 1083.9185000000002, 1084.7405, 1085.394], "yaxis": "y"}, {"marker": {"color": ["#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136"]}, "showlegend": false, "type": "bar", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x2", "y": [226989220, 236729820, 237900350, 260565040, 201213880, 293380040, 202762210, 179904920, 191233000, 251875980, 256138710, 220909490, 217569700, 244210070, 167554260, 185700070, 218860010, 169503530, 178618370, 177557300, 149769520, 144396460, 138404680, 277828320, 208517190, 252159660, 225319460, 219485390, 181487540, 203298360, 168883830, 209963370, 255026110, 203196670, 250435530, 259622650, 207113380, 244164790, 283163650, 242695650, 330301590, 362218980, 240337660, 224186950, 350872790, 359563370, 266056050, 282066390, 247835770, 566705760, 365896890, 257560460, 298991630, 351255700, 233074010, 288032320, 242412630, 325431890, 523338470, 406875300, 278347760, 407131960, 366643860, 373338390, 327076390, 316952590, 245023320, 254669990, 179312620, 207137230, 195319830, 230006180, 196950780, 155263440, 172545450, 170384160, 144893910, 150435810, 163611590, 178334330, 181519070, 227687640, 186274290, 124547468, 102397242, 84239877, 236017620, 119786571, 22342508, 49775643, 57737987, 91940863, 18334682, 44698124, 253389540, 105484665, 85231478, 527081000, 533311190, 516175760, 116662517, 120588487, 237631200, 231850930, 228114460, 120600309, 188561260, 182457450, 333896590, 194746130, 253587950, 229800680, 159532290, 153580070, 121896192, 177208600, 184921650, 163106000], "yaxis": "y3"}, {"showlegend": false, "type": "scatter", "x": ["2022-09-01", "2022-09-02", "2022-09-05", "2022-09-06", "2022-09-07", "2022-09-08", "2022-09-09", "2022-09-13", "2022-09-14", "2022-09-15", "2022-09-16", "2022-09-19", "2022-09-20", "2022-09-21", "2022-09-22", "2022-09-23", "2022-09-26", "2022-09-27", "2022-09-28", "2022-09-29", "2022-09-30", "2022-10-10", "2022-10-11", "2022-10-12", "2022-10-13", "2022-10-14", "2022-10-17", "2022-10-18", "2022-10-19", "2022-10-20", "2022-10-21", "2022-10-24", "2022-10-25", "2022-10-26", "2022-10-27", "2022-10-28", "2022-10-31", "2022-11-01", "2022-11-02", "2022-11-03", "2022-11-04", "2022-11-07", "2022-11-08", "2022-11-09", "2022-11-10", "2022-11-11", "2022-11-14", "2022-11-15", "2022-11-16", "2022-11-17", "2022-11-18", "2022-11-21", "2022-11-22", "2022-11-23", "2022-11-24", "2022-11-25", "2022-11-28", "2022-11-29", "2022-11-30", "2022-12-01", "2022-12-02", "2022-12-05", "2022-12-06", "2022-12-07", "2022-12-08", "2022-12-09", "2022-12-12", "2022-12-13", "2022-12-14", "2022-12-15", "2022-12-16", "2022-12-19", "2022-12-20", "2022-12-21", "2022-12-22", "2022-12-23", "2022-12-26", "2022-12-27", "2022-12-28", "2022-12-29", "2022-12-30", "2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01"], "xaxis": "x3", "y": [null, null, null, null, null, null, 79.7468354430378, 82.57294511091328, 57.37572111387301, 40.40639103501499, 29.413317914939086, 28.968217139478327, 54.227539139877976, 56.95046712094351, 44.60726060705147, 35.50425391669486, 25.581064853257367, 34.56978983460072, 27.62213938681536, 25.543393583178876, 22.36725337224261, 17.407235014330837, 26.219644190896208, 40.72115036198661, 48.32924753531781, 58.65307215446654, 63.89390034222551, 65.66318021274594, 61.99029875410149, 54.06653550279513, 44.09601736355228, 31.924438785637793, 39.92212947166057, 49.50477897815361, 58.00133350046944, 33.98427321807704, 46.682105004459544, 59.91558280187094, 66.31103616040382, 67.66777320529536, 73.75224769210874, 74.24527616522707, 66.1024011280651, 69.42825789420615, 70.7829858417031, 72.81879776861163, 59.33850575341311, 68.05342452085603, 66.79895153338327, 72.54286342308738, 49.36342154694091, 57.70946735100332, 56.978310332805016, 69.26477381020977, 52.83047114431639, 49.954088339977, 45.14666306023328, 58.974583822691685, 72.72887418086248, 59.8403976584911, 53.84344254709582, 66.81304875220465, 65.9376467121676, 57.75608832143123, 51.17136903482729, 42.42821605776269, 34.47594776921893, 37.0092168507978, 34.153198218309925, 44.630549917756646, 35.05722926323446, 24.304632087449555, 27.657139883822758, 23.60771424880288, 19.461627954744483, 16.141429631418145, 36.0701587739092, 37.78812932435741, 28.484790254163308, 24.16892125463229, 36.962432456597206, 52.64667421478675, 55.14251636684293, 57.436957639751085, 63.24407268272798, 58.626560809908504, 68.29587063054437, 59.87217562640539, 48.41221609201302, 59.997937882605925, 61.125394607154085, 65.99173575268924, 58.357162065261136, 57.987421204058286, 76.35148509613595, 83.67300506934635, 69.69536338832941, 81.06910213859213, 73.37142127558604, 74.57579453334463, 71.81751764537785, 72.54409376196416, 69.12575331014635, 73.56575106388539, 77.22267476151029, 78.94249590536681, 78.65844852653044, 73.68372967828846, 38.088330952470706, 35.86031880103182, 58.5979699667637, 64.49204875030263, 61.91270744636107, 58.39691715710137, 49.6782946726698, 35.94812785837039, 51.74578499401207, 59.21524973105144], "yaxis": "y5"}], "layout": {"annotations": [{"font": {"size": 16}, "showarrow": false, "text": "volume", "x": 0.47, "xanchor": "center", "xref": "paper", "y": 0.33999999999999997, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "rsi", "x": 0.47, "xanchor": "center", "xref": "paper", "y": 0.12, "yanchor": "bottom", "yref": "paper"}], "autosize": true, "hovermode": "x unified", "plot_bgcolor": "rgba(0, 0, 0, 0)", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "xaxis": {"anchor": "y", "domain": [0, 0.94], "matches": "x3", "range": [-2, 117], "rangeslider": {"visible": false, "yaxis": {}}, "showgrid": false, "showspikes": true, "showticklabels": false, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "type": "category"}, "xaxis2": {"anchor": "y3", "domain": [0, 0.94], "matches": "x3", "range": [-2, 117], "showgrid": false, "showspikes": true, "showticklabels": false, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "type": "category"}, "xaxis3": {"anchor": "y5", "domain": [0, 0.94], "minor": {"nticks": 5, "ticklen": 5, "ticks": "outside"}, "nticks": 11, "range": [-2, 117], "showgrid": false, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "ticklen": 10, "ticks": "outside", "type": "category"}, "yaxis": {"anchor": "x", "domain": [0.44, 1], "gridcolor": "rgba(0, 0, 0, 0.1)", "range": [905.0260467529297, 1197.650994873047], "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "type": "linear"}, "yaxis2": {"anchor": "x", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis3": {"anchor": "x2", "autorange": true, "domain": [0.22, 0.33999999999999997], "gridcolor": "rgba(0, 0, 0, 0.1)", "range": [0, 596532378.9473684], "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "type": "linear"}, "yaxis4": {"anchor": "x2", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y3", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis5": {"anchor": "x3", "autorange": true, "domain": [0, 0.12], "gridcolor": "rgba(0, 0, 0, 0.1)", "range": [12.389675440422135, 87.42475926034236], "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "type": "linear"}, "yaxis6": {"anchor": "x3", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y5", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}}}, "image/png": "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", "text/html": ["<div>                            <div id=\"007aea95-5a7d-4bc2-9432-21f0de2c6a4a\" class=\"plotly-graph-div\" style=\"height:800px; width:100%;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"007aea95-5a7d-4bc2-9432-21f0de2c6a4a\")) {                    Plotly.newPlot(                        \"007aea95-5a7d-4bc2-9432-21f0de2c6a4a\",                        [{\"close\":[1067.23,1082.51,1090.92,1111.16,1109.46,1099.8,1100.6,1108.18,1088.3,1065.5,1041.49,1040.36,1074.7,1079.79,1060.06,1040.61,1009.8,1022.42,1000.52,993.13,981.51,959.32,969.28,988.31,1000.19,1019.45,1031.11,1035.06,1031.08,1022.39,1009.58,987.51,996.38,1008.32,1020.94,976.77,997.94,1028.22,1047.52,1051.75,1072.04,1073.76,1064.36,1072.13,1075.19,1079.5,1067.79,1082.17,1081.12,1091.05,1068.59,1080.15,1079.4,1099.12,1081.22,1077.6,1071.7,1088.92,1117.63,1102.26,1094.21,1120.37,1119.34,1110.06,1102.04,1089.96,1076.37,1078.8,1074.42,1083.37,1070.57,1048.57,1051.34,1042.4,1031.56,1020.98,1037.09,1038.65,1022.85,1013.3,1023.96,1041.4,1044.65,1047.42,1054.55,1051.12,1063.06,1057.07,1047.49,1059.57,1060.87,1066.37,1061.58,1061.36,1083.83,1103.04,1092.67,1123.75,1116.51,1119.52,1117.39,1118.66,1116.63,1122.66,1128.27,1131.03,1130.92,1129.2,1108.02,1105.75,1123.51,1130.44,1128.75,1126.63,1121.17,1109.53,1121.02,1128.13],\"decreasing\":{\"fillcolor\":\"#3DAA70\",\"line\":{\"color\":\"#3DAA70\"}},\"high\":[1080.427001953125,1083.81103515625,1091.135986328125,1112.3380126953125,1114.14697265625,1117.490966796875,1103.2850341796875,1109.81201171875,1094.9639892578125,1092.85205078125,1065.7509765625,1050.6190185546875,1081.22900390625,1085.4639892578125,1078.821044921875,1065.72900390625,1032.56201171875,1023.5900268554688,1023.0070190429688,1012.8980102539062,994.489990234375,984.833984375,970.5170288085938,988.676025390625,1007.0499877929688,1026.4610595703125,1031.6209716796875,1044.0240478515625,1041.9549560546875,1034.623046875,1026.8900146484375,1021.4299926757812,1000.4910278320312,1016.4929809570312,1029.383056640625,1014.4110107421875,1004.3309936523438,1028.958984375,1052.9010009765625,1063.240966796875,1073.72802734375,1081.7509765625,1073.220947265625,1082.1920166015625,1083.2259521484375,1096.9840087890625,1081.166015625,1082.16796875,1092.4119873046875,1092.6219482421875,1089.4840087890625,1083.4029541015625,1090.5450439453125,1104.1529541015625,1094.7110595703125,1082.906982421875,1075.7679443359375,1090.8609619140625,1125.0849609375,1128.779052734375,1102.6280517578125,1120.3719482421875,1120.97802734375,1123.885986328125,1110.447998046875,1103.64697265625,1087.885009765625,1090.9560546875,1080.758056640625,1087.8929443359375,1083.550048828125,1075.22802734375,1055.385986328125,1052.373046875,1050.2490234375,1031.803955078125,1039.1600341796875,1039.383056640625,1033.2740478515625,1022.135009765625,1025.197998046875,1041.509033203125,1046.614013671875,1047.5069580078125,1057.0159912109375,1055.4599609375,1066.510009765625,1062.7449951171875,1050.053955078125,1062.5040283203125,1064.426025390625,1069.47998046875,1064.8280029296875,1062.6009521484375,1085.9429931640625,1104.697021484375,1093.844970703125,1124.08203125,1122.9560546875,1122.8929443359375,1121.60498046875,1122.4539794921875,1123.1910400390625,1123.9639892578125,1129.7320556640625,1132.5970458984375,1140.6199951171875,1133.718017578125,1134.3709716796875,1116.6409912109375,1124.18505859375,1136.0970458984375,1131.56396484375,1137.2030029296875,1127.072021484375,1124.364013671875,1121.3380126953125,1128.22900390625],\"increasing\":{\"fillcolor\":\"rgba(255,255,255,0.9)\",\"line\":{\"color\":\"#FF4136\"}},\"line\":{\"width\":1},\"low\":[1066.4739990234375,1069.3709716796875,1077.68798828125,1092.4630126953125,1100.3380126953125,1098.93701171875,1087.9830322265625,1100.3199462890625,1080.5849609375,1059.657958984375,1041.488037109375,1032.3280029296875,1046.29296875,1051.4449462890625,1058.4219970703125,1036.0799560546875,1008.625,1003.739990234375,1000.5230102539062,988.3820190429688,981.2639770507812,955.7659912109375,952.6589965820312,952.7230224609375,981.8690185546875,1011.1719970703125,1011.2059936523438,1031.72802734375,1026.9560546875,1011.5490112304688,1008.7680053710938,983.8280029296875,972.875,996.5070190429688,1008.77197265625,975.2219848632812,977.7730102539062,1000.5759887695312,1034.2969970703125,1043.583984375,1050.7979736328125,1063.7449951171875,1059.23095703125,1064.5419921875,1064.85595703125,1078.791015625,1061.85400390625,1063.593994140625,1075.791015625,1075.010009765625,1067.199951171875,1063.6400146484375,1071.4090576171875,1079.0579833984375,1076.16796875,1069.718017578125,1055.5489501953125,1072.9169921875,1084.262939453125,1100.717041015625,1090.4150390625,1097.5009765625,1109.1309814453125,1106.31005859375,1099.623046875,1085.14794921875,1072.7650146484375,1075.58203125,1069.697998046875,1071.0269775390625,1067.06201171875,1045.39697265625,1041.1839599609375,1037.6529541015625,1029.1199951171875,1013.2249755859375,1020.302001953125,1025.4949951171875,1017.5280151367188,1012.4849853515625,1015.1179809570312,1019.4910278320312,1035.5870361328125,1041.032958984375,1042.239990234375,1047.2330322265625,1040.5140380859375,1053.3470458984375,1044.385986328125,1052.8599853515625,1056.0789794921875,1059.9520263671875,1060.3990478515625,1056.3060302734375,1070.489990234375,1094.9110107421875,1088.2449951171875,1105.06494140625,1113.2110595703125,1107.3809814453125,1113.364013671875,1114.697021484375,1115.0159912109375,1112.1199951171875,1119.032958984375,1124.970947265625,1126.739013671875,1124.987060546875,1100.2659912109375,1102.6949462890625,1102.637939453125,1122.35302734375,1125.6180419921875,1120.991943359375,1114.623046875,1108.740966796875,1109.8270263671875,1116.6199951171875],\"name\":\"K\\u7ebf\",\"open\":[1077.7030029296875,1069.3709716796875,1080.0140380859375,1093.052978515625,1108.7110595703125,1107.3489990234375,1096.1700439453125,1101.5150146484375,1092.93798828125,1091.406982421875,1065.7509765625,1032.7249755859375,1046.3310546875,1066.3189697265625,1070.116943359375,1060.9560546875,1032.56201171875,1010.0189819335938,1020.677978515625,1005.791015625,991.6690063476562,982.2739868164062,961.2899780273438,971.5250244140625,983.5650024414062,1011.1719970703125,1011.2059936523438,1033.1719970703125,1029.9739990234375,1025.2320556640625,1021.8270263671875,1012.0800170898438,989.739013671875,997.2899780273438,1009.5960083007812,1014.4110107421875,977.7730102539062,1000.5759887695312,1034.509033203125,1043.583984375,1050.7979736328125,1067.56298828125,1072.06103515625,1066.02099609375,1071.323974609375,1091.31201171875,1078.5899658203125,1067.6529541015625,1081.373046875,1077.0050048828125,1089.4840087890625,1072.3389892578125,1080.7490234375,1079.0579833984375,1091.0689697265625,1077.2220458984375,1065.958984375,1072.9169921875,1087.3280029296875,1124.0870361328125,1101.77294921875,1098.2960205078125,1115.737060546875,1110.552001953125,1107.677978515625,1100.6009521484375,1087.885009765625,1075.58203125,1077.35400390625,1073.7349853515625,1078.5870361328125,1069.6080322265625,1045.3299560546875,1049.4530029296875,1047.5489501953125,1024.426025390625,1020.302001953125,1037.2530517578125,1033.1400146484375,1019.239013671875,1017.8579711914062,1020.9299926757812,1040.762939453125,1045.31005859375,1046.237060546875,1049.5040283203125,1052.251953125,1058.6710205078125,1044.385986328125,1052.8599853515625,1056.0789794921875,1069.47998046875,1062.156982421875,1058.740966796875,1073.26904296875,1096.7669677734375,1092.25,1105.06494140625,1122.85302734375,1115.614990234375,1115.031982421875,1121.1719970703125,1120.864013671875,1115.31396484375,1121.06005859375,1130.4639892578125,1139.0040283203125,1132.386962890625,1128.9520263671875,1107.802001953125,1108.60498046875,1123.4019775390625,1129.31494140625,1130.56005859375,1127.072021484375,1117.748046875,1112.2459716796875,1122.2330322265625],\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"type\":\"candlestick\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#1432F5\",\"width\":1},\"name\":\"ma5\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,1092.2559999999999,1098.77,1102.388,1105.84,1101.268,1092.476,1080.814,1068.766,1062.0700000000002,1060.3680000000002,1059.28,1059.104,1052.992,1042.5359999999998,1026.682,1013.2959999999999,1001.4759999999999,991.38,980.752,978.31,979.722,987.31,1001.6679999999999,1014.8239999999998,1023.3779999999999,1027.818,1025.8439999999998,1017.1239999999998,1009.3879999999998,1004.8359999999998,1004.5459999999998,997.9839999999997,1000.0699999999997,1006.4379999999998,1014.2779999999998,1020.4399999999998,1039.494,1054.658,1061.8859999999997,1066.8079999999998,1071.4959999999999,1072.988,1071.7939999999999,1075.356,1077.154,1080.3259999999998,1078.144,1080.616,1080.062,1083.662,1081.696,1083.498,1081.808,1083.712,1087.414,1091.622,1094.944,1104.6779999999999,1110.762,1109.248,1109.204,1108.354,1099.5539999999999,1091.446,1084.318,1080.584,1076.706,1071.146,1065.654,1059.25,1048.8880000000001,1038.97,1036.6740000000002,1034.1360000000002,1030.2260000000003,1026.5740000000003,1027.17,1028.0320000000002,1029.2320000000002,1034.1460000000002,1042.3960000000002,1047.8280000000002,1052.16,1054.644,1054.6580000000001,1055.662,1057.6119999999999,1058.274,1059.176,1061.9499999999998,1066.802,1075.2359999999999,1080.4959999999999,1092.93,1103.9599999999998,1111.098,1113.968,1119.166,1117.742,1118.9720000000002,1120.7220000000002,1123.45,1125.902,1128.416,1125.488,1120.9840000000002,1119.48,1119.384,1119.294,1123.016,1126.1000000000001,1123.304,1121.42,1121.296],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#EB52F7\",\"width\":1},\"name\":\"ma10\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,null,null,null,1092.366,1089.7920000000001,1085.577,1083.9550000000002,1080.818,1075.8780000000002,1069.959,1060.8790000000001,1052.303,1043.525,1036.288,1030.29,1022.1860000000001,1011.6440000000001,1002.4960000000001,996.509,994.393,996.524,997.788,1000.8439999999999,1003.77,1006.5769999999999,1009.396,1012.106,1014.107,1016.182,1011.914,1008.597,1007.913,1009.557,1012.493,1018.739,1027.364,1034.162,1040.543,1045.9679999999998,1056.2409999999998,1063.2259999999999,1068.6209999999999,1071.981,1075.9109999999998,1075.5659999999998,1076.2049999999997,1077.7089999999998,1080.408,1081.011,1080.821,1081.212,1081.887,1085.538,1086.659,1089.2210000000002,1093.2430000000002,1097.237,1098.3310000000001,1100.4130000000002,1101.6490000000001,1102.1160000000002,1101.104,1096.7830000000001,1094.8940000000002,1092.5300000000004,1085.3500000000004,1078.5500000000004,1071.7840000000003,1064.7360000000006,1057.8380000000004,1053.9100000000005,1049.8950000000004,1044.7380000000005,1037.7310000000004,1033.0700000000004,1032.3530000000005,1031.6840000000004,1032.1860000000004,1034.4850000000004,1037.4990000000005,1040.0960000000002,1041.9380000000003,1044.4020000000003,1049.0290000000002,1052.7200000000003,1055.2170000000003,1056.9100000000003,1058.3040000000003,1061.2320000000004,1066.4240000000004,1069.3850000000004,1076.0530000000006,1082.9550000000006,1088.9500000000005,1094.6020000000005,1099.8310000000008,1105.336000000001,1111.4660000000008,1115.9100000000008,1118.7090000000007,1122.5340000000008,1123.0790000000009,1122.230000000001,1120.8530000000007,1121.4650000000008,1122.643000000001,1123.8550000000012,1124.252000000001,1123.542000000001,1121.392000000001,1120.402000000001,1120.295000000001],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#C0C0C0\",\"width\":1},\"name\":\"ma20\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1064.3269999999998,1060.041,1053.8815,1047.7994999999999,1041.657,1036.1935,1032.1760000000002,1028.7015,1025.0455,1022.1845,1020.029,1018.4335,1015.791,1011.875,1008.3015,1006.3455,1003.1535,1002.5605,1002.8505,1005.2004999999999,1008.1315,1012.6579999999999,1018.3799999999998,1023.1339999999997,1027.3249999999996,1031.0749999999996,1034.0774999999996,1035.9114999999997,1038.2669999999996,1040.7689999999998,1044.2019999999998,1047.1524999999997,1051.7844999999995,1055.9354999999996,1060.4754999999996,1063.4894999999995,1068.5309999999995,1072.2189999999994,1075.2539999999995,1078.7594999999994,1081.2849999999994,1082.3934999999994,1084.7239999999995,1087.4729999999995,1089.3694999999996,1090.7119999999993,1091.2349999999994,1091.6639999999993,1091.4954999999993,1091.1604999999993,1090.7764999999993,1090.8754999999992,1089.2964999999992,1087.893499999999,1085.057499999999,1082.574499999999,1079.7434999999991,1078.012999999999,1075.499499999999,1070.7604999999992,1066.312499999999,1062.799999999999,1058.851499999999,1055.116999999999,1051.9849999999992,1049.610499999999,1047.668499999999,1047.002999999999,1045.9164999999991,1044.569999999999,1043.379999999999,1042.894999999999,1043.784999999999,1044.2969999999991,1045.244999999999,1047.8584999999991,1051.9614999999992,1054.7404999999992,1058.995499999999,1063.678499999999,1068.9894999999992,1073.6609999999991,1077.5239999999992,1081.1229999999991,1084.884999999999,1088.5709999999992,1092.5664999999992,1095.9594999999993,1099.5659999999993,1102.5924999999993,1104.9014999999993,1108.0334999999993,1111.2369999999992,1114.5954999999992,1117.8589999999992,1119.7259999999992,1120.0504999999991,1121.4679999999992,1121.6869999999992],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#882111\",\"width\":1},\"name\":\"ma30\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1044.1413333333333,1042.2196666666666,1039.0529999999999,1035.9016666666664,1032.4736666666665,1029.523,1025.4219999999998,1021.9999999999999,1019.3346666666665,1017.9753333333333,1017.5169999999999,1018.5353333333333,1019.6486666666666,1019.304,1019.0486666666666,1019.553,1020.8493333333333,1022.7823333333333,1024.7740000000001,1027.4606666666666,1030.7246666666665,1033.6273333333334,1037.655,1041.3256666666666,1045.0193333333334,1047.7203333333334,1049.6586666666667,1051.0116666666668,1052.807,1055.692,1058.3543333333334,1061.1753333333334,1065.604,1069.7026666666668,1073.094,1075.7973333333334,1079.5703333333333,1082.1846666666668,1083.8706666666667,1084.7673333333335,1085.8213333333333,1085.7723333333333,1084.9326666666668,1084.4986666666668,1083.5076666666669,1082.0533333333333,1080.1026666666667,1079.0793333333334,1077.6286666666667,1075.6863333333333,1073.0946666666666,1071.607,1070.3153333333332,1069.157,1067.4336666666666,1066.5446666666664,1065.6619999999998,1065.3739999999998,1064.3123333333333,1061.9743333333333,1060.5513333333333,1059.44,1057.64,1055.7146666666667,1054.0913333333333,1053.4843333333333,1053.9203333333332,1054.4636666666665,1055.962,1057.365,1058.5700000000002,1060.1306666666667,1062.467,1064.6433333333334,1067.3186666666668,1070.5423333333333,1074.2106666666666,1077.3383333333334,1080.3566666666666,1083.1956666666665,1086.2773333333332,1089.5956666666666,1092.5636666666664,1095.3669999999997,1098.007333333333,1100.2279999999998,1102.175,1104.107,1106.4756666666665],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"line\":{\"color\":\"#5E8E28\",\"width\":1},\"name\":\"ma60\",\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1051.2478333333333,1051.6975,1052.3285,1052.8021666666668,1052.7838333333334,1052.6601666666668,1052.4961666666668,1052.0923333333335,1051.602666666667,1051.3713333333335,1051.669166666667,1052.1538333333335,1052.290666666667,1051.9013333333337,1051.278166666667,1050.803166666667,1050.4760000000003,1050.9308333333336,1051.2013333333337,1051.5735000000004,1051.9096666666671,1052.617166666667,1053.985166666667,1055.2413333333338,1056.2265000000004,1057.1325000000004,1057.6603333333337,1058.1928333333337,1058.559666666667,1058.833166666667,1059.4528333333337,1060.307666666667,1061.6220000000005,1062.708666666667,1063.5926666666671,1064.6408333333338,1066.7453333333337,1068.324166666667,1069.9163333333338,1071.066166666667,1072.195666666667,1072.9515000000004,1073.6998333333336,1074.5710000000004,1075.413166666667,1076.2978333333335,1077.156666666667,1078.2088333333336,1078.9926666666668,1079.4410000000003,1079.6860000000001,1080.6013333333335,1081.4395000000002,1082.2620000000002,1082.7205000000001,1083.3863333333334,1083.9185000000002,1084.7405,1085.394],\"type\":\"scatter\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"marker\":{\"color\":[\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\"]},\"showlegend\":false,\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[226989220.0,236729820.0,237900350.0,260565040.0,201213880.0,293380040.0,202762210.0,179904920.0,191233000.0,251875980.0,256138710.0,220909490.0,217569700.0,244210070.0,167554260.0,185700070.0,218860010.0,169503530.0,178618370.0,177557300.0,149769520.0,144396460.0,138404680.0,277828320.0,208517190.0,252159660.0,225319460.0,219485390.0,181487540.0,203298360.0,168883830.0,209963370.0,255026110.0,203196670.0,250435530.0,259622650.0,207113380.0,244164790.0,283163650.0,242695650.0,330301590.0,362218980.0,240337660.0,224186950.0,350872790.0,359563370.0,266056050.0,282066390.0,247835770.0,566705760.0,365896890.0,257560460.0,298991630.0,351255700.0,233074010.0,288032320.0,242412630.0,325431890.0,523338470.0,406875300.0,278347760.0,407131960.0,366643860.0,373338390.0,327076390.0,316952590.0,245023320.0,254669990.0,179312620.0,207137230.0,195319830.0,230006180.0,196950780.0,155263440.0,172545450.0,170384160.0,144893910.0,150435810.0,163611590.0,178334330.0,181519070.0,227687640.0,186274290.0,124547468.0,102397242.0,84239877.0,236017620.0,119786571.0,22342508.0,49775643.0,57737987.0,91940863.0,18334682.0,44698124.0,253389540.0,105484665.0,85231478.0,527081000.0,533311190.0,516175760.0,116662517.0,120588487.0,237631200.0,231850930.0,228114460.0,120600309.0,188561260.0,182457450.0,333896590.0,194746130.0,253587950.0,229800680.0,159532290.0,153580070.0,121896192.0,177208600.0,184921650.0,163106000.0],\"type\":\"bar\",\"xaxis\":\"x2\",\"yaxis\":\"y3\"},{\"showlegend\":false,\"x\":[\"2022-09-01\",\"2022-09-02\",\"2022-09-05\",\"2022-09-06\",\"2022-09-07\",\"2022-09-08\",\"2022-09-09\",\"2022-09-13\",\"2022-09-14\",\"2022-09-15\",\"2022-09-16\",\"2022-09-19\",\"2022-09-20\",\"2022-09-21\",\"2022-09-22\",\"2022-09-23\",\"2022-09-26\",\"2022-09-27\",\"2022-09-28\",\"2022-09-29\",\"2022-09-30\",\"2022-10-10\",\"2022-10-11\",\"2022-10-12\",\"2022-10-13\",\"2022-10-14\",\"2022-10-17\",\"2022-10-18\",\"2022-10-19\",\"2022-10-20\",\"2022-10-21\",\"2022-10-24\",\"2022-10-25\",\"2022-10-26\",\"2022-10-27\",\"2022-10-28\",\"2022-10-31\",\"2022-11-01\",\"2022-11-02\",\"2022-11-03\",\"2022-11-04\",\"2022-11-07\",\"2022-11-08\",\"2022-11-09\",\"2022-11-10\",\"2022-11-11\",\"2022-11-14\",\"2022-11-15\",\"2022-11-16\",\"2022-11-17\",\"2022-11-18\",\"2022-11-21\",\"2022-11-22\",\"2022-11-23\",\"2022-11-24\",\"2022-11-25\",\"2022-11-28\",\"2022-11-29\",\"2022-11-30\",\"2022-12-01\",\"2022-12-02\",\"2022-12-05\",\"2022-12-06\",\"2022-12-07\",\"2022-12-08\",\"2022-12-09\",\"2022-12-12\",\"2022-12-13\",\"2022-12-14\",\"2022-12-15\",\"2022-12-16\",\"2022-12-19\",\"2022-12-20\",\"2022-12-21\",\"2022-12-22\",\"2022-12-23\",\"2022-12-26\",\"2022-12-27\",\"2022-12-28\",\"2022-12-29\",\"2022-12-30\",\"2023-01-03\",\"2023-01-04\",\"2023-01-05\",\"2023-01-06\",\"2023-01-09\",\"2023-01-10\",\"2023-01-11\",\"2023-01-12\",\"2023-01-13\",\"2023-01-16\",\"2023-01-17\",\"2023-01-18\",\"2023-01-19\",\"2023-01-20\",\"2023-01-30\",\"2023-01-31\",\"2023-02-01\",\"2023-02-02\",\"2023-02-03\",\"2023-02-06\",\"2023-02-07\",\"2023-02-08\",\"2023-02-09\",\"2023-02-10\",\"2023-02-13\",\"2023-02-14\",\"2023-02-15\",\"2023-02-16\",\"2023-02-17\",\"2023-02-20\",\"2023-02-21\",\"2023-02-22\",\"2023-02-23\",\"2023-02-24\",\"2023-02-27\",\"2023-02-28\",\"2023-03-01\"],\"y\":[null,null,null,null,null,null,79.7468354430378,82.57294511091328,57.37572111387301,40.40639103501499,29.413317914939086,28.968217139478327,54.227539139877976,56.95046712094351,44.60726060705147,35.50425391669486,25.581064853257367,34.56978983460072,27.62213938681536,25.543393583178876,22.36725337224261,17.407235014330837,26.219644190896208,40.72115036198661,48.32924753531781,58.65307215446654,63.89390034222551,65.66318021274594,61.99029875410149,54.06653550279513,44.09601736355228,31.924438785637793,39.92212947166057,49.50477897815361,58.00133350046944,33.98427321807704,46.682105004459544,59.91558280187094,66.31103616040382,67.66777320529536,73.75224769210874,74.24527616522707,66.1024011280651,69.42825789420615,70.7829858417031,72.81879776861163,59.33850575341311,68.05342452085603,66.79895153338327,72.54286342308738,49.36342154694091,57.70946735100332,56.978310332805016,69.26477381020977,52.83047114431639,49.954088339977,45.14666306023328,58.974583822691685,72.72887418086248,59.8403976584911,53.84344254709582,66.81304875220465,65.9376467121676,57.75608832143123,51.17136903482729,42.42821605776269,34.47594776921893,37.0092168507978,34.153198218309925,44.630549917756646,35.05722926323446,24.304632087449555,27.657139883822758,23.60771424880288,19.461627954744483,16.141429631418145,36.0701587739092,37.78812932435741,28.484790254163308,24.16892125463229,36.962432456597206,52.64667421478675,55.14251636684293,57.436957639751085,63.24407268272798,58.626560809908504,68.29587063054437,59.87217562640539,48.41221609201302,59.997937882605925,61.125394607154085,65.99173575268924,58.357162065261136,57.987421204058286,76.35148509613595,83.67300506934635,69.69536338832941,81.06910213859213,73.37142127558604,74.57579453334463,71.81751764537785,72.54409376196416,69.12575331014635,73.56575106388539,77.22267476151029,78.94249590536681,78.65844852653044,73.68372967828846,38.088330952470706,35.86031880103182,58.5979699667637,64.49204875030263,61.91270744636107,58.39691715710137,49.6782946726698,35.94812785837039,51.74578499401207,59.21524973105144],\"type\":\"scatter\",\"xaxis\":\"x3\",\"yaxis\":\"y5\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,0.94],\"matches\":\"x3\",\"showticklabels\":false,\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"type\":\"category\",\"range\":[-2,117],\"rangeslider\":{\"visible\":false}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.44,1.0],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\",\"range\":[905.0260467529297,1197.650994873047]},\"yaxis2\":{\"anchor\":\"x\",\"overlaying\":\"y\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"xaxis2\":{\"anchor\":\"y3\",\"domain\":[0.0,0.94],\"matches\":\"x3\",\"showticklabels\":false,\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"type\":\"category\",\"range\":[-2,117]},\"yaxis3\":{\"anchor\":\"x2\",\"domain\":[0.22,0.33999999999999997],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"yaxis4\":{\"anchor\":\"x2\",\"overlaying\":\"y3\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"xaxis3\":{\"anchor\":\"y5\",\"domain\":[0.0,0.94],\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"minor\":{\"nticks\":5,\"ticklen\":5,\"ticks\":\"outside\"},\"nticks\":11,\"ticklen\":10,\"ticks\":\"outside\",\"type\":\"category\",\"range\":[-2,117]},\"yaxis5\":{\"anchor\":\"x3\",\"domain\":[0.0,0.12],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"yaxis6\":{\"anchor\":\"x3\",\"overlaying\":\"y5\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"annotations\":[{\"font\":{\"size\":16},\"showarrow\":false,\"text\":\"volume\",\"x\":0.47,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":0.33999999999999997,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{\"size\":16},\"showarrow\":false,\"text\":\"rsi\",\"x\":0.47,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":0.12,\"yanchor\":\"bottom\",\"yref\":\"paper\"}],\"hovermode\":\"x unified\",\"plot_bgcolor\":\"rgba(0,0,0,0)\",\"height\":800},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('007aea95-5a7d-4bc2-9432-21f0de2c6a4a');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from omicron.plotting.candlestick import Candlestick\n", "\n", "cs = Candlestick(bars, height=800)\n", "cs.plot()"]}, {"cell_type": "markdown", "id": "1bdf1834-1b5f-4b71-9c03-0f5e0fa886a9", "metadata": {}, "source": ["## 回测\n", "\n", "您可以像omicron.strategy.sma.SMAStrategy策略一样，编写回测并执行（注意，在本环境下运行时，您需要事先补充分钟级数据）"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}