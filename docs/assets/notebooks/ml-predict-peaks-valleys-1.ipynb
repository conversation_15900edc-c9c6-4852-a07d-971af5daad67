{"cells": [{"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["from xgboost import XGBClassifier\n", "\n", "from sklearn.model_selection import train_test_split\n", "import pickle\n", "\n", "with open(\"/data/pv-labels.pkl\", \"rb\") as f:\n", "    raw = pickle.load(f)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "def wr_up(bars):\n", "    shadow = bars[\"high\"] - bars[\"close\"]\n", "    return shadow/(bars[\"high\"] - bars[\"low\"]+1e-7)\n", "\n", "def wr_down(bars):\n", "    shadow = bars[\"close\"] - bars[\"low\"]\n", "    return shadow/(bars[\"high\"] - bars[\"low\"]+1e-7)\n", "\n", "def upper_shadow(bars):\n", "    shadow = bars[\"high\"] - np.maximum(bars[\"open\"], bars[\"close\"])\n", "    \n", "    # 正则化\n", "    return shadow/(bars[\"high\"] - bars[\"low\"]+1e-7)\n", "\n", "def lower_shadow(bars):\n", "    shadow = np.minimum(bars[\"open\"], bars[\"close\"]) - bars[\"low\"]\n", "    \n", "    # 正则化\n", "    return shadow/(bars[\"high\"] - bars[\"low\"]+1e-7)\n", "\n", "bars = raw[[\"open\", \"high\", \"low\", \"close\"]].to_records(index=False)\n", "data = {\n", "    \"label\": raw[\"flag\"].values,\n", "    \"data\": np.vstack(\n", "        (wr_up(bars), \n", "         wr_down(bars), \n", "         upper_shadow(bars), \n", "         lower_shadow(bars)\n", "        )\n", "        ).T\n", "}"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["X_train, X_test, y_train, y_test = train_test_split(data['data'], data['label'] + 1, test_size=.2)\n", "# create model instance\n", "bst = XGBClassifier(n_estimators=3, max_depth=2, learning_rate=0.5)\n", "# fit model\n", "bst.fit(X_train, y_train)\n", "# make predictions\n", "preds = bst.predict(X_test)\n"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ACC: 0.922\n", "Recall:92.2%\n", "F1-score: 88.4%\n", "Precesion:84.9%\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/pluto/lib/python3.8/site-packages/sklearn/metrics/_classification.py:1318: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, msg_start, len(result))\n"]}], "source": ["from sklearn.metrics import *\n", "\n", "# https://stackoverflow.com/questions/52269187/facing-valueerror-target-is-multiclass-but-average-binary\n", "acc = accuracy_score(y_test,preds)\n", "print(f\"ACC: {acc:.3f}\")\n", "\n", "recall = recall_score(y_test,preds, average='weighted')\n", "print(f\"Recall:{recall:.1%}\")\n", "\n", "f1 = f1_score(y_test,preds, average='weighted')\n", "print(f\"F1-score: {f1:.1%}\")\n", "\n", "precision = precision_score(y_test,preds, average='weighted')\n", "print(f\"Precesion:{precision:.1%}\")\n", "mx = confusion_matrix(y_test,preds)\n"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "\n", "sns.heatmap(mx/np.sum(mx), cmap=\"YlGnBu\", annot=True, fmt=\".1%\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "pluto", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.17"}}, "nbformat": 4, "nbformat_minor": 2}