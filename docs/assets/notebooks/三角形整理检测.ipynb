{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["最近和一位做量化的私募大佬聊了一下行情，他给我发了这张图片。\n", "\n", "<div style='width:30%;text-align:center;margin: 0 auto 1rem'>\n", "<img src='https://images.jieyu.ai/images/2024/10/roger-trend.jpg'>\n", "<span style='font-size:0.6rem'></span>\n", "</div>\n", "\n", "这个底部点位，他**又一次**精准命中了（3143那个点，不是3066。周五上证实际下探到3152点）。不过，我更好奇的是他的研究方法，也就图的下半部分。知道大致的底之后，再结合缺口、前低等一些信息，确实有可能比较精准地预测底部点位。\n", "\n", "通过akshare获取指定日期间的行情数据。\n", "\n", "我当时就回了一句，最近忙着上课，等有时间了，把这个三角形检测写出来。\n", "\n", "这个检测并不难，写一个教学示例，一个小时的时间足够了。\n", "\n", "在分享我的算法之前，先推荐一个外网的[方案](https://www.youtube.com/watch?v=b5m7BZAHysk)。同样是教学代码，显然不如我随手写的优雅，先小小自得一下。不过，这样的好处就是，他的代码可能更容易读懂。\n", "\n", "所谓旗形整理（或者说三角形检测），就是下面这张图：\n", "\n", "\n", "<div style='width:50%;text-align:center;margin: 0 auto 1rem'>\n", "<img src='https://images.jieyu.ai/images/2024/10/flag-pattern-1.jpg'>\n", "<span style='font-size:0.6rem'></span>\n", "</div>\n", "\n", "\n", "\n", "在这张图，每次上涨的局部高点连接起来，构成压力线；而下跌的局部低点连起来，构成支撑线。\n", "\n", "如果我们再在开始的位置画一条竖线，就构成了一个小旗帜，这就是旗形的来由。\n", "\n", "旗形整理的特别之处是，整理何时结束似乎是可以预测的，因为这两条线之间的交易空间会越来越窄。\n", "\n", "**当它小于一个ATR时**，就是整理必须结束，即将选择方向的时候。\n", "\n", "下图显示了随时间推移，震荡幅度越来越小的情况。\n", "\n", "<div style='width:50%;text-align:center;margin: 0 auto 1rem'>\n", "<img src='https://images.jieyu.ai/images/2024/10/flag-pattern-2.jpg'>\n", "<span style='font-size:0.6rem'></span>\n", "</div>\n", "\n", "\n", "最终，股价会选择方向。一旦选择方向，就往往会有一波较大的行情（或者下跌）：\n", "\n", "<div style='width:50%;text-align:center;margin: 0 auto 1rem'>\n", "<img src='https://images.jieyu.ai/images/2024/10/flag-pattern-3.jpg'>\n", "<span style='font-size:0.6rem'></span>\n", "</div>\n", "\n", "\n", "所以，能够自动化检测旗形整理，有以下作用：\n", "\n", "\n", "\n", "1. 如果当前处理在旗形整理中，可以设定合理的波段期望。\n", "2. 检测临近整理结束，可以减仓等待方向。\n", "3. 一旦方向确定，立即加仓。\n", "\n", "现在，我们就来看如何实现。首先，我们有这样一个标的：\n", "\n", "<div style='width:50%;text-align:center;margin: 0 auto 1rem'>\n", "<img src='https://images.jieyu.ai/images/2024/10/605158-1.png'>\n", "<span style='font-size:0.6rem'></span>\n", "</div>\n", "\n", "\n", "这是已经上涨后的。我们再来看它上涨前的：\n", "\n", "<div style='width:50%;text-align:center;margin: 0 auto 1rem'>\n", "<img src='https://images.jieyu.ai/images/2024/10/605158.png'>\n", "<span style='font-size:0.6rem'></span>\n", "</div>\n", "\n", "\n", "肉眼来看，一个旗形整理似有若无。\n", "\n", "我们的算法分这样几步：\n", "\n", "1. 找到每阶段的峰和谷的坐标\n", "2. 通过这些坐标及它们的收盘价，进行趋势线拟合\n", "3. 通过np.poly1d生成趋势线\n", "4. 将趋势线和k线图画在一张图上\n", "\n", "首先，我们来获取相关数据。\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "      <th>outstanding_share</th>\n", "      <th>turnover</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>185</th>\n", "      <td>2024-10-14</td>\n", "      <td>8.53</td>\n", "      <td>9.00</td>\n", "      <td>8.36</td>\n", "      <td>9.00</td>\n", "      <td>13635627.0</td>\n", "      <td>120173201.0</td>\n", "      <td>511420000.0</td>\n", "      <td>0.026662</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186</th>\n", "      <td>2024-10-15</td>\n", "      <td>9.05</td>\n", "      <td>9.56</td>\n", "      <td>9.05</td>\n", "      <td>9.11</td>\n", "      <td>22525713.0</td>\n", "      <td>209376045.0</td>\n", "      <td>511420000.0</td>\n", "      <td>0.044045</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>2024-10-16</td>\n", "      <td>9.05</td>\n", "      <td>9.54</td>\n", "      <td>8.89</td>\n", "      <td>9.22</td>\n", "      <td>14126252.0</td>\n", "      <td>129985106.0</td>\n", "      <td>511420000.0</td>\n", "      <td>0.027622</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>2024-10-17</td>\n", "      <td>9.27</td>\n", "      <td>9.49</td>\n", "      <td>9.08</td>\n", "      <td>9.39</td>\n", "      <td>13667642.0</td>\n", "      <td>127023312.0</td>\n", "      <td>511420000.0</td>\n", "      <td>0.026725</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>2024-10-18</td>\n", "      <td>9.32</td>\n", "      <td>9.95</td>\n", "      <td>9.27</td>\n", "      <td>9.88</td>\n", "      <td>14676130.0</td>\n", "      <td>141660370.0</td>\n", "      <td>511420000.0</td>\n", "      <td>0.028697</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           date  open  high   low  close      volume       amount  \\\n", "185  2024-10-14  8.53  9.00  8.36   9.00  13635627.0  120173201.0   \n", "186  2024-10-15  9.05  9.56  9.05   9.11  22525713.0  209376045.0   \n", "187  2024-10-16  9.05  9.54  8.89   9.22  14126252.0  129985106.0   \n", "188  2024-10-17  9.27  9.49  9.08   9.39  13667642.0  127023312.0   \n", "189  2024-10-18  9.32  9.95  9.27   9.88  14676130.0  141660370.0   \n", "\n", "     outstanding_share  turnover  \n", "185        511420000.0  0.026662  \n", "186        511420000.0  0.044045  \n", "187        511420000.0  0.027622  \n", "188        511420000.0  0.026725  \n", "189        511420000.0  0.028697  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import akshare as ak\n", "import pandas as pd\n", "import numpy as np\n", "import datetime\n", "import plotly.graph_objects as go\n", "\n", "code = \"sh605158\"\n", "\n", "def get_bars(symbol, start, end):\n", "    start_ = f\"{start.year:04d}{start.month:02d}{start.day:02d}\"\n", "    end_ = f\"{end.year:04d}{end.month:02d}{end.day:02d}\"\n", "    bars = ak.stock_zh_a_daily(symbol=code, start_date=start_, end_date = end_, adjust=\"qfq\")\n", "\n", "    return bars\n", "\n", "df = get_bars(code, datetime.date(2024,1,1), datetime.date(2024,10,18))\n", "df.tail()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["绘制未上涨前整理期图形"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"close": [7.84, 7.45, 7.54, 8.3, 9.14, 8.39, 8.95, 8.77, 8.41, 8.25, 7.97, 7.52, 7.54, 7.3, 6.85, 6.83, 7.02, 7.11, 7.1, 6.86, 6.92, 6.52, 6.52, 6.22, 5.6, 5.58, 5.05, 5.55, 5.89, 6.04, 6.13, 6.2, 6.52, 6.64, 6.73, 6.25, 6.54, 6.57, 6.72, 6.58, 6.63, 6.88, 6.85, 6.82, 6.83, 6.87, 6.9, 7.02, 7.16, 7.35, 7.31, 7.36, 7.41, 7.41, 7.62, 7.41, 7.46, 7.49, 7.78, 8.11, 7.83, 7.63, 7.75, 7.51, 7.61, 7.73, 7.38, 6.69, 7.24, 7.51, 7.11, 6.98, 6.99, 7.28, 7.17, 6.88, 6.92, 6.83, 7.09, 7.22, 7.06, 7.18, 7.06, 6.86, 6.92, 6.94, 7.02, 6.98, 7.09, 6.93, 6.93, 6.72, 6.74, 6.76, 6.66, 6.66, 6.59, 6.58, 6.4, 6.35, 6.15, 6.05, 6.2, 6.1, 6.2, 6.12, 6.17, 6.07, 6.2, 6.26, 6.13, 6.14, 6.75, 6.68, 6.58, 6.38, 6.43, 6.49, 6.37, 6.22, 5.95, 5.98, 6, 6.15, 6.24, 6.34, 6.26, 6.39, 6.45, 6.49, 6.49, 6.26, 6.23, 6.1, 6.15, 6.15, 6.32, 6.49, 6.38, 6.51, 6.47, 6.32, 6.19, 6.46, 6.52, 6.44, 6.61, 6.63, 6.67, 6.54, 6.63, 6.94, 6.88, 6.76, 7.04, 6.82, 6.9, 7, 6.94, 7.03, 6.97, 6.84, 6.73, 6.77, 6.63, 6.6, 6.44, 6.38, 6.36, 6.37, 6.18, 6.16, 6.16, 6.23, 6.21, 6.53, 6.74, 6.89, 7.19], "decreasing": {"fillcolor": "#3DAA70", "line": {"color": "#3DAA70"}}, "high": [7.84, 7.76, 7.56, 8.3, 9.14, 9.31, 9.22, 8.82, 8.73, 8.52, 8.2, 8.02, 7.59, 7.53, 7.38, 6.94, 7.02, 7.14, 7.27, 7.11, 7.4, 6.99, 6.7, 6.73, 6.22, 5.8, 5.61, 5.55, 5.99, 6.07, 6.34, 6.24, 6.53, 6.78, 6.73, 6.87, 6.54, 6.62, 6.8, 6.69, 6.76, 6.88, 6.97, 6.89, 6.84, 6.95, 6.99, 7.03, 7.18, 7.35, 7.48, 7.4, 7.53, 7.67, 7.62, 7.9, 7.51, 7.52, 7.81, 8.14, 7.99, 7.97, 7.76, 7.77, 7.74, 7.82, 7.72, 7.4, 7.28, 7.51, 7.52, 7.45, 7.13, 7.28, 7.36, 7, 6.95, 6.96, 7.11, 7.23, 7.24, 7.24, 7.21, 6.98, 6.98, 7.04, 7.06, 7.05, 7.13, 7.13, 7.03, 6.95, 6.86, 6.81, 6.78, 6.8, 6.73, 6.63, 6.66, 6.43, 6.36, 6.23, 6.24, 6.25, 6.21, 6.21, 6.19, 6.17, 6.2, 6.28, 6.35, 6.18, 6.75, 7.09, 6.59, 6.46, 6.5, 6.69, 6.49, 6.39, 6.25, 6.01, 6.08, 6.24, 6.37, 6.4, 6.33, 6.46, 6.49, 6.57, 6.62, 6.49, 6.3, 6.27, 6.23, 6.22, 6.35, 6.75, 6.44, 6.53, 6.52, 6.48, 6.35, 6.47, 6.54, 6.52, 6.62, 6.72, 6.7, 6.69, 6.65, 6.95, 7.1, 6.92, 7.05, 7.04, 6.91, 7.02, 7.12, 7.08, 7.11, 6.97, 6.84, 6.81, 6.83, 6.63, 6.62, 6.47, 6.41, 6.53, 6.41, 6.23, 6.23, 6.28, 6.27, 6.57, 6.75, 6.98, 7.22], "increasing": {"fillcolor": "rgba(255,255,255,0.9)", "line": {"color": "#FF4136"}}, "line": {"width": 1}, "low": [7.39, 7.27, 7.18, 7.48, 8.66, 8.26, 8.26, 8.39, 8.38, 8.09, 7.67, 7.52, 7.29, 7.26, 6.8, 6.7, 6.69, 6.85, 7.02, 6.83, 6.86, 6.5, 6.44, 6.07, 5.6, 5.05, 5.03, 5.1, 5.56, 5.79, 5.92, 6.03, 6.22, 6.47, 6.51, 6.24, 6.19, 6.47, 6.46, 6.54, 6.52, 6.6, 6.7, 6.73, 6.74, 6.81, 6.79, 6.83, 7.04, 7.12, 7.2, 7.17, 7.16, 7.38, 7.26, 7.39, 7.26, 7.37, 7.48, 7.77, 7.72, 7.58, 7.58, 7.47, 7.44, 7.59, 7.1, 6.69, 6.84, 7.19, 7.08, 6.95, 6.94, 6.96, 7.15, 6.72, 6.61, 6.82, 6.86, 7.07, 7.02, 7.07, 6.96, 6.82, 6.84, 6.87, 6.95, 6.86, 6.97, 6.91, 6.9, 6.69, 6.71, 6.65, 6.63, 6.59, 6.57, 6.52, 6.31, 6.27, 6.14, 5.95, 6.05, 6.03, 6.08, 6.09, 6.07, 6.05, 6.08, 6.16, 6.13, 6.03, 6.15, 6.51, 6.28, 6.32, 6.2, 6.43, 6.34, 6.16, 5.91, 5.72, 5.85, 5.89, 6.1, 6.25, 6.18, 6.21, 6.22, 6.39, 6.38, 6.23, 6.17, 6.1, 6.06, 6.09, 6.13, 6.3, 6.34, 6.38, 6.41, 6.3, 6.17, 6.2, 6.41, 6.41, 6.44, 6.49, 6.56, 6.49, 6.46, 6.65, 6.83, 6.73, 6.72, 6.77, 6.74, 6.82, 6.89, 6.85, 6.9, 6.78, 6.66, 6.68, 6.62, 6.45, 6.39, 6.33, 6.28, 6.27, 6.16, 6.15, 6.03, 6.06, 6.15, 6.21, 6.49, 6.82, 6.8], "name": "K 线", "open": [7.39, 7.76, 7.4, 7.51, 8.93, 8.79, 8.26, 8.44, 8.6, 8.3, 8.14, 7.98, 7.35, 7.52, 7.28, 6.83, 6.84, 6.89, 7.08, 7.08, 7.27, 6.88, 6.51, 6.6, 6.18, 5.36, 5.6, 5.13, 5.56, 5.89, 5.93, 6.04, 6.25, 6.58, 6.57, 6.77, 6.23, 6.55, 6.59, 6.66, 6.56, 6.63, 6.8, 6.8, 6.8, 6.85, 6.87, 6.85, 7.06, 7.16, 7.39, 7.32, 7.31, 7.38, 7.37, 7.58, 7.26, 7.44, 7.48, 7.79, 7.94, 7.8, 7.59, 7.7, 7.52, 7.63, 7.67, 7.37, 6.95, 7.25, 7.43, 7.24, 7.06, 6.96, 7.21, 6.97, 6.76, 6.88, 6.86, 7.12, 7.21, 7.14, 7.16, 6.97, 6.84, 6.91, 6.97, 7.05, 6.98, 7.09, 6.97, 6.93, 6.78, 6.76, 6.78, 6.64, 6.71, 6.54, 6.57, 6.43, 6.36, 6.18, 6.05, 6.25, 6.11, 6.21, 6.11, 6.11, 6.09, 6.21, 6.22, 6.1, 6.15, 7.09, 6.5, 6.46, 6.34, 6.44, 6.49, 6.35, 6.22, 5.93, 5.96, 5.98, 6.14, 6.25, 6.3, 6.21, 6.36, 6.42, 6.52, 6.49, 6.25, 6.23, 6.1, 6.12, 6.13, 6.3, 6.44, 6.38, 6.5, 6.44, 6.29, 6.24, 6.45, 6.49, 6.44, 6.61, 6.66, 6.65, 6.48, 6.72, 6.95, 6.85, 6.73, 7, 6.82, 6.9, 6.93, 6.86, 6.96, 6.97, 6.82, 6.7, 6.73, 6.6, 6.6, 6.38, 6.38, 6.36, 6.37, 6.16, 6.19, 6.06, 6.25, 6.21, 6.49, 6.84, 6.87], "type": "candlestick", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178]}], "layout": {"template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["RED = \"#FF4136\"\n", "GREEN = \"#3DAA70\"\n", "\n", "def candlestick(df):\n", "    candle = go.Candlestick(x=df.index,\n", "                    open=df['open'],\n", "                    high=df['high'],\n", "                    low=df['low'],\n", "                    close=df['close'],\n", "                    line=dict({\"width\": 1}),\n", "                    name=\"<PERSON> 线\",\n", "                    increasing = {\n", "                        \"fillcolor\":\"rgba(255,255,255,0.9)\",\n", "                        \"line\": dict({\"color\": RED})\n", "                    },\n", "                    decreasing = {\n", "                        \"fillcolor\": GREEN, \n", "                        \"line\": dict(color =  GREEN)\n", "                    })\n", "\n", "    fig = go.Figure(data=[candle])\n", "\n", "    fig.show()\n", "\n", "candlestick(df.iloc[:179])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 定义算法"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def find_runs(x):\n", "    x = np.asanyarray(x)\n", "    n = len(x)\n", "\n", "    loc_run_start = np.empty(n, dtype=bool)\n", "    loc_run_start[0] = True\n", "    np.not_equal(x[:-1], x[1:], out=loc_run_start[1:])\n", "    run_starts = np.nonzero(loc_run_start)[0]\n", "\n", "    # find run values\n", "    run_values = x[loc_run_start]\n", "\n", "    # find run lengths\n", "    run_lengths = np.diff(np.append(run_starts, n))\n", "\n", "    return run_values, run_starts, run_lengths\n", "\n", "def find_peak_pivots(df, win):\n", "    local_high = df.close.rolling(win).apply(lambda x: x.argmax()== win-1)\n", "    local_high[:win] = 0\n", "    \n", "    v,s,l = find_runs(local_high)\n", "\n", "    peaks = []\n", "    i = 0\n", "    while i < len(v):\n", "        if l[i] >= win // 2:\n", "            if s[i] > 0:\n", "                peaks.append(s[i] - 1)\n", "        for j in range(i+1, len(v)):\n", "            if l[j] >= win // 2:\n", "                peaks.append(s[j] - 1)\n", "                i = j\n", "        if j == len(v)-1:\n", "            break\n", "\n", "    return peaks\n", "\n", "def find_valley_pivots(df, win):\n", "    local_min = df.close.rolling(win).apply(lambda x: x.argmin()== win-1)\n", "    local_min[:win] = 0\n", "    \n", "    v,s,l = find_runs(local_min)\n", "\n", "    valleys = []\n", "    i = 0\n", "    while i < len(v):\n", "        if l[i] >= win // 2:\n", "            if s[i] > 0:\n", "                valleys.append(s[i] - 1)\n", "        for j in range(i+1, len(v)):\n", "            if l[j] >= win // 2:\n", "                valleys.append(s[j] - 1)\n", "                i = j\n", "        if j == len(v)-1:\n", "            break\n", "\n", "    return valleys\n", "\n", "\n", "def trendline(df):\n", "    peaks = find_peak_pivots(df, 20)\n", "    valleys = find_valley_pivots(df, 20)\n", "\n", "    p = np.polyfit(x=peaks, y = df.close[peaks].values, deg=1)\n", "    upper_trendline = np.poly1d(p)(np.arange(0, len(df)))\n", "\n", "    v = np.polyfit(x=valleys, y = df.close[valleys].values, deg=1)\n", "    lower_trendline = np.poly1d(v)(np.arange(0, len(df)))\n", "\n", "    candle = go.Candlestick(x=df.index,\n", "                    open=df['open'],\n", "                    high=df['high'],\n", "                    low=df['low'],\n", "                    close=df['close'],\n", "                    line=dict({\"width\": 1}),\n", "                    name=\"<PERSON> 线\",\n", "                    increasing = {\n", "                        \"fillcolor\": \"rgba(255,255,255,0.9)\",\n", "                        \"line\": dict({\"color\": RED})\n", "                    },\n", "                    decreasing = dict(fillcolor =GREEN, line = dict(color =  GREEN))\n", "                    )\n", "    upper_trace = go.<PERSON><PERSON>(x=df.index, y=upper_trendline, mode='lines', name='压力线')\n", "    lower_trace = go.<PERSON><PERSON>(x=df.index, y=lower_trendline, mode='lines', name='支撑线')\n", "    fig = go.Figure(data=[candle,lower_trace, upper_trace ])\n", "\n", "    fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 形态检测\n", "\n", "最后，我们对该标的在上涨之前的形态进行检测："]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"close": [11.62, 10.01, 11.02, 11.7, 11.56, 12.29, 12.84, 13.12, 14.05, 13.73, 13.81, 12.98, 12.44, 11.68, 11.6, 12.13, 10.56, 10.62, 12.1, 12.92, 13.21, 13.26, 12.71, 13.86, 15.26, 13.84, 13.22, 11.71, 12.39, 12.6, 12.66, 12.26, 10.87, 11.52, 10.77, 10.92, 11.81, 12.32, 12.19, 12.21, 12.26, 11.86, 12.42, 12.21, 12.76, 12.69, 12.19, 11.73, 11.82, 11.31, 11.3, 11.39, 11.28, 11.37, 11.02, 11.39, 11.44, 11.26, 11.44, 11.46, 11.02, 10.36, 10.03, 10.01, 10.15, 10.07, 10.14, 9.99, 9.6, 8.89, 9.04, 9.38, 9.25, 8.93, 8.81, 8.27, 8.76, 9.13, 8.95, 8.75, 9.42, 9.62, 9.26, 10.82, 10.62, 10.38, 10.38, 10.24, 9.97, 9.97, 10.09, 9.81, 10.04, 9.71, 9.71, 9.62, 9.59, 9.21, 9.37, 9.67, 9.39, 9.47, 9.47, 9.57, 9.11, 9.22, 9.15, 9.28, 9.2, 8.8, 8.94, 9.17, 9.16, 9.27, 9.76, 9.63, 9.92, 9.95, 9.55, 9.99, 9.96, 10.17, 10.2, 9.58, 9.45, 9.61, 9.9, 9.62, 9.25, 9.41, 8.95, 9.09, 9.1, 9.28, 9.21, 9.21, 9.56, 10.25, 9.75, 9.76, 10.16, 9.74, 9.24, 9.72, 9.73, 9.9, 10.13, 11.09, 10.46, 10.07, 9.79, 9.91, 9.77, 9.59, 9.52, 9.35, 9.55, 9.48, 9.63, 9.04, 9.84, 9.44, 9.15, 9.04, 9.32, 9.12, 8.85, 8.92, 9.11, 9.41, 9.13, 9.4, 9.44, 9.63, 9.5, 9.17, 9.35, 9.18, 9.4], "decreasing": {"fillcolor": "#3DAA70", "line": {"color": "#3DAA70"}}, "high": [11.75, 11.84, 11.42, 12.73, 11.92, 12.59, 13.48, 13.4, 14.26, 14.57, 14.16, 13.95, 13.59, 12.68, 11.9, 12.58, 12.13, 11.12, 12.17, 13.7, 13.69, 13.57, 13.5, 14, 16.1, 15.08, 14.52, 13.22, 12.61, 13.16, 13.14, 12.88, 12.23, 11.88, 11.92, 11.46, 11.89, 12.65, 13.07, 12.59, 12.65, 12.04, 12.42, 13.34, 13.28, 12.92, 12.74, 12.1, 12.04, 11.87, 11.47, 11.42, 11.53, 11.67, 11.52, 11.56, 11.61, 11.6, 11.47, 11.72, 11.41, 11.02, 10.63, 10.52, 10.45, 10.17, 10.21, 10.33, 10.02, 9.56, 9.15, 9.97, 9.58, 9.44, 9.28, 8.98, 8.88, 9.61, 9.18, 9.04, 9.46, 10.07, 9.48, 11.42, 11.54, 10.95, 10.74, 10.46, 10.35, 10.22, 10.28, 10.05, 10.15, 10.29, 9.93, 9.75, 9.74, 9.63, 9.4, 10.09, 9.73, 9.56, 9.71, 9.73, 9.55, 9.28, 9.25, 9.33, 9.45, 9.25, 9.02, 9.21, 9.31, 9.34, 9.9, 9.74, 9.92, 10.53, 9.91, 10.09, 10.53, 10.21, 10.32, 10.08, 9.72, 9.74, 10.21, 9.79, 9.79, 9.55, 9.35, 9.21, 9.34, 9.39, 9.34, 9.5, 9.61, 10.41, 10.18, 10.07, 10.83, 10.11, 9.8, 10.25, 9.88, 10.15, 10.2, 11.74, 10.88, 10.55, 10.3, 10.11, 10.07, 9.89, 9.76, 9.56, 9.59, 9.6, 9.85, 9.54, 9.91, 9.6, 9.41, 9.33, 9.4, 9.21, 9.21, 9.05, 9.22, 9.63, 9.41, 9.49, 9.91, 10.52, 10.12, 9.42, 9.37, 9.3, 9.6], "increasing": {"fillcolor": "rgba(255,255,255,0.9)", "line": {"color": "#FF4136"}}, "line": {"width": 1}, "low": [11.26, 9.69, 9.42, 11.39, 10.97, 11.24, 11.9, 12.34, 12.85, 13.52, 13.27, 12.86, 12.43, 11.16, 11.21, 11.57, 10.53, 10.51, 10.59, 12.04, 12.15, 12.74, 12.32, 12.85, 13.2, 13.71, 13.16, 11.67, 11.69, 12.14, 12.19, 11.55, 10.87, 10.19, 10.67, 10.49, 11.24, 11.57, 11.85, 11.87, 11.9, 11.57, 11.79, 12.18, 12.33, 12.47, 11.91, 11.52, 11.57, 11.1, 11.03, 11.18, 11.24, 11.27, 10.84, 10.77, 11.26, 11.21, 11.21, 11.37, 10.96, 10.36, 9.82, 10, 9.97, 9.96, 10.06, 9.97, 9.6, 8.89, 8.9, 9.01, 9.21, 8.9, 8.6, 8.27, 8.39, 8.62, 8.71, 8.73, 8.64, 9.48, 9.21, 9.36, 10.32, 10.38, 10.3, 9.97, 9.91, 9.93, 9.73, 9.63, 9.74, 9.65, 9.63, 9.51, 9.52, 9.21, 9.11, 9.22, 9.38, 9.34, 9.41, 9.41, 9.09, 9.07, 9.06, 9.11, 9.19, 8.74, 8.79, 8.81, 9.11, 9.1, 9.21, 9.5, 9.58, 9.87, 9.4, 9.45, 9.56, 9.77, 9.83, 9.57, 9.37, 9.4, 9.55, 9.55, 9.25, 9, 8.93, 8.77, 8.97, 9.16, 9.13, 8.97, 9.13, 9.51, 9.72, 9.67, 10.06, 9.73, 9.21, 9.4, 9.41, 9.66, 9.68, 9.98, 10.33, 9.98, 9.74, 9.76, 9.75, 9.48, 9.48, 9.28, 9.32, 9.43, 9.36, 8.99, 9.01, 9.21, 9.15, 8.97, 8.96, 9.02, 8.83, 8.83, 8.88, 9.05, 9.13, 9.19, 9.44, 9.58, 9.42, 9.08, 8.96, 9.07, 9.16], "name": "K 线", "open": [11.46, 11.63, 9.89, 12.24, 11.37, 11.27, 12.11, 12.65, 12.86, 13.79, 13.61, 13.53, 13.02, 12.53, 11.46, 11.6, 11.9, 10.62, 10.72, 12.24, 12.47, 12.83, 13.26, 13.07, 14.28, 14.57, 13.45, 12.84, 11.82, 12.27, 12.36, 12.66, 12.04, 10.53, 11.15, 11.09, 11.25, 11.82, 11.98, 11.9, 12.34, 11.81, 11.87, 12.73, 12.72, 12.47, 12.66, 12.04, 11.75, 11.75, 11.45, 11.19, 11.45, 11.31, 11.31, 11.09, 11.36, 11.39, 11.36, 11.43, 11.35, 10.9, 10.44, 10.02, 9.98, 10.09, 10.14, 10.18, 9.99, 9.55, 8.97, 9.11, 9.3, 9.21, 9.1, 8.93, 8.39, 8.67, 9.02, 8.95, 8.78, 9.49, 9.21, 9.6, 10.6, 10.43, 10.67, 10.19, 10.12, 10.06, 9.88, 9.91, 9.85, 10.22, 9.69, 9.65, 9.63, 9.62, 9.28, 9.4, 9.6, 9.5, 9.52, 9.43, 9.47, 9.07, 9.22, 9.18, 9.31, 9.22, 8.79, 8.87, 9.16, 9.2, 9.29, 9.5, 9.65, 9.98, 9.88, 9.53, 9.56, 9.86, 9.96, 10.08, 9.58, 9.44, 9.55, 9.73, 9.55, 9.28, 9.31, 8.95, 9.02, 9.3, 9.34, 9.15, 9.15, 9.63, 10.15, 9.73, 10.14, 10.01, 9.66, 9.45, 9.46, 9.86, 9.75, 10.05, 10.67, 10.35, 9.95, 9.9, 9.96, 9.73, 9.65, 9.44, 9.42, 9.45, 9.39, 9.43, 9.14, 9.45, 9.29, 9.23, 8.98, 9.05, 9.1, 8.84, 8.99, 9.12, 9.29, 9.19, 9.53, 10.15, 9.58, 9.25, 9.1, 9.23, 9.17], "type": "candlestick", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178]}, {"mode": "lines", "name": "支撑线", "type": "scatter", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178], "y": [7.95683975240715, 7.963178587803758, 7.969517423200364, 7.975856258596972, 7.982195093993579, 7.988533929390186, 7.994872764786793, 8.0012116001834, 8.007550435580008, 8.013889270976614, 8.02022810637322, 8.026566941769827, 8.032905777166436, 8.039244612563042, 8.045583447959649, 8.051922283356257, 8.058261118752863, 8.06459995414947, 8.070938789546078, 8.077277624942685, 8.083616460339291, 8.0899552957359, 8.096294131132506, 8.102632966529113, 8.10897180192572, 8.115310637322327, 8.121649472718934, 8.127988308115542, 8.134327143512149, 8.140665978908755, 8.147004814305362, 8.15334364970197, 8.159682485098577, 8.166021320495183, 8.172360155891791, 8.178698991288398, 8.185037826685004, 8.191376662081613, 8.19771549747822, 8.204054332874826, 8.210393168271434, 8.21673200366804, 8.223070839064647, 8.229409674461255, 8.235748509857862, 8.242087345254468, 8.248426180651077, 8.254765016047683, 8.26110385144429, 8.267442686840896, 8.273781522237504, 8.280120357634111, 8.286459193030717, 8.292798028427326, 8.299136863823932, 8.305475699220539, 8.311814534617147, 8.318153370013754, 8.32449220541036, 8.330831040806968, 8.337169876203575, 8.343508711600181, 8.34984754699679, 8.356186382393396, 8.362525217790003, 8.36886405318661, 8.375202888583217, 8.381541723979824, 8.38788055937643, 8.394219394773039, 8.400558230169645, 8.406897065566252, 8.41323590096286, 8.419574736359467, 8.425913571756073, 8.432252407152681, 8.438591242549288, 8.444930077945894, 8.451268913342503, 8.45760774873911, 8.463946584135716, 8.470285419532324, 8.47662425492893, 8.482963090325537, 8.489301925722145, 8.495640761118752, 8.501979596515358, 8.508318431911965, 8.514657267308573, 8.52099610270518, 8.527334938101786, 8.533673773498395, 8.540012608895001, 8.546351444291608, 8.552690279688216, 8.559029115084822, 8.565367950481429, 8.571706785878037, 8.578045621274644, 8.58438445667125, 8.590723292067858, 8.597062127464465, 8.603400962861071, 8.609739798257678, 8.616078633654286, 8.622417469050893, 8.6287563044475, 8.635095139844108, 8.641433975240714, 8.64777281063732, 8.654111646033929, 8.660450481430535, 8.666789316827142, 8.67312815222375, 8.679466987620357, 8.685805823016963, 8.692144658413572, 8.698483493810178, 8.704822329206785, 8.711161164603393, 8.7175, 8.723838835396606, 8.730177670793214, 8.73651650618982, 8.742855341586427, 8.749194176983034, 8.755533012379642, 8.761871847776249, 8.768210683172855, 8.774549518569463, 8.78088835396607, 8.787227189362676, 8.793566024759285, 8.799904860155891, 8.806243695552498, 8.812582530949106, 8.818921366345712, 8.825260201742319, 8.831599037138925, 8.837937872535534, 8.84427670793214, 8.850615543328747, 8.856954378725355, 8.863293214121962, 8.869632049518568, 8.875970884915176, 8.882309720311783, 8.88864855570839, 8.894987391104998, 8.901326226501604, 8.90766506189821, 8.914003897294819, 8.920342732691426, 8.926681568088032, 8.93302040348464, 8.939359238881247, 8.945698074277853, 8.952036909674462, 8.958375745071068, 8.964714580467675, 8.971053415864283, 8.97739225126089, 8.983731086657496, 8.990069922054102, 8.99640875745071, 9.002747592847317, 9.009086428243924, 9.015425263640532, 9.021764099037139, 9.028102934433745, 9.034441769830353, 9.04078060522696, 9.047119440623566, 9.053458276020175, 9.059797111416781, 9.066135946813388, 9.072474782209994, 9.078813617606603, 9.085152453003209]}, {"mode": "lines", "name": "压力线", "type": "scatter", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178], "y": [15.267378105409792, 15.230943231947986, 15.194508358486178, 15.158073485024373, 15.121638611562567, 15.08520373810076, 15.048768864638953, 15.012333991177147, 14.975899117715342, 14.939464244253536, 14.903029370791728, 14.866594497329922, 14.830159623868116, 14.79372475040631, 14.757289876944503, 14.720855003482697, 14.684420130020891, 14.647985256559085, 14.611550383097278, 14.575115509635472, 14.538680636173666, 14.50224576271186, 14.465810889250053, 14.429376015788247, 14.39294114232644, 14.356506268864635, 14.320071395402827, 14.283636521941022, 14.247201648479216, 14.210766775017408, 14.174331901555602, 14.137897028093796, 14.10146215463199, 14.065027281170185, 14.028592407708377, 13.992157534246571, 13.955722660784765, 13.919287787322958, 13.882852913861152, 13.846418040399346, 13.80998316693754, 13.773548293475733, 13.737113420013927, 13.70067854655212, 13.664243673090315, 13.627808799628507, 13.591373926166701, 13.554939052704896, 13.51850417924309, 13.482069305781282, 13.445634432319476, 13.40919955885767, 13.372764685395865, 13.336329811934057, 13.299894938472251, 13.263460065010445, 13.227025191548638, 13.190590318086832, 13.154155444625026, 13.11772057116322, 13.081285697701414, 13.044850824239607, 13.0084159507778, 12.971981077315995, 12.935546203854187, 12.899111330392381, 12.862676456930576, 12.82624158346877, 12.789806710006964, 12.753371836545156, 12.71693696308335, 12.680502089621545, 12.644067216159737, 12.607632342697931, 12.571197469236125, 12.53476259577432, 12.498327722312514, 12.461892848850706, 12.4254579753889, 12.389023101927094, 12.352588228465287, 12.31615335500348, 12.279718481541675, 12.243283608079867, 12.206848734618061, 12.170413861156256, 12.13397898769445, 12.097544114232644, 12.061109240770836, 12.02467436730903, 11.988239493847225, 11.951804620385417, 11.915369746923611, 11.878934873461805, 11.8425, 11.806065126538194, 11.769630253076386, 11.73319537961458, 11.696760506152774, 11.660325632690967, 11.62389075922916, 11.587455885767355, 11.551021012305549, 11.514586138843743, 11.478151265381936, 11.44171639192013, 11.405281518458324, 11.368846644996516, 11.33241177153471, 11.295976898072905, 11.259542024611097, 11.223107151149293, 11.186672277687485, 11.15023740422568, 11.113802530763873, 11.077367657302066, 11.04093278384026, 11.004497910378454, 10.968063036916647, 10.931628163454842, 10.895193289993035, 10.858758416531229, 10.822323543069423, 10.785888669607615, 10.74945379614581, 10.713018922684004, 10.676584049222196, 10.640149175760392, 10.603714302298584, 10.567279428836777, 10.530844555374973, 10.494409681913165, 10.45797480845136, 10.421539934989553, 10.385105061527746, 10.34867018806594, 10.312235314604134, 10.275800441142326, 10.239365567680522, 10.202930694218715, 10.166495820756909, 10.130060947295103, 10.093626073833295, 10.05719120037149, 10.020756326909684, 9.984321453447876, 9.947886579986072, 9.911451706524264, 9.875016833062459, 9.838581959600653, 9.802147086138845, 9.76571221267704, 9.729277339215233, 9.692842465753426, 9.656407592291622, 9.619972718829814, 9.583537845368008, 9.547102971906202, 9.510668098444395, 9.474233224982589, 9.437798351520783, 9.401363478058975, 9.36492860459717, 9.328493731135364, 9.292058857673556, 9.255623984211752, 9.219189110749944, 9.182754237288139, 9.146319363826333, 9.109884490364525, 9.07344961690272, 9.037014743440913, 9.000579869979106, 8.964144996517302, 8.927710123055494, 8.891275249593688, 8.854840376131882, 8.818405502670075, 8.781970629208269]}], "layout": {"template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["trendline(df.iloc[:179])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这个结果说明，旗形整理结束时，方向选择受大盘影响，仍有一定不确定性，但没有跌破前低，这是此后能凝聚共识、返身上涨的关键。\n", "\n", "## 另一个示例\n", "\n", "我们再来看一个最近一个月翻了7倍的标的："]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"close": [11.62, 10.01, 11.02, 11.7, 11.56, 12.29, 12.84, 13.12, 14.05, 13.73, 13.81, 12.98, 12.44, 11.68, 11.6, 12.13, 10.56, 10.62, 12.1, 12.92, 13.21, 13.26, 12.71, 13.86, 15.26, 13.84, 13.22, 11.71, 12.39, 12.6, 12.66, 12.26, 10.87, 11.52, 10.77, 10.92, 11.81, 12.32, 12.19, 12.21, 12.26, 11.86, 12.42, 12.21, 12.76, 12.69, 12.19, 11.73, 11.82, 11.31, 11.3, 11.39, 11.28, 11.37, 11.02, 11.39, 11.44, 11.26, 11.44, 11.46, 11.02, 10.36, 10.03, 10.01, 10.15, 10.07, 10.14, 9.99, 9.6, 8.89, 9.04, 9.38, 9.25, 8.93, 8.81, 8.27, 8.76, 9.13, 8.95, 8.75, 9.42, 9.62, 9.26, 10.82, 10.62, 10.38, 10.38, 10.24, 9.97, 9.97, 10.09, 9.81, 10.04, 9.71, 9.71, 9.62, 9.59, 9.21, 9.37, 9.67, 9.39, 9.47, 9.47, 9.57, 9.11, 9.22, 9.15, 9.28, 9.2, 8.8, 8.94, 9.17, 9.16, 9.27, 9.76, 9.63, 9.92, 9.95, 9.55, 9.99, 9.96, 10.17, 10.2, 9.58, 9.45, 9.61, 9.9, 9.62, 9.25, 9.41, 8.95, 9.09, 9.1, 9.28, 9.21, 9.21, 9.56, 10.25, 9.75, 9.76, 10.16, 9.74, 9.24, 9.72, 9.73, 9.9, 10.13, 11.09, 10.46, 10.07, 9.79, 9.91, 9.77, 9.59, 9.52, 9.35, 9.55, 9.48, 9.63, 9.04, 9.84, 9.44, 9.15, 9.04, 9.32, 9.12, 8.85, 8.92, 9.11, 9.41, 9.13, 9.4, 9.44, 9.63, 9.5, 9.17, 9.35, 9.18, 9.4, 9.35, 9.35, 9.58, 9.47, 9.31, 9.7, 10.01, 10.7, 13.68, 17.78, 23.11, 28.22, 22.3, 27.34, 35.54, 38.75, 48.63, 57.68, 74.98, 97.47, 77.29], "decreasing": {"fillcolor": "#3DAA70", "line": {"color": "#3DAA70"}}, "high": [11.75, 11.84, 11.42, 12.73, 11.92, 12.59, 13.48, 13.4, 14.26, 14.57, 14.16, 13.95, 13.59, 12.68, 11.9, 12.58, 12.13, 11.12, 12.17, 13.7, 13.69, 13.57, 13.5, 14, 16.1, 15.08, 14.52, 13.22, 12.61, 13.16, 13.14, 12.88, 12.23, 11.88, 11.92, 11.46, 11.89, 12.65, 13.07, 12.59, 12.65, 12.04, 12.42, 13.34, 13.28, 12.92, 12.74, 12.1, 12.04, 11.87, 11.47, 11.42, 11.53, 11.67, 11.52, 11.56, 11.61, 11.6, 11.47, 11.72, 11.41, 11.02, 10.63, 10.52, 10.45, 10.17, 10.21, 10.33, 10.02, 9.56, 9.15, 9.97, 9.58, 9.44, 9.28, 8.98, 8.88, 9.61, 9.18, 9.04, 9.46, 10.07, 9.48, 11.42, 11.54, 10.95, 10.74, 10.46, 10.35, 10.22, 10.28, 10.05, 10.15, 10.29, 9.93, 9.75, 9.74, 9.63, 9.4, 10.09, 9.73, 9.56, 9.71, 9.73, 9.55, 9.28, 9.25, 9.33, 9.45, 9.25, 9.02, 9.21, 9.31, 9.34, 9.9, 9.74, 9.92, 10.53, 9.91, 10.09, 10.53, 10.21, 10.32, 10.08, 9.72, 9.74, 10.21, 9.79, 9.79, 9.55, 9.35, 9.21, 9.34, 9.39, 9.34, 9.5, 9.61, 10.41, 10.18, 10.07, 10.83, 10.11, 9.8, 10.25, 9.88, 10.15, 10.2, 11.74, 10.88, 10.55, 10.3, 10.11, 10.07, 9.89, 9.76, 9.56, 9.59, 9.6, 9.85, 9.54, 9.91, 9.6, 9.41, 9.33, 9.4, 9.21, 9.21, 9.05, 9.22, 9.63, 9.41, 9.49, 9.91, 10.52, 10.12, 9.42, 9.37, 9.3, 9.6, 9.57, 9.4, 9.61, 9.73, 9.47, 10.09, 10.5, 10.83, 13.8, 17.78, 23.11, 30.04, 28.55, 28.85, 35.54, 45.17, 49.16, 63.21, 74.98, 97.47, 107], "increasing": {"fillcolor": "rgba(255,255,255,0.9)", "line": {"color": "#FF4136"}}, "line": {"width": 1}, "low": [11.26, 9.69, 9.42, 11.39, 10.97, 11.24, 11.9, 12.34, 12.85, 13.52, 13.27, 12.86, 12.43, 11.16, 11.21, 11.57, 10.53, 10.51, 10.59, 12.04, 12.15, 12.74, 12.32, 12.85, 13.2, 13.71, 13.16, 11.67, 11.69, 12.14, 12.19, 11.55, 10.87, 10.19, 10.67, 10.49, 11.24, 11.57, 11.85, 11.87, 11.9, 11.57, 11.79, 12.18, 12.33, 12.47, 11.91, 11.52, 11.57, 11.1, 11.03, 11.18, 11.24, 11.27, 10.84, 10.77, 11.26, 11.21, 11.21, 11.37, 10.96, 10.36, 9.82, 10, 9.97, 9.96, 10.06, 9.97, 9.6, 8.89, 8.9, 9.01, 9.21, 8.9, 8.6, 8.27, 8.39, 8.62, 8.71, 8.73, 8.64, 9.48, 9.21, 9.36, 10.32, 10.38, 10.3, 9.97, 9.91, 9.93, 9.73, 9.63, 9.74, 9.65, 9.63, 9.51, 9.52, 9.21, 9.11, 9.22, 9.38, 9.34, 9.41, 9.41, 9.09, 9.07, 9.06, 9.11, 9.19, 8.74, 8.79, 8.81, 9.11, 9.1, 9.21, 9.5, 9.58, 9.87, 9.4, 9.45, 9.56, 9.77, 9.83, 9.57, 9.37, 9.4, 9.55, 9.55, 9.25, 9, 8.93, 8.77, 8.97, 9.16, 9.13, 8.97, 9.13, 9.51, 9.72, 9.67, 10.06, 9.73, 9.21, 9.4, 9.41, 9.66, 9.68, 9.98, 10.33, 9.98, 9.74, 9.76, 9.75, 9.48, 9.48, 9.28, 9.32, 9.43, 9.36, 8.99, 9.01, 9.21, 9.15, 8.97, 8.96, 9.02, 8.83, 8.83, 8.88, 9.05, 9.13, 9.19, 9.44, 9.58, 9.42, 9.08, 8.96, 9.07, 9.16, 9.06, 9.09, 9.24, 9.46, 9.28, 9.05, 9.66, 9.73, 10.61, 16, 21.21, 21, 19.76, 20.11, 25.4, 33.5, 36.08, 50.08, 54, 79.2, 68.24], "name": "K 线", "open": [11.46, 11.63, 9.89, 12.24, 11.37, 11.27, 12.11, 12.65, 12.86, 13.79, 13.61, 13.53, 13.02, 12.53, 11.46, 11.6, 11.9, 10.62, 10.72, 12.24, 12.47, 12.83, 13.26, 13.07, 14.28, 14.57, 13.45, 12.84, 11.82, 12.27, 12.36, 12.66, 12.04, 10.53, 11.15, 11.09, 11.25, 11.82, 11.98, 11.9, 12.34, 11.81, 11.87, 12.73, 12.72, 12.47, 12.66, 12.04, 11.75, 11.75, 11.45, 11.19, 11.45, 11.31, 11.31, 11.09, 11.36, 11.39, 11.36, 11.43, 11.35, 10.9, 10.44, 10.02, 9.98, 10.09, 10.14, 10.18, 9.99, 9.55, 8.97, 9.11, 9.3, 9.21, 9.1, 8.93, 8.39, 8.67, 9.02, 8.95, 8.78, 9.49, 9.21, 9.6, 10.6, 10.43, 10.67, 10.19, 10.12, 10.06, 9.88, 9.91, 9.85, 10.22, 9.69, 9.65, 9.63, 9.62, 9.28, 9.4, 9.6, 9.5, 9.52, 9.43, 9.47, 9.07, 9.22, 9.18, 9.31, 9.22, 8.79, 8.87, 9.16, 9.2, 9.29, 9.5, 9.65, 9.98, 9.88, 9.53, 9.56, 9.86, 9.96, 10.08, 9.58, 9.44, 9.55, 9.73, 9.55, 9.28, 9.31, 8.95, 9.02, 9.3, 9.34, 9.15, 9.15, 9.63, 10.15, 9.73, 10.14, 10.01, 9.66, 9.45, 9.46, 9.86, 9.75, 10.05, 10.67, 10.35, 9.95, 9.9, 9.96, 9.73, 9.65, 9.44, 9.42, 9.45, 9.39, 9.43, 9.14, 9.45, 9.29, 9.23, 8.98, 9.05, 9.1, 8.84, 8.99, 9.12, 9.29, 9.19, 9.53, 10.15, 9.58, 9.25, 9.1, 9.23, 9.17, 9.24, 9.27, 9.35, 9.65, 9.47, 9.22, 9.8, 9.87, 11.2, 16.5, 23.11, 21.5, 26.1, 20.21, 29, 36, 36.4, 50.1, 60.4, 80, 105.3], "type": "candlestick", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199]}], "layout": {"template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["code = \"bj830799\"\n", "bars = ak.stock_zh_a_daily(symbol=code, adjust=\"qfq\", start_date=\"20231101\",end_date=\"20241022\")\n", "\n", "# 获取最近120个交易日的数据\n", "df = bars.iloc[-200:]\n", "df.index = np.arange(200)\n", "df.tail()\n", "\n", "candlestick(df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这是未上涨前的形态："]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"close": [11.62, 10.01, 11.02, 11.7, 11.56, 12.29, 12.84, 13.12, 14.05, 13.73, 13.81, 12.98, 12.44, 11.68, 11.6, 12.13, 10.56, 10.62, 12.1, 12.92, 13.21, 13.26, 12.71, 13.86, 15.26, 13.84, 13.22, 11.71, 12.39, 12.6, 12.66, 12.26, 10.87, 11.52, 10.77, 10.92, 11.81, 12.32, 12.19, 12.21, 12.26, 11.86, 12.42, 12.21, 12.76, 12.69, 12.19, 11.73, 11.82, 11.31, 11.3, 11.39, 11.28, 11.37, 11.02, 11.39, 11.44, 11.26, 11.44, 11.46, 11.02, 10.36, 10.03, 10.01, 10.15, 10.07, 10.14, 9.99, 9.6, 8.89, 9.04, 9.38, 9.25, 8.93, 8.81, 8.27, 8.76, 9.13, 8.95, 8.75, 9.42, 9.62, 9.26, 10.82, 10.62, 10.38, 10.38, 10.24, 9.97, 9.97, 10.09, 9.81, 10.04, 9.71, 9.71, 9.62, 9.59, 9.21, 9.37, 9.67, 9.39, 9.47, 9.47, 9.57, 9.11, 9.22, 9.15, 9.28, 9.2, 8.8, 8.94, 9.17, 9.16, 9.27, 9.76, 9.63, 9.92, 9.95, 9.55, 9.99, 9.96, 10.17, 10.2, 9.58, 9.45, 9.61, 9.9, 9.62, 9.25, 9.41, 8.95, 9.09, 9.1, 9.28, 9.21, 9.21, 9.56, 10.25, 9.75, 9.76, 10.16, 9.74, 9.24, 9.72, 9.73, 9.9, 10.13, 11.09, 10.46, 10.07, 9.79, 9.91, 9.77, 9.59, 9.52, 9.35, 9.55, 9.48, 9.63, 9.04, 9.84, 9.44, 9.15, 9.04, 9.32, 9.12, 8.85, 8.92, 9.11, 9.41, 9.13, 9.4, 9.44, 9.63, 9.5, 9.17, 9.35, 9.18, 9.4, 9.35, 9.35, 9.58, 9.47, 9.31, 9.7, 10.01, 10.7], "decreasing": {"fillcolor": "#3DAA70", "line": {"color": "#3DAA70"}}, "high": [11.75, 11.84, 11.42, 12.73, 11.92, 12.59, 13.48, 13.4, 14.26, 14.57, 14.16, 13.95, 13.59, 12.68, 11.9, 12.58, 12.13, 11.12, 12.17, 13.7, 13.69, 13.57, 13.5, 14, 16.1, 15.08, 14.52, 13.22, 12.61, 13.16, 13.14, 12.88, 12.23, 11.88, 11.92, 11.46, 11.89, 12.65, 13.07, 12.59, 12.65, 12.04, 12.42, 13.34, 13.28, 12.92, 12.74, 12.1, 12.04, 11.87, 11.47, 11.42, 11.53, 11.67, 11.52, 11.56, 11.61, 11.6, 11.47, 11.72, 11.41, 11.02, 10.63, 10.52, 10.45, 10.17, 10.21, 10.33, 10.02, 9.56, 9.15, 9.97, 9.58, 9.44, 9.28, 8.98, 8.88, 9.61, 9.18, 9.04, 9.46, 10.07, 9.48, 11.42, 11.54, 10.95, 10.74, 10.46, 10.35, 10.22, 10.28, 10.05, 10.15, 10.29, 9.93, 9.75, 9.74, 9.63, 9.4, 10.09, 9.73, 9.56, 9.71, 9.73, 9.55, 9.28, 9.25, 9.33, 9.45, 9.25, 9.02, 9.21, 9.31, 9.34, 9.9, 9.74, 9.92, 10.53, 9.91, 10.09, 10.53, 10.21, 10.32, 10.08, 9.72, 9.74, 10.21, 9.79, 9.79, 9.55, 9.35, 9.21, 9.34, 9.39, 9.34, 9.5, 9.61, 10.41, 10.18, 10.07, 10.83, 10.11, 9.8, 10.25, 9.88, 10.15, 10.2, 11.74, 10.88, 10.55, 10.3, 10.11, 10.07, 9.89, 9.76, 9.56, 9.59, 9.6, 9.85, 9.54, 9.91, 9.6, 9.41, 9.33, 9.4, 9.21, 9.21, 9.05, 9.22, 9.63, 9.41, 9.49, 9.91, 10.52, 10.12, 9.42, 9.37, 9.3, 9.6, 9.57, 9.4, 9.61, 9.73, 9.47, 10.09, 10.5, 10.83], "increasing": {"fillcolor": "rgba(255,255,255,0.9)", "line": {"color": "#FF4136"}}, "line": {"width": 1}, "low": [11.26, 9.69, 9.42, 11.39, 10.97, 11.24, 11.9, 12.34, 12.85, 13.52, 13.27, 12.86, 12.43, 11.16, 11.21, 11.57, 10.53, 10.51, 10.59, 12.04, 12.15, 12.74, 12.32, 12.85, 13.2, 13.71, 13.16, 11.67, 11.69, 12.14, 12.19, 11.55, 10.87, 10.19, 10.67, 10.49, 11.24, 11.57, 11.85, 11.87, 11.9, 11.57, 11.79, 12.18, 12.33, 12.47, 11.91, 11.52, 11.57, 11.1, 11.03, 11.18, 11.24, 11.27, 10.84, 10.77, 11.26, 11.21, 11.21, 11.37, 10.96, 10.36, 9.82, 10, 9.97, 9.96, 10.06, 9.97, 9.6, 8.89, 8.9, 9.01, 9.21, 8.9, 8.6, 8.27, 8.39, 8.62, 8.71, 8.73, 8.64, 9.48, 9.21, 9.36, 10.32, 10.38, 10.3, 9.97, 9.91, 9.93, 9.73, 9.63, 9.74, 9.65, 9.63, 9.51, 9.52, 9.21, 9.11, 9.22, 9.38, 9.34, 9.41, 9.41, 9.09, 9.07, 9.06, 9.11, 9.19, 8.74, 8.79, 8.81, 9.11, 9.1, 9.21, 9.5, 9.58, 9.87, 9.4, 9.45, 9.56, 9.77, 9.83, 9.57, 9.37, 9.4, 9.55, 9.55, 9.25, 9, 8.93, 8.77, 8.97, 9.16, 9.13, 8.97, 9.13, 9.51, 9.72, 9.67, 10.06, 9.73, 9.21, 9.4, 9.41, 9.66, 9.68, 9.98, 10.33, 9.98, 9.74, 9.76, 9.75, 9.48, 9.48, 9.28, 9.32, 9.43, 9.36, 8.99, 9.01, 9.21, 9.15, 8.97, 8.96, 9.02, 8.83, 8.83, 8.88, 9.05, 9.13, 9.19, 9.44, 9.58, 9.42, 9.08, 8.96, 9.07, 9.16, 9.06, 9.09, 9.24, 9.46, 9.28, 9.05, 9.66, 9.73], "name": "K 线", "open": [11.46, 11.63, 9.89, 12.24, 11.37, 11.27, 12.11, 12.65, 12.86, 13.79, 13.61, 13.53, 13.02, 12.53, 11.46, 11.6, 11.9, 10.62, 10.72, 12.24, 12.47, 12.83, 13.26, 13.07, 14.28, 14.57, 13.45, 12.84, 11.82, 12.27, 12.36, 12.66, 12.04, 10.53, 11.15, 11.09, 11.25, 11.82, 11.98, 11.9, 12.34, 11.81, 11.87, 12.73, 12.72, 12.47, 12.66, 12.04, 11.75, 11.75, 11.45, 11.19, 11.45, 11.31, 11.31, 11.09, 11.36, 11.39, 11.36, 11.43, 11.35, 10.9, 10.44, 10.02, 9.98, 10.09, 10.14, 10.18, 9.99, 9.55, 8.97, 9.11, 9.3, 9.21, 9.1, 8.93, 8.39, 8.67, 9.02, 8.95, 8.78, 9.49, 9.21, 9.6, 10.6, 10.43, 10.67, 10.19, 10.12, 10.06, 9.88, 9.91, 9.85, 10.22, 9.69, 9.65, 9.63, 9.62, 9.28, 9.4, 9.6, 9.5, 9.52, 9.43, 9.47, 9.07, 9.22, 9.18, 9.31, 9.22, 8.79, 8.87, 9.16, 9.2, 9.29, 9.5, 9.65, 9.98, 9.88, 9.53, 9.56, 9.86, 9.96, 10.08, 9.58, 9.44, 9.55, 9.73, 9.55, 9.28, 9.31, 8.95, 9.02, 9.3, 9.34, 9.15, 9.15, 9.63, 10.15, 9.73, 10.14, 10.01, 9.66, 9.45, 9.46, 9.86, 9.75, 10.05, 10.67, 10.35, 9.95, 9.9, 9.96, 9.73, 9.65, 9.44, 9.42, 9.45, 9.39, 9.43, 9.14, 9.45, 9.29, 9.23, 8.98, 9.05, 9.1, 8.84, 8.99, 9.12, 9.29, 9.19, 9.53, 10.15, 9.58, 9.25, 9.1, 9.23, 9.17, 9.24, 9.27, 9.35, 9.65, 9.47, 9.22, 9.8, 9.87], "type": "candlestick", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186]}], "layout": {"template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["candlestick(df.iloc[:187])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["这是检测出来的旗形整理："]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"close": [11.62, 10.01, 11.02, 11.7, 11.56, 12.29, 12.84, 13.12, 14.05, 13.73, 13.81, 12.98, 12.44, 11.68, 11.6, 12.13, 10.56, 10.62, 12.1, 12.92, 13.21, 13.26, 12.71, 13.86, 15.26, 13.84, 13.22, 11.71, 12.39, 12.6, 12.66, 12.26, 10.87, 11.52, 10.77, 10.92, 11.81, 12.32, 12.19, 12.21, 12.26, 11.86, 12.42, 12.21, 12.76, 12.69, 12.19, 11.73, 11.82, 11.31, 11.3, 11.39, 11.28, 11.37, 11.02, 11.39, 11.44, 11.26, 11.44, 11.46, 11.02, 10.36, 10.03, 10.01, 10.15, 10.07, 10.14, 9.99, 9.6, 8.89, 9.04, 9.38, 9.25, 8.93, 8.81, 8.27, 8.76, 9.13, 8.95, 8.75, 9.42, 9.62, 9.26, 10.82, 10.62, 10.38, 10.38, 10.24, 9.97, 9.97, 10.09, 9.81, 10.04, 9.71, 9.71, 9.62, 9.59, 9.21, 9.37, 9.67, 9.39, 9.47, 9.47, 9.57, 9.11, 9.22, 9.15, 9.28, 9.2, 8.8, 8.94, 9.17, 9.16, 9.27, 9.76, 9.63, 9.92, 9.95, 9.55, 9.99, 9.96, 10.17, 10.2, 9.58, 9.45, 9.61, 9.9, 9.62, 9.25, 9.41, 8.95, 9.09, 9.1, 9.28, 9.21, 9.21, 9.56, 10.25, 9.75, 9.76, 10.16, 9.74, 9.24, 9.72, 9.73, 9.9, 10.13, 11.09, 10.46, 10.07, 9.79, 9.91, 9.77, 9.59, 9.52, 9.35, 9.55, 9.48, 9.63, 9.04, 9.84, 9.44, 9.15, 9.04, 9.32, 9.12, 8.85, 8.92, 9.11, 9.41, 9.13, 9.4, 9.44, 9.63, 9.5, 9.17, 9.35, 9.18, 9.4, 9.35, 9.35, 9.58, 9.47, 9.31, 9.7, 10.01, 10.7], "decreasing": {"fillcolor": "#3DAA70", "line": {"color": "#3DAA70"}}, "high": [11.75, 11.84, 11.42, 12.73, 11.92, 12.59, 13.48, 13.4, 14.26, 14.57, 14.16, 13.95, 13.59, 12.68, 11.9, 12.58, 12.13, 11.12, 12.17, 13.7, 13.69, 13.57, 13.5, 14, 16.1, 15.08, 14.52, 13.22, 12.61, 13.16, 13.14, 12.88, 12.23, 11.88, 11.92, 11.46, 11.89, 12.65, 13.07, 12.59, 12.65, 12.04, 12.42, 13.34, 13.28, 12.92, 12.74, 12.1, 12.04, 11.87, 11.47, 11.42, 11.53, 11.67, 11.52, 11.56, 11.61, 11.6, 11.47, 11.72, 11.41, 11.02, 10.63, 10.52, 10.45, 10.17, 10.21, 10.33, 10.02, 9.56, 9.15, 9.97, 9.58, 9.44, 9.28, 8.98, 8.88, 9.61, 9.18, 9.04, 9.46, 10.07, 9.48, 11.42, 11.54, 10.95, 10.74, 10.46, 10.35, 10.22, 10.28, 10.05, 10.15, 10.29, 9.93, 9.75, 9.74, 9.63, 9.4, 10.09, 9.73, 9.56, 9.71, 9.73, 9.55, 9.28, 9.25, 9.33, 9.45, 9.25, 9.02, 9.21, 9.31, 9.34, 9.9, 9.74, 9.92, 10.53, 9.91, 10.09, 10.53, 10.21, 10.32, 10.08, 9.72, 9.74, 10.21, 9.79, 9.79, 9.55, 9.35, 9.21, 9.34, 9.39, 9.34, 9.5, 9.61, 10.41, 10.18, 10.07, 10.83, 10.11, 9.8, 10.25, 9.88, 10.15, 10.2, 11.74, 10.88, 10.55, 10.3, 10.11, 10.07, 9.89, 9.76, 9.56, 9.59, 9.6, 9.85, 9.54, 9.91, 9.6, 9.41, 9.33, 9.4, 9.21, 9.21, 9.05, 9.22, 9.63, 9.41, 9.49, 9.91, 10.52, 10.12, 9.42, 9.37, 9.3, 9.6, 9.57, 9.4, 9.61, 9.73, 9.47, 10.09, 10.5, 10.83], "increasing": {"fillcolor": "rgba(255,255,255,0.9)", "line": {"color": "#FF4136"}}, "line": {"width": 1}, "low": [11.26, 9.69, 9.42, 11.39, 10.97, 11.24, 11.9, 12.34, 12.85, 13.52, 13.27, 12.86, 12.43, 11.16, 11.21, 11.57, 10.53, 10.51, 10.59, 12.04, 12.15, 12.74, 12.32, 12.85, 13.2, 13.71, 13.16, 11.67, 11.69, 12.14, 12.19, 11.55, 10.87, 10.19, 10.67, 10.49, 11.24, 11.57, 11.85, 11.87, 11.9, 11.57, 11.79, 12.18, 12.33, 12.47, 11.91, 11.52, 11.57, 11.1, 11.03, 11.18, 11.24, 11.27, 10.84, 10.77, 11.26, 11.21, 11.21, 11.37, 10.96, 10.36, 9.82, 10, 9.97, 9.96, 10.06, 9.97, 9.6, 8.89, 8.9, 9.01, 9.21, 8.9, 8.6, 8.27, 8.39, 8.62, 8.71, 8.73, 8.64, 9.48, 9.21, 9.36, 10.32, 10.38, 10.3, 9.97, 9.91, 9.93, 9.73, 9.63, 9.74, 9.65, 9.63, 9.51, 9.52, 9.21, 9.11, 9.22, 9.38, 9.34, 9.41, 9.41, 9.09, 9.07, 9.06, 9.11, 9.19, 8.74, 8.79, 8.81, 9.11, 9.1, 9.21, 9.5, 9.58, 9.87, 9.4, 9.45, 9.56, 9.77, 9.83, 9.57, 9.37, 9.4, 9.55, 9.55, 9.25, 9, 8.93, 8.77, 8.97, 9.16, 9.13, 8.97, 9.13, 9.51, 9.72, 9.67, 10.06, 9.73, 9.21, 9.4, 9.41, 9.66, 9.68, 9.98, 10.33, 9.98, 9.74, 9.76, 9.75, 9.48, 9.48, 9.28, 9.32, 9.43, 9.36, 8.99, 9.01, 9.21, 9.15, 8.97, 8.96, 9.02, 8.83, 8.83, 8.88, 9.05, 9.13, 9.19, 9.44, 9.58, 9.42, 9.08, 8.96, 9.07, 9.16, 9.06, 9.09, 9.24, 9.46, 9.28, 9.05, 9.66, 9.73], "name": "K 线", "open": [11.46, 11.63, 9.89, 12.24, 11.37, 11.27, 12.11, 12.65, 12.86, 13.79, 13.61, 13.53, 13.02, 12.53, 11.46, 11.6, 11.9, 10.62, 10.72, 12.24, 12.47, 12.83, 13.26, 13.07, 14.28, 14.57, 13.45, 12.84, 11.82, 12.27, 12.36, 12.66, 12.04, 10.53, 11.15, 11.09, 11.25, 11.82, 11.98, 11.9, 12.34, 11.81, 11.87, 12.73, 12.72, 12.47, 12.66, 12.04, 11.75, 11.75, 11.45, 11.19, 11.45, 11.31, 11.31, 11.09, 11.36, 11.39, 11.36, 11.43, 11.35, 10.9, 10.44, 10.02, 9.98, 10.09, 10.14, 10.18, 9.99, 9.55, 8.97, 9.11, 9.3, 9.21, 9.1, 8.93, 8.39, 8.67, 9.02, 8.95, 8.78, 9.49, 9.21, 9.6, 10.6, 10.43, 10.67, 10.19, 10.12, 10.06, 9.88, 9.91, 9.85, 10.22, 9.69, 9.65, 9.63, 9.62, 9.28, 9.4, 9.6, 9.5, 9.52, 9.43, 9.47, 9.07, 9.22, 9.18, 9.31, 9.22, 8.79, 8.87, 9.16, 9.2, 9.29, 9.5, 9.65, 9.98, 9.88, 9.53, 9.56, 9.86, 9.96, 10.08, 9.58, 9.44, 9.55, 9.73, 9.55, 9.28, 9.31, 8.95, 9.02, 9.3, 9.34, 9.15, 9.15, 9.63, 10.15, 9.73, 10.14, 10.01, 9.66, 9.45, 9.46, 9.86, 9.75, 10.05, 10.67, 10.35, 9.95, 9.9, 9.96, 9.73, 9.65, 9.44, 9.42, 9.45, 9.39, 9.43, 9.14, 9.45, 9.29, 9.23, 8.98, 9.05, 9.1, 8.84, 8.99, 9.12, 9.29, 9.19, 9.53, 10.15, 9.58, 9.25, 9.1, 9.23, 9.17, 9.24, 9.27, 9.35, 9.65, 9.47, 9.22, 9.8, 9.87], "type": "candlestick", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186]}, {"mode": "lines", "name": "支撑线", "type": "scatter", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186], "y": [7.95683975240715, 7.963178587803758, 7.969517423200364, 7.975856258596972, 7.982195093993579, 7.988533929390186, 7.994872764786793, 8.0012116001834, 8.007550435580008, 8.013889270976614, 8.02022810637322, 8.026566941769827, 8.032905777166436, 8.039244612563042, 8.045583447959649, 8.051922283356257, 8.058261118752863, 8.06459995414947, 8.070938789546078, 8.077277624942685, 8.083616460339291, 8.0899552957359, 8.096294131132506, 8.102632966529113, 8.10897180192572, 8.115310637322327, 8.121649472718934, 8.127988308115542, 8.134327143512149, 8.140665978908755, 8.147004814305362, 8.15334364970197, 8.159682485098577, 8.166021320495183, 8.172360155891791, 8.178698991288398, 8.185037826685004, 8.191376662081613, 8.19771549747822, 8.204054332874826, 8.210393168271434, 8.21673200366804, 8.223070839064647, 8.229409674461255, 8.235748509857862, 8.242087345254468, 8.248426180651077, 8.254765016047683, 8.26110385144429, 8.267442686840896, 8.273781522237504, 8.280120357634111, 8.286459193030717, 8.292798028427326, 8.299136863823932, 8.305475699220539, 8.311814534617147, 8.318153370013754, 8.32449220541036, 8.330831040806968, 8.337169876203575, 8.343508711600181, 8.34984754699679, 8.356186382393396, 8.362525217790003, 8.36886405318661, 8.375202888583217, 8.381541723979824, 8.38788055937643, 8.394219394773039, 8.400558230169645, 8.406897065566252, 8.41323590096286, 8.419574736359467, 8.425913571756073, 8.432252407152681, 8.438591242549288, 8.444930077945894, 8.451268913342503, 8.45760774873911, 8.463946584135716, 8.470285419532324, 8.47662425492893, 8.482963090325537, 8.489301925722145, 8.495640761118752, 8.501979596515358, 8.508318431911965, 8.514657267308573, 8.52099610270518, 8.527334938101786, 8.533673773498395, 8.540012608895001, 8.546351444291608, 8.552690279688216, 8.559029115084822, 8.565367950481429, 8.571706785878037, 8.578045621274644, 8.58438445667125, 8.590723292067858, 8.597062127464465, 8.603400962861071, 8.609739798257678, 8.616078633654286, 8.622417469050893, 8.6287563044475, 8.635095139844108, 8.641433975240714, 8.64777281063732, 8.654111646033929, 8.660450481430535, 8.666789316827142, 8.67312815222375, 8.679466987620357, 8.685805823016963, 8.692144658413572, 8.698483493810178, 8.704822329206785, 8.711161164603393, 8.7175, 8.723838835396606, 8.730177670793214, 8.73651650618982, 8.742855341586427, 8.749194176983034, 8.755533012379642, 8.761871847776249, 8.768210683172855, 8.774549518569463, 8.78088835396607, 8.787227189362676, 8.793566024759285, 8.799904860155891, 8.806243695552498, 8.812582530949106, 8.818921366345712, 8.825260201742319, 8.831599037138925, 8.837937872535534, 8.84427670793214, 8.850615543328747, 8.856954378725355, 8.863293214121962, 8.869632049518568, 8.875970884915176, 8.882309720311783, 8.88864855570839, 8.894987391104998, 8.901326226501604, 8.90766506189821, 8.914003897294819, 8.920342732691426, 8.926681568088032, 8.93302040348464, 8.939359238881247, 8.945698074277853, 8.952036909674462, 8.958375745071068, 8.964714580467675, 8.971053415864283, 8.97739225126089, 8.983731086657496, 8.990069922054102, 8.99640875745071, 9.002747592847317, 9.009086428243924, 9.015425263640532, 9.021764099037139, 9.028102934433745, 9.034441769830353, 9.04078060522696, 9.047119440623566, 9.053458276020175, 9.059797111416781, 9.066135946813388, 9.072474782209994, 9.078813617606603, 9.085152453003209, 9.091491288399816, 9.097830123796424, 9.10416895919303, 9.110507794589637, 9.116846629986245, 9.123185465382852, 9.129524300779458, 9.135863136176066]}, {"mode": "lines", "name": "压力线", "type": "scatter", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186], "y": [15.267378105409792, 15.230943231947986, 15.194508358486178, 15.158073485024373, 15.121638611562567, 15.08520373810076, 15.048768864638953, 15.012333991177147, 14.975899117715342, 14.939464244253536, 14.903029370791728, 14.866594497329922, 14.830159623868116, 14.79372475040631, 14.757289876944503, 14.720855003482697, 14.684420130020891, 14.647985256559085, 14.611550383097278, 14.575115509635472, 14.538680636173666, 14.50224576271186, 14.465810889250053, 14.429376015788247, 14.39294114232644, 14.356506268864635, 14.320071395402827, 14.283636521941022, 14.247201648479216, 14.210766775017408, 14.174331901555602, 14.137897028093796, 14.10146215463199, 14.065027281170185, 14.028592407708377, 13.992157534246571, 13.955722660784765, 13.919287787322958, 13.882852913861152, 13.846418040399346, 13.80998316693754, 13.773548293475733, 13.737113420013927, 13.70067854655212, 13.664243673090315, 13.627808799628507, 13.591373926166701, 13.554939052704896, 13.51850417924309, 13.482069305781282, 13.445634432319476, 13.40919955885767, 13.372764685395865, 13.336329811934057, 13.299894938472251, 13.263460065010445, 13.227025191548638, 13.190590318086832, 13.154155444625026, 13.11772057116322, 13.081285697701414, 13.044850824239607, 13.0084159507778, 12.971981077315995, 12.935546203854187, 12.899111330392381, 12.862676456930576, 12.82624158346877, 12.789806710006964, 12.753371836545156, 12.71693696308335, 12.680502089621545, 12.644067216159737, 12.607632342697931, 12.571197469236125, 12.53476259577432, 12.498327722312514, 12.461892848850706, 12.4254579753889, 12.389023101927094, 12.352588228465287, 12.31615335500348, 12.279718481541675, 12.243283608079867, 12.206848734618061, 12.170413861156256, 12.13397898769445, 12.097544114232644, 12.061109240770836, 12.02467436730903, 11.988239493847225, 11.951804620385417, 11.915369746923611, 11.878934873461805, 11.8425, 11.806065126538194, 11.769630253076386, 11.73319537961458, 11.696760506152774, 11.660325632690967, 11.62389075922916, 11.587455885767355, 11.551021012305549, 11.514586138843743, 11.478151265381936, 11.44171639192013, 11.405281518458324, 11.368846644996516, 11.33241177153471, 11.295976898072905, 11.259542024611097, 11.223107151149293, 11.186672277687485, 11.15023740422568, 11.113802530763873, 11.077367657302066, 11.04093278384026, 11.004497910378454, 10.968063036916647, 10.931628163454842, 10.895193289993035, 10.858758416531229, 10.822323543069423, 10.785888669607615, 10.74945379614581, 10.713018922684004, 10.676584049222196, 10.640149175760392, 10.603714302298584, 10.567279428836777, 10.530844555374973, 10.494409681913165, 10.45797480845136, 10.421539934989553, 10.385105061527746, 10.34867018806594, 10.312235314604134, 10.275800441142326, 10.239365567680522, 10.202930694218715, 10.166495820756909, 10.130060947295103, 10.093626073833295, 10.05719120037149, 10.020756326909684, 9.984321453447876, 9.947886579986072, 9.911451706524264, 9.875016833062459, 9.838581959600653, 9.802147086138845, 9.76571221267704, 9.729277339215233, 9.692842465753426, 9.656407592291622, 9.619972718829814, 9.583537845368008, 9.547102971906202, 9.510668098444395, 9.474233224982589, 9.437798351520783, 9.401363478058975, 9.36492860459717, 9.328493731135364, 9.292058857673556, 9.255623984211752, 9.219189110749944, 9.182754237288139, 9.146319363826333, 9.109884490364525, 9.07344961690272, 9.037014743440913, 9.000579869979106, 8.964144996517302, 8.927710123055494, 8.891275249593688, 8.854840376131882, 8.818405502670075, 8.781970629208269, 8.745535755746463, 8.709100882284655, 8.672666008822851, 8.636231135361044, 8.599796261899238, 8.563361388437432, 8.526926514975624, 8.490491641513819]}], "layout": {"template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["trendline(df.iloc[:187])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "coursea", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}