{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import akshare as ak"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20201120\n", "20241119\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>close</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>volume</th>\n", "      <th>成交额</th>\n", "      <th>振幅</th>\n", "      <th>涨跌幅</th>\n", "      <th>涨跌额</th>\n", "      <th>换手率</th>\n", "      <th>flag</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-11-13</th>\n", "      <td>6453.86</td>\n", "      <td>6474.39</td>\n", "      <td>6512.60</td>\n", "      <td>6357.56</td>\n", "      <td>317485928</td>\n", "      <td>4.203869e+11</td>\n", "      <td>2.39</td>\n", "      <td>-0.27</td>\n", "      <td>-17.58</td>\n", "      <td>3.33</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-14</th>\n", "      <td>6462.01</td>\n", "      <td>6272.19</td>\n", "      <td>6472.95</td>\n", "      <td>6263.31</td>\n", "      <td>294794718</td>\n", "      <td>3.878677e+11</td>\n", "      <td>3.24</td>\n", "      <td>-3.12</td>\n", "      <td>-202.20</td>\n", "      <td>3.09</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-15</th>\n", "      <td>6246.34</td>\n", "      <td>6125.51</td>\n", "      <td>6331.87</td>\n", "      <td>6124.66</td>\n", "      <td>295191634</td>\n", "      <td>3.834241e+11</td>\n", "      <td>3.30</td>\n", "      <td>-2.34</td>\n", "      <td>-146.68</td>\n", "      <td>3.09</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-18</th>\n", "      <td>6154.51</td>\n", "      <td>5974.56</td>\n", "      <td>6180.88</td>\n", "      <td>5937.09</td>\n", "      <td>322672608</td>\n", "      <td>3.740080e+11</td>\n", "      <td>3.98</td>\n", "      <td>-2.46</td>\n", "      <td>-150.95</td>\n", "      <td>3.38</td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-19</th>\n", "      <td>5977.84</td>\n", "      <td>6130.28</td>\n", "      <td>6130.50</td>\n", "      <td>5945.46</td>\n", "      <td>273167526</td>\n", "      <td>3.279243e+11</td>\n", "      <td>3.10</td>\n", "      <td>2.61</td>\n", "      <td>155.72</td>\n", "      <td>2.86</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               open    close     high      low     volume           成交额    振幅  \\\n", "date                                                                            \n", "2024-11-13  6453.86  6474.39  6512.60  6357.56  317485928  4.203869e+11  2.39   \n", "2024-11-14  6462.01  6272.19  6472.95  6263.31  294794718  3.878677e+11  3.24   \n", "2024-11-15  6246.34  6125.51  6331.87  6124.66  295191634  3.834241e+11  3.30   \n", "2024-11-18  6154.51  5974.56  6180.88  5937.09  322672608  3.740080e+11  3.98   \n", "2024-11-19  5977.84  6130.28  6130.50  5945.46  273167526  3.279243e+11  3.10   \n", "\n", "             涨跌幅     涨跌额   换手率  flag  \n", "date                                  \n", "2024-11-13 -0.27  -17.58  3.33     1  \n", "2024-11-14 -3.12 -202.20  3.09    -1  \n", "2024-11-15 -2.34 -146.68  3.09    -1  \n", "2024-11-18 -2.46 -150.95  3.38    -1  \n", "2024-11-19  2.61  155.72  2.86     1  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["now = datetime.datetime.now().date()\n", "start = now - datetime.<PERSON><PERSON><PERSON>(days=365*4)\n", "\n", "start_date = start.strftime(\"%Y%m%d\")\n", "end_date = now.strftime(\"%Y%m%d\")\n", "\n", "print(start_date)\n", "print(end_date)\n", "# 通过akshare获取中证1000日线数据（近1000天）\n", "bars = ak.index_zh_a_hist(symbol=\"000852\", start_date=start_date, end_date=end_date)\n", "\n", "bars.rename(columns = {\n", "    \"日期\": \"date\",\n", "    \"开盘\": \"open\",\n", "    \"最高\": \"high\",\n", "    \"最低\": \"low\",\n", "    \"收盘\": \"close\",\n", "    \"成交量\":\"volume\"\n", "}, inplace=True)\n", "\n", "bars[\"date\"] = pd.to_datetime(bars[\"date\"])\n", "bars.set_index(\"date\", inplace=True)\n", "\n", "bars[\"flag\"] = np.select([bars[\"close\"] > bars[\"open\"], \n", "                          bars[\"close\"] < bars[\"open\"]], \n", "                          [1, -1], \n", "                          0)\n", "bars.tail()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def find_runs(x):\n", "    \"\"\"Find runs of consecutive items in an array.\n", "    \"\"\"\n", "\n", "    # ensure array\n", "    x = np.asanyarray(x)\n", "    if x.ndim != 1:\n", "        raise ValueError(\"only 1D array supported\")\n", "    n = x.shape[0]\n", "\n", "    # handle empty array\n", "    if n == 0:\n", "        return np.array([]), np.array([]), np.array([])\n", "\n", "    else:\n", "        # find run starts\n", "        loc_run_start = np.empty(n, dtype=bool)\n", "        loc_run_start[0] = True\n", "        np.not_equal(x[:-1], x[1:], out=loc_run_start[1:])\n", "        run_starts = np.nonzero(loc_run_start)[0]\n", "\n", "        # find run values\n", "        run_values = x[loc_run_start]\n", "\n", "        # find run lengths\n", "        run_lengths = np.diff(np.append(run_starts, n))\n", "\n", "        return run_values, run_starts, run_lengths"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>cnr</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-11-25</td>\n", "      <td>2020-11-26</td>\n", "      <td>-0.025388</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-12-07</td>\n", "      <td>2020-12-09</td>\n", "      <td>-0.023310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-12-28</td>\n", "      <td>2020-12-29</td>\n", "      <td>-0.012879</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2021-01-06</td>\n", "      <td>2021-01-11</td>\n", "      <td>-0.048171</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2021-01-22</td>\n", "      <td>2021-01-26</td>\n", "      <td>-0.029975</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>103</th>\n", "      <td>2024-08-20</td>\n", "      <td>2024-08-22</td>\n", "      <td>-0.036867</td>\n", "    </tr>\n", "    <tr>\n", "      <th>104</th>\n", "      <td>2024-09-06</td>\n", "      <td>2024-09-09</td>\n", "      <td>-0.024712</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>2024-09-12</td>\n", "      <td>2024-09-18</td>\n", "      <td>-0.020352</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106</th>\n", "      <td>2024-10-08</td>\n", "      <td>2024-10-11</td>\n", "      <td>-0.158511</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>2024-11-14</td>\n", "      <td>2024-11-18</td>\n", "      <td>-0.075433</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>108 rows × 3 columns</p>\n", "</div>"], "text/plain": ["         start        end       cnr\n", "0   2020-11-25 2020-11-26 -0.025388\n", "1   2020-12-07 2020-12-09 -0.023310\n", "2   2020-12-28 2020-12-29 -0.012879\n", "3   2021-01-06 2021-01-11 -0.048171\n", "4   2021-01-22 2021-01-26 -0.029975\n", "..         ...        ...       ...\n", "103 2024-08-20 2024-08-22 -0.036867\n", "104 2024-09-06 2024-09-09 -0.024712\n", "105 2024-09-12 2024-09-18 -0.020352\n", "106 2024-10-08 2024-10-11 -0.158511\n", "107 2024-11-14 2024-11-18 -0.075433\n", "\n", "[108 rows x 3 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["v, s, l = find_runs(bars[\"flag\"] == -1)\n", "\n", "cum_neg_returns = []\n", "for vi, si, li in zip(v, s, l):\n", "    if vi and li > 1:\n", "        cum_neg_returns.append((bars.index[si], \n", "                                bars.index[si + li-1], \n", "                                bars.close[si + li -1 ]/bars.open[si] - 1))\n", "        \n", "r = pd.DataFrame(cum_neg_returns, columns=[\"start\", \"end\", \"cnr\"])\n", "r"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["r.cnr.hist()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>cnr</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>2024-11-14</td>\n", "      <td>2024-11-18</td>\n", "      <td>-0.075433</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106</th>\n", "      <td>2024-10-08</td>\n", "      <td>2024-10-11</td>\n", "      <td>-0.158511</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89</th>\n", "      <td>2024-03-21</td>\n", "      <td>2024-03-27</td>\n", "      <td>-0.071948</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>2024-01-26</td>\n", "      <td>2024-02-05</td>\n", "      <td>-0.190592</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>2024-01-19</td>\n", "      <td>2024-01-22</td>\n", "      <td>-0.067846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>2023-10-16</td>\n", "      <td>2023-10-23</td>\n", "      <td>-0.074109</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>2022-09-15</td>\n", "      <td>2022-09-19</td>\n", "      <td>-0.066983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>2022-04-20</td>\n", "      <td>2022-04-26</td>\n", "      <td>-0.170335</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>2022-03-14</td>\n", "      <td>2022-03-15</td>\n", "      <td>-0.066983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>2022-03-03</td>\n", "      <td>2022-03-09</td>\n", "      <td>-0.086669</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         start        end       cnr\n", "107 2024-11-14 2024-11-18 -0.075433\n", "106 2024-10-08 2024-10-11 -0.158511\n", "89  2024-03-21 2024-03-27 -0.071948\n", "88  2024-01-26 2024-02-05 -0.190592\n", "87  2024-01-19 2024-01-22 -0.067846\n", "78  2023-10-16 2023-10-23 -0.074109\n", "45  2022-09-15 2022-09-19 -0.066983\n", "37  2022-04-20 2022-04-26 -0.170335\n", "34  2022-03-14 2022-03-15 -0.066983\n", "33  2022-03-03 2022-03-09 -0.086669"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["r.nsma<PERSON>t(10, \"cnr\").sort_values(\"end\", ascending=False)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.046296296296296294"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# 3/27这一次下跌0.0694，此后继续下跌的概率\n", "\n", "p_decline = r.cnr.le(-0.075433).mean()\n", "p_decline"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.1452991452991453"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# 4/16这一次下跌0.058，此后继续下跌的概率\n", "\n", "p_decline = r.cnr.le(-0.058228).mean()\n", "p_decline"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["下跌幅度-5.0%, 反弹概率: 81.5%\n", "下跌幅度-5.6%, 反弹概率: 83.3%\n", "下跌幅度-6.1%, 反弹概率: 87.0%\n", "下跌幅度-6.7%, 反弹概率: 89.8%\n", "下跌幅度-7.2%, 反弹概率: 94.4%\n", "下跌幅度-7.8%, 反弹概率: 96.3%\n", "下跌幅度-8.3%, 反弹概率: 96.3%\n", "下跌幅度-8.9%, 反弹概率: 97.2%\n", "下跌幅度-9.4%, 反弹概率: 97.2%\n", "下跌幅度-10.0%, 反弹概率: 97.2%\n"]}], "source": ["import numpy as np\n", "for loss in np.linspace(-0.05, -0.1, 10):\n", "    print(f\"下跌幅度{loss:.1%}, 反弹概率: {1- r.cnr.le(loss).mean():.1%}\")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "</style>\n", "<table id=\"T_5e9d0\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_5e9d0_level0_col0\" class=\"col_heading level0 col0\" >最大亏损</th>\n", "      <th id=\"T_5e9d0_level0_col1\" class=\"col_heading level0 col1\" >抄底胜率</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_5e9d0_row0_col0\" class=\"data row0 col0\" >-6.0%</td>\n", "      <td id=\"T_5e9d0_row0_col1\" class=\"data row0 col1\" >87.0%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_5e9d0_row1_col0\" class=\"data row1 col0\" >-6.1%</td>\n", "      <td id=\"T_5e9d0_row1_col1\" class=\"data row1 col1\" >87.0%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_5e9d0_row2_col0\" class=\"data row2 col0\" >-6.2%</td>\n", "      <td id=\"T_5e9d0_row2_col1\" class=\"data row2 col1\" >87.0%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_5e9d0_row3_col0\" class=\"data row3 col0\" >-6.3%</td>\n", "      <td id=\"T_5e9d0_row3_col1\" class=\"data row3 col1\" >87.0%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_5e9d0_row4_col0\" class=\"data row4 col0\" >-6.5%</td>\n", "      <td id=\"T_5e9d0_row4_col1\" class=\"data row4 col1\" >88.0%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_5e9d0_row5_col0\" class=\"data row5 col0\" >-6.6%</td>\n", "      <td id=\"T_5e9d0_row5_col1\" class=\"data row5 col1\" >89.8%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_5e9d0_row6_col0\" class=\"data row6 col0\" >-6.7%</td>\n", "      <td id=\"T_5e9d0_row6_col1\" class=\"data row6 col1\" >90.7%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_5e9d0_row7_col0\" class=\"data row7 col0\" >-6.8%</td>\n", "      <td id=\"T_5e9d0_row7_col1\" class=\"data row7 col1\" >93.5%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_5e9d0_row8_col0\" class=\"data row8 col0\" >-6.9%</td>\n", "      <td id=\"T_5e9d0_row8_col1\" class=\"data row8 col1\" >93.5%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_5e9d0_row9_col0\" class=\"data row9 col0\" >-7.0%</td>\n", "      <td id=\"T_5e9d0_row9_col1\" class=\"data row9 col1\" >93.5%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row10\" class=\"row_heading level0 row10\" >10</th>\n", "      <td id=\"T_5e9d0_row10_col0\" class=\"data row10 col0\" >-7.1%</td>\n", "      <td id=\"T_5e9d0_row10_col1\" class=\"data row10 col1\" >93.5%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row11\" class=\"row_heading level0 row11\" >11</th>\n", "      <td id=\"T_5e9d0_row11_col0\" class=\"data row11 col0\" >-7.3%</td>\n", "      <td id=\"T_5e9d0_row11_col1\" class=\"data row11 col1\" >94.4%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row12\" class=\"row_heading level0 row12\" >12</th>\n", "      <td id=\"T_5e9d0_row12_col0\" class=\"data row12 col0\" >-7.4%</td>\n", "      <td id=\"T_5e9d0_row12_col1\" class=\"data row12 col1\" >94.4%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row13\" class=\"row_heading level0 row13\" >13</th>\n", "      <td id=\"T_5e9d0_row13_col0\" class=\"data row13 col0\" >-7.5%</td>\n", "      <td id=\"T_5e9d0_row13_col1\" class=\"data row13 col1\" >95.4%</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5e9d0_level0_row14\" class=\"row_heading level0 row14\" >14</th>\n", "      <td id=\"T_5e9d0_row14_col0\" class=\"data row14 col0\" >-7.6%</td>\n", "      <td id=\"T_5e9d0_row14_col1\" class=\"data row14 col1\" >96.3%</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1748edf50>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["data = []\n", "for loss in np.linspace(-0.06, -0.076, 15):\n", "    data.append((loss, 1- r.cnr.le(loss).mean()))\n", "\n", "df = pd.DataFrame(data, columns=['最大亏损', '抄底胜率'])\n", "df.style.format(\"{:.1%}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "coursea", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}