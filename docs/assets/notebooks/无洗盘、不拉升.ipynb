{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-05-18 09:33:48,317 I 2042631 cfg4py.core:update_config:280 | configuration is\n", "alpha: {data_home: ~/zillionare/alpha/data, tts_server: 'http://127.0.0.1:5002/api/tts?'}\n", "backtest: {url: 'http://192.168.100.114:7080/backtest/api/trade/v0.5/'}\n", "influxdb: {bucket_name: zillionare, enable_compress: true, max_query_size: 5000, org: zillionare,\n", "  token: hwxHycJfp_t6bCOYe2MhEDW4QBOO4FDtgeBWnPR6bGZJGEZ_41m_OHtTJFZKyD2HsbVqkZM8rJNkMvjyoXCG6Q==,\n", "  url: 'http://192.168.100.101:58086'}\n", "notify: {dingtalk_access_token: 58df072143b52368086736cb38236753073ccde6537650cad1d5567747803563,\n", "  keyword: trader}\n", "pluto: {store: ~/zillionare/pluto/store}\n", "redis: {dsn: 'redis://192.168.100.101:56379'}\n", "tasks: {pooling: false, wr: false}\n", "\n", "2024-05-18 09:33:48,322 I 2042631 /home/<USER>/miniconda3/envs/coursea/lib/python3.8/site-packages/omicron/dal/cache.py:init:94 | init redis cache...\n", "2024-05-18 09:33:48,340 I 2042631 /home/<USER>/miniconda3/envs/coursea/lib/python3.8/site-packages/omicron/dal/cache.py:init:124 | redis cache is inited\n", "2024-05-18 09:33:48,508 I 2042631 omicron.models.security:load_securities:333 | 7123 securities loaded, types: {'fjm', 'index', 'reits', 'mmf', 'etf', 'stock', 'lof'}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["init securities done\n"]}], "source": ["from coursea import *\n", "await init()"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.7, 0.15, 0.15]\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"close": [9.76, 9.83, 10.17, 10.39, 11.34, 11.15, 11.37, 11.18, 11.16, 11.35, 11.29, 11.34, 10.9, 10.81, 10.41, 10.66, 10.99, 11.4, 11.89, 11.7, 10.99, 11.38, 10.87, 10.82, 12.98, 11.11, 9.01, 9.86, 9.53, 9.57, 9.39, 9.49, 9.94, 10.38, 10.24, 11.19, 11.29, 12.18, 14.62, 15.51], "decreasing": {"fillcolor": "#3DAA70", "line": {"color": "#3DAA70"}}, "high": [10.079999923706055, 9.960000038146973, 10.1899995803833, 10.390000343322754, 11.880000114440918, 12.279999732971191, 11.65999984741211, 11.460000038146973, 11.359999656677246, 11.479999542236328, 11.460000038146973, 11.609999656677246, 11.359999656677246, 11.039999961853027, 10.949999809265137, 10.890000343322754, 11, 11.420000076293945, 12.119999885559082, 12.09000015258789, 11.670000076293945, 11.399999618530273, 11.350000381469727, 11.069999694824219, 12.979999542236328, 12.890000343322754, 10.84000015258789, 9.960000038146973, 9.819999694824219, 9.979999542236328, 9.84000015258789, 9.680000305175781, 10.489999771118164, 10.65999984741211, 10.40999984741211, 11.1899995803833, 11.869999885559082, 12.369999885559082, 14.619999885559082, 16.8799991607666], "increasing": {"fillcolor": "rgba(255,255,255,0.9)", "line": {"color": "#FF4136"}}, "line": {"width": 1}, "low": [9.75, 9.640000343322754, 9.800000190734863, 10.109999656677246, 10.279999732971191, 10.800000190734863, 10.75, 11.069999694824219, 11.029999732971191, 11.09000015258789, 11.09000015258789, 11.170000076293945, 10.880000114440918, 10.489999771118164, 10.390000343322754, 10.359999656677246, 10.619999885559082, 10.989999771118164, 11.380000114440918, 11.420000076293945, 10.970000267028809, 10.90999984741211, 10.720000267028809, 10.630000114440918, 10.720000267028809, 10.760000228881836, 8.979999542236328, 9.100000381469727, 9.319999694824219, 9.119999885559082, 9.149999618530273, 9.1899995803833, 9.529999732971191, 9.84000015258789, 9.949999809265137, 10.319999694824219, 10.899999618530273, 10.949999809265137, 12.899999618530273, 14.699999809265137], "name": "K线", "open": [9.930000305175781, 9.770000457763672, 9.829999923706055, 10.220000267028809, 10.350000381469727, 11.210000038146973, 11, 11.3100004196167, 11.199999809265137, 11.149999618530273, 11.300000190734863, 11.279999732971191, 11.329999923706055, 11, 10.850000381469727, 10.470000267028809, 10.619999885559082, 10.989999771118164, 11.40999984741211, 11.899999618530273, 11.670000076293945, 10.989999771118164, 11.34000015258789, 10.869999885559082, 11.029999732971191, 12.640000343322754, 10.789999961853027, 9.140000343322754, 9.65999984741211, 9.460000038146973, 9.579999923706055, 9.279999732971191, 9.609999656677246, 9.84000015258789, 10.09000015258789, 10.489999771118164, 11.069999694824219, 11.479999542236328, 12.899999618530273, 15], "type": "candlestick", "x": ["2024-03-07", "2024-03-08", "2024-03-11", "2024-03-12", "2024-03-13", "2024-03-14", "2024-03-15", "2024-03-18", "2024-03-19", "2024-03-20", "2024-03-21", "2024-03-22", "2024-03-25", "2024-03-26", "2024-03-27", "2024-03-28", "2024-03-29", "2024-04-01", "2024-04-02", "2024-04-03", "2024-04-08", "2024-04-09", "2024-04-10", "2024-04-11", "2024-04-12", "2024-04-15", "2024-04-16", "2024-04-17", "2024-04-18", "2024-04-19", "2024-04-22", "2024-04-23", "2024-04-24", "2024-04-25", "2024-04-26", "2024-04-29", "2024-04-30", "2024-05-06", "2024-05-07", "2024-05-08"], "xaxis": "x", "yaxis": "y"}, {"marker": {"color": ["#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136", "#FF4136"]}, "showlegend": false, "type": "bar", "x": ["2024-03-07", "2024-03-08", "2024-03-11", "2024-03-12", "2024-03-13", "2024-03-14", "2024-03-15", "2024-03-18", "2024-03-19", "2024-03-20", "2024-03-21", "2024-03-22", "2024-03-25", "2024-03-26", "2024-03-27", "2024-03-28", "2024-03-29", "2024-04-01", "2024-04-02", "2024-04-03", "2024-04-08", "2024-04-09", "2024-04-10", "2024-04-11", "2024-04-12", "2024-04-15", "2024-04-16", "2024-04-17", "2024-04-18", "2024-04-19", "2024-04-22", "2024-04-23", "2024-04-24", "2024-04-25", "2024-04-26", "2024-04-29", "2024-04-30", "2024-05-06", "2024-05-07", "2024-05-08"], "xaxis": "x2", "y": [2779895, 2351190, 2989823, 2644659, 9535116, 11689710, 9180880, 6802940, 5927290, 5175887, 3469620, 5567893, 4074230, 3640150, 2907470, 2798090, 2406550, 2888650, 5709092, 4510395, 4361438, 3772050, 3353510, 2440700, 15502974, 19170599, 15716498, 13359805, 7716160, 8846134, 7128420, 4935210, 6557523, 8370579, 7036173, 8568020, 10736353, 13392983, 15811762, 37701628], "yaxis": "y3"}, {"showlegend": false, "type": "scatter", "x": ["2024-03-07", "2024-03-08", "2024-03-11", "2024-03-12", "2024-03-13", "2024-03-14", "2024-03-15", "2024-03-18", "2024-03-19", "2024-03-20", "2024-03-21", "2024-03-22", "2024-03-25", "2024-03-26", "2024-03-27", "2024-03-28", "2024-03-29", "2024-04-01", "2024-04-02", "2024-04-03", "2024-04-08", "2024-04-09", "2024-04-10", "2024-04-11", "2024-04-12", "2024-04-15", "2024-04-16", "2024-04-17", "2024-04-18", "2024-04-19", "2024-04-22", "2024-04-23", "2024-04-24", "2024-04-25", "2024-04-26", "2024-04-29", "2024-04-30", "2024-05-06", "2024-05-07", "2024-05-08"], "xaxis": "x3", "y": [null, null, null, null, null, null, 90.45226130653269, 81.15419296663664, 80.11393982552968, 82.64935226319554, 78.84021550370137, 79.772460721712, 54.443041911970234, 50.506704252374966, 36.45087089714376, 47.424541118938116, 58.716595962380055, 68.72977664836985, 76.79989595019306, 68.56614931731667, 46.30489037469858, 55.77033673214573, 43.68572442373797, 42.59975491803602, 74.91986889855926, 47.269165902448016, 31.568403974827486, 41.074966418169595, 38.578161435285395, 39.116473978167534, 37.34887799335885, 39.18106933890979, 47.47568275640358, 54.72126173921858, 51.98326553096495, 65.88340676398286, 67.08692504484497, 76.09372018807447, 87.4196740137742, 89.58038173157317], "yaxis": "y5"}], "layout": {"annotations": [{"font": {"size": 16}, "showarrow": false, "text": "volume", "x": 0.47, "xanchor": "center", "xref": "paper", "y": 0.33999999999999997, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "rsi", "x": 0.47, "xanchor": "center", "xref": "paper", "y": 0.12, "yanchor": "bottom", "yref": "paper"}], "hovermode": "x unified", "plot_bgcolor": "rgba(0,0,0,0)", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "xaxis": {"anchor": "y", "domain": [0, 0.94], "matches": "x3", "range": [-80, 39], "rangeslider": {"visible": false}, "showgrid": false, "showspikes": true, "showticklabels": false, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "type": "category"}, "xaxis2": {"anchor": "y3", "domain": [0, 0.94], "matches": "x3", "range": [-80, 39], "showgrid": false, "showspikes": true, "showticklabels": false, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "type": "category"}, "xaxis3": {"anchor": "y5", "domain": [0, 0.94], "minor": {"nticks": 5, "ticklen": 5, "ticks": "outside"}, "nticks": 4, "range": [-80, 39], "showgrid": false, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "ticklen": 10, "ticks": "outside", "type": "category"}, "yaxis": {"anchor": "x", "domain": [0.44, 1], "gridcolor": "rgba(0, 0, 0, 0.1)", "range": [8.530999565124512, 17.723999118804933], "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis2": {"anchor": "x", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis3": {"anchor": "x2", "domain": [0.22, 0.33999999999999997], "gridcolor": "rgba(0, 0, 0, 0.1)", "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis4": {"anchor": "x2", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y3", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis5": {"anchor": "x3", "domain": [0, 0.12], "gridcolor": "rgba(0, 0, 0, 0.1)", "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis6": {"anchor": "x3", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y5", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}}}, "text/html": ["<div>                            <div id=\"07f5fcae-5fa3-49e2-9fe2-15a279f05845\" class=\"plotly-graph-div\" style=\"height:525px; width:100%;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"07f5fcae-5fa3-49e2-9fe2-15a279f05845\")) {                    Plotly.newPlot(                        \"07f5fcae-5fa3-49e2-9fe2-15a279f05845\",                        [{\"close\":[9.76,9.83,10.17,10.39,11.34,11.15,11.37,11.18,11.16,11.35,11.29,11.34,10.9,10.81,10.41,10.66,10.99,11.4,11.89,11.7,10.99,11.38,10.87,10.82,12.98,11.11,9.01,9.86,9.53,9.57,9.39,9.49,9.94,10.38,10.24,11.19,11.29,12.18,14.62,15.51],\"decreasing\":{\"fillcolor\":\"#3DAA70\",\"line\":{\"color\":\"#3DAA70\"}},\"high\":[10.079999923706055,9.960000038146973,10.1899995803833,10.390000343322754,11.880000114440918,12.279999732971191,11.65999984741211,11.460000038146973,11.359999656677246,11.479999542236328,11.460000038146973,11.609999656677246,11.359999656677246,11.039999961853027,10.949999809265137,10.890000343322754,11.0,11.420000076293945,12.119999885559082,12.09000015258789,11.670000076293945,11.399999618530273,11.350000381469727,11.069999694824219,12.979999542236328,12.890000343322754,10.84000015258789,9.960000038146973,9.819999694824219,9.979999542236328,9.84000015258789,9.680000305175781,10.489999771118164,10.65999984741211,10.40999984741211,11.1899995803833,11.869999885559082,12.369999885559082,14.619999885559082,16.8799991607666],\"increasing\":{\"fillcolor\":\"rgba(255,255,255,0.9)\",\"line\":{\"color\":\"#FF4136\"}},\"line\":{\"width\":1},\"low\":[9.75,9.640000343322754,9.800000190734863,10.109999656677246,10.279999732971191,10.800000190734863,10.75,11.069999694824219,11.029999732971191,11.09000015258789,11.09000015258789,11.170000076293945,10.880000114440918,10.489999771118164,10.390000343322754,10.359999656677246,10.619999885559082,10.989999771118164,11.380000114440918,11.420000076293945,10.970000267028809,10.90999984741211,10.720000267028809,10.630000114440918,10.720000267028809,10.760000228881836,8.979999542236328,9.100000381469727,9.319999694824219,9.119999885559082,9.149999618530273,9.1899995803833,9.529999732971191,9.84000015258789,9.949999809265137,10.319999694824219,10.899999618530273,10.949999809265137,12.899999618530273,14.699999809265137],\"name\":\"K\\u7ebf\",\"open\":[9.930000305175781,9.770000457763672,9.829999923706055,10.220000267028809,10.350000381469727,11.210000038146973,11.0,11.3100004196167,11.199999809265137,11.149999618530273,11.300000190734863,11.279999732971191,11.329999923706055,11.0,10.850000381469727,10.470000267028809,10.619999885559082,10.989999771118164,11.40999984741211,11.899999618530273,11.670000076293945,10.989999771118164,11.34000015258789,10.869999885559082,11.029999732971191,12.640000343322754,10.789999961853027,9.140000343322754,9.65999984741211,9.460000038146973,9.579999923706055,9.279999732971191,9.609999656677246,9.84000015258789,10.09000015258789,10.489999771118164,11.069999694824219,11.479999542236328,12.899999618530273,15.0],\"x\":[\"2024-03-07\",\"2024-03-08\",\"2024-03-11\",\"2024-03-12\",\"2024-03-13\",\"2024-03-14\",\"2024-03-15\",\"2024-03-18\",\"2024-03-19\",\"2024-03-20\",\"2024-03-21\",\"2024-03-22\",\"2024-03-25\",\"2024-03-26\",\"2024-03-27\",\"2024-03-28\",\"2024-03-29\",\"2024-04-01\",\"2024-04-02\",\"2024-04-03\",\"2024-04-08\",\"2024-04-09\",\"2024-04-10\",\"2024-04-11\",\"2024-04-12\",\"2024-04-15\",\"2024-04-16\",\"2024-04-17\",\"2024-04-18\",\"2024-04-19\",\"2024-04-22\",\"2024-04-23\",\"2024-04-24\",\"2024-04-25\",\"2024-04-26\",\"2024-04-29\",\"2024-04-30\",\"2024-05-06\",\"2024-05-07\",\"2024-05-08\"],\"type\":\"candlestick\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"marker\":{\"color\":[\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\",\"#FF4136\"]},\"showlegend\":false,\"x\":[\"2024-03-07\",\"2024-03-08\",\"2024-03-11\",\"2024-03-12\",\"2024-03-13\",\"2024-03-14\",\"2024-03-15\",\"2024-03-18\",\"2024-03-19\",\"2024-03-20\",\"2024-03-21\",\"2024-03-22\",\"2024-03-25\",\"2024-03-26\",\"2024-03-27\",\"2024-03-28\",\"2024-03-29\",\"2024-04-01\",\"2024-04-02\",\"2024-04-03\",\"2024-04-08\",\"2024-04-09\",\"2024-04-10\",\"2024-04-11\",\"2024-04-12\",\"2024-04-15\",\"2024-04-16\",\"2024-04-17\",\"2024-04-18\",\"2024-04-19\",\"2024-04-22\",\"2024-04-23\",\"2024-04-24\",\"2024-04-25\",\"2024-04-26\",\"2024-04-29\",\"2024-04-30\",\"2024-05-06\",\"2024-05-07\",\"2024-05-08\"],\"y\":[2779895.0,2351190.0,2989823.0,2644659.0,9535116.0,11689710.0,9180880.0,6802940.0,5927290.0,5175887.0,3469620.0,5567893.0,4074230.0,3640150.0,2907470.0,2798090.0,2406550.0,2888650.0,5709092.0,4510395.0,4361438.0,3772050.0,3353510.0,2440700.0,15502974.0,19170599.0,15716498.0,13359805.0,7716160.0,8846134.0,7128420.0,4935210.0,6557523.0,8370579.0,7036173.0,8568020.0,10736353.0,13392983.0,15811762.0,37701628.0],\"type\":\"bar\",\"xaxis\":\"x2\",\"yaxis\":\"y3\"},{\"showlegend\":false,\"x\":[\"2024-03-07\",\"2024-03-08\",\"2024-03-11\",\"2024-03-12\",\"2024-03-13\",\"2024-03-14\",\"2024-03-15\",\"2024-03-18\",\"2024-03-19\",\"2024-03-20\",\"2024-03-21\",\"2024-03-22\",\"2024-03-25\",\"2024-03-26\",\"2024-03-27\",\"2024-03-28\",\"2024-03-29\",\"2024-04-01\",\"2024-04-02\",\"2024-04-03\",\"2024-04-08\",\"2024-04-09\",\"2024-04-10\",\"2024-04-11\",\"2024-04-12\",\"2024-04-15\",\"2024-04-16\",\"2024-04-17\",\"2024-04-18\",\"2024-04-19\",\"2024-04-22\",\"2024-04-23\",\"2024-04-24\",\"2024-04-25\",\"2024-04-26\",\"2024-04-29\",\"2024-04-30\",\"2024-05-06\",\"2024-05-07\",\"2024-05-08\"],\"y\":[null,null,null,null,null,null,90.45226130653269,81.15419296663664,80.11393982552968,82.64935226319554,78.84021550370137,79.772460721712,54.443041911970234,50.506704252374966,36.45087089714376,47.424541118938116,58.716595962380055,68.72977664836985,76.79989595019306,68.56614931731667,46.30489037469858,55.77033673214573,43.68572442373797,42.59975491803602,74.91986889855926,47.269165902448016,31.568403974827486,41.074966418169595,38.578161435285395,39.116473978167534,37.34887799335885,39.18106933890979,47.47568275640358,54.72126173921858,51.98326553096495,65.88340676398286,67.08692504484497,76.09372018807447,87.4196740137742,89.58038173157317],\"type\":\"scatter\",\"xaxis\":\"x3\",\"yaxis\":\"y5\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,0.94],\"matches\":\"x3\",\"showticklabels\":false,\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"type\":\"category\",\"range\":[-80,39],\"rangeslider\":{\"visible\":false}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.44,1.0],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\",\"range\":[8.530999565124512,17.723999118804933]},\"yaxis2\":{\"anchor\":\"x\",\"overlaying\":\"y\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"xaxis2\":{\"anchor\":\"y3\",\"domain\":[0.0,0.94],\"matches\":\"x3\",\"showticklabels\":false,\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"type\":\"category\",\"range\":[-80,39]},\"yaxis3\":{\"anchor\":\"x2\",\"domain\":[0.22,0.33999999999999997],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"yaxis4\":{\"anchor\":\"x2\",\"overlaying\":\"y3\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"xaxis3\":{\"anchor\":\"y5\",\"domain\":[0.0,0.94],\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"minor\":{\"nticks\":5,\"ticklen\":5,\"ticks\":\"outside\"},\"nticks\":4,\"ticklen\":10,\"ticks\":\"outside\",\"type\":\"category\",\"range\":[-80,39]},\"yaxis5\":{\"anchor\":\"x3\",\"domain\":[0.0,0.12],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"yaxis6\":{\"anchor\":\"x3\",\"overlaying\":\"y5\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"annotations\":[{\"font\":{\"size\":16},\"showarrow\":false,\"text\":\"volume\",\"x\":0.47,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":0.33999999999999997,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{\"size\":16},\"showarrow\":false,\"text\":\"rsi\",\"x\":0.47,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":0.12,\"yanchor\":\"bottom\",\"yref\":\"paper\"}],\"hovermode\":\"x unified\",\"plot_bgcolor\":\"rgba(0,0,0,0)\"},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('07f5fcae-5fa3-49e2-9fe2-15a279f05845');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["code = \"300721.XSHE\"\n", "end = datetime.date(2024, 5, 8)\n", "bars = await Stock.get_bars(code, 40, FrameType.DAY, end=end)\n", "cs = Candlestick(bars, ma_groups=[])\n", "cs.plot()"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 0.01  0.03  0.02  0.1  -0.02  0.03 -0.02 -0.    0.02 -0.01  0.01 -0.04\n", " -0.02 -0.04  0.02  0.03  0.04  0.04 -0.02 -0.06  0.04 -0.04 -0.    0.2\n", " -0.14 -0.19  0.09 -0.03  0.01 -0.02  0.02  0.05  0.05  0.01  0.09  0.02\n", "  0.08  0.2   0.06]\n"]}], "source": ["# 示例2\n", "def feature_washout(bars, threshold=0.05):\n", "    \"\"\"返回在bars中最后一次洗盘结束的位置，-1表示最后一个bar,\n", "        0表示不存在洗盘模式\n", "    \"\"\"\n", "    close = bars[\"close\"]\n", "    opn = bars[\"open\"]\n", "    truerange = np.maximum(np.abs(close[1:] - close[:-1]), \n", "                           np.abs(opn-close)[1:]) \n", "    # 百分比化\n", "    tr = truerange / close[:-1]\n", "    sign = (opn < close)[1:] * 2 - 1\n", "    signed_tr = tr * sign\n", "    \n", "    print(np.round(signed_tr, 2))\n", "    \n", "feature_washout(bars)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.7, 0.15, 0.15]\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"close": [12.16, 12.25, 12.12, 12.09, 12.12, 12.08, 12.04, 12.16, 12.28, 12.21, 12.35, 12.3, 11.73, 11.89, 12.1, 12.07, 12.21, 12.28, 12.15, 12.14, 12.17, 12.21, 12.03, 11.81, 11.82, 11.67, 12.26, 12.2, 12.68, 12.36, 12.46, 12.38, 13.62, 13.04, 12.28, 12.68, 12.69, 12.61, 13.87, 13.38], "decreasing": {"fillcolor": "#3DAA70", "line": {"color": "#3DAA70"}}, "high": [12.25, 12.25, 12.270000457763672, 12.220000267028809, 12.25, 12.180000305175781, 12.210000038146973, 12.220000267028809, 12.40999984741211, 12.399999618530273, 12.380000114440918, 12.399999618530273, 12.289999961853027, 12.039999961853027, 12.130000114440918, 12.210000038146973, 12.279999732971191, 12.3100004196167, 12.380000114440918, 12.270000457763672, 12.369999885559082, 12.300000190734863, 12.359999656677246, 12.029999732971191, 12, 11.949999809265137, 12.329999923706055, 12.289999961853027, 12.800000190734863, 12.850000381469727, 12.699999809265137, 12.680000305175781, 13.619999885559082, 14.979999542236328, 12.970000267028809, 13.100000381469727, 13.65999984741211, 13.199999809265137, 13.869999885559082, 13.869999885559082], "increasing": {"fillcolor": "rgba(255,255,255,0.9)", "line": {"color": "#FF4136"}}, "line": {"width": 1}, "low": [12.079999923706055, 12.050000190734863, 12.069999694824219, 12.039999961853027, 12.029999732971191, 11.989999771118164, 11.9399995803833, 12.0600004196167, 12.170000076293945, 12.1899995803833, 12.119999885559082, 12.180000305175781, 11.699999809265137, 11.619999885559082, 11.829999923706055, 12.039999961853027, 12.069999694824219, 12.180000305175781, 12.09000015258789, 11.989999771118164, 12.140000343322754, 11.9399995803833, 11.960000038146973, 11.699999809265137, 11.6899995803833, 11.619999885559082, 11.5, 12.09000015258789, 12.220000267028809, 12.270000457763672, 12.180000305175781, 12.300000190734863, 12.289999961853027, 13.010000228881836, 11.899999618530273, 11.949999809265137, 12.6899995803833, 12.369999885559082, 12.640000343322754, 13.010000228881836], "name": "K线", "open": [12.109999656677246, 12.130000114440918, 12.270000457763672, 12.100000381469727, 12.09000015258789, 12.119999885559082, 12.140000343322754, 12.100000381469727, 12.210000038146973, 12.279999732971191, 12.149999618530273, 12.359999656677246, 12.289999961853027, 11.619999885559082, 11.890000343322754, 12.149999618530273, 12.260000228881836, 12.210000038146973, 12.279999732971191, 12.1899995803833, 12.140000343322754, 12.170000076293945, 12.329999923706055, 12.029999732971191, 11.789999961853027, 11.829999923706055, 11.699999809265137, 12.260000228881836, 12.25, 12.699999809265137, 12.359999656677246, 12.460000038146973, 12.329999923706055, 14.979999542236328, 12.84000015258789, 11.979999542236328, 13, 12.930000305175781, 12.640000343322754, 13.729999542236328], "type": "candlestick", "x": ["2023-11-22", "2023-11-23", "2023-11-24", "2023-11-27", "2023-11-28", "2023-11-29", "2023-11-30", "2023-12-01", "2023-12-04", "2023-12-05", "2023-12-06", "2023-12-07", "2023-12-08", "2023-12-11", "2023-12-12", "2023-12-13", "2023-12-14", "2023-12-15", "2023-12-18", "2023-12-19", "2023-12-20", "2023-12-21", "2023-12-22", "2023-12-25", "2023-12-26", "2023-12-27", "2023-12-28", "2023-12-29", "2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-08", "2024-01-09", "2024-01-10", "2024-01-11", "2024-01-12", "2024-01-15", "2024-01-16", "2024-01-17"], "xaxis": "x", "yaxis": "y"}, {"marker": {"color": ["#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70", "#3DAA70", "#FF4136", "#3DAA70"]}, "showlegend": false, "type": "bar", "x": ["2023-11-22", "2023-11-23", "2023-11-24", "2023-11-27", "2023-11-28", "2023-11-29", "2023-11-30", "2023-12-01", "2023-12-04", "2023-12-05", "2023-12-06", "2023-12-07", "2023-12-08", "2023-12-11", "2023-12-12", "2023-12-13", "2023-12-14", "2023-12-15", "2023-12-18", "2023-12-19", "2023-12-20", "2023-12-21", "2023-12-22", "2023-12-25", "2023-12-26", "2023-12-27", "2023-12-28", "2023-12-29", "2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-08", "2024-01-09", "2024-01-10", "2024-01-11", "2024-01-12", "2024-01-15", "2024-01-16", "2024-01-17"], "xaxis": "x2", "y": [1031200, 836000, 776500, 819100, 920365, 1068900, 963100, 930200, 1290700, 997800, 988100, 1103500, 1460500, 1664900, 1202000, 885300, 959710, 1247200, 1396100, 1051600, 1162350, 1422500, 1104600, 1151700, 1701000, 1152400, 2218100, 2046900, 2602000, 2032700, 1941210, 1775900, 3714510, 21711165, 14649282, 13062947, 10687782, 6902600, 4790950, 16492050], "yaxis": "y3"}, {"showlegend": false, "type": "scatter", "x": ["2023-11-22", "2023-11-23", "2023-11-24", "2023-11-27", "2023-11-28", "2023-11-29", "2023-11-30", "2023-12-01", "2023-12-04", "2023-12-05", "2023-12-06", "2023-12-07", "2023-12-08", "2023-12-11", "2023-12-12", "2023-12-13", "2023-12-14", "2023-12-15", "2023-12-18", "2023-12-19", "2023-12-20", "2023-12-21", "2023-12-22", "2023-12-25", "2023-12-26", "2023-12-27", "2023-12-28", "2023-12-29", "2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-08", "2024-01-09", "2024-01-10", "2024-01-11", "2024-01-12", "2024-01-15", "2024-01-16", "2024-01-17"], "xaxis": "x3", "y": [null, null, null, null, null, null, 33.33333333333317, 52.380952380952394, 64.53900709219853, 54.75330926594477, 66.82548085406745, 59.96832937450529, 24.94789072731628, 37.28462066593473, 50.18205965041383, 48.47316985854129, 56.725644662608424, 60.519531150896334, 50.62785647529591, 49.87537069072769, 52.42117475379522, 55.99699835710865, 39.83166691912046, 27.983458683307923, 29.133266292411275, 22.629785511907798, 62.32561372630233, 58.65326636089144, 73.59134423358816, 57.09046516727886, 60.41861309079276, 56.23163436969602, 80.87857950190983, 61.45437882702298, 44.60848779563939, 52.78308571937649, 52.99121036098011, 50.83990592555043, 72.18335453730737, 60.022298474538125], "yaxis": "y5"}], "layout": {"annotations": [{"font": {"size": 16}, "showarrow": false, "text": "volume", "x": 0.47, "xanchor": "center", "xref": "paper", "y": 0.33999999999999997, "yanchor": "bottom", "yref": "paper"}, {"font": {"size": 16}, "showarrow": false, "text": "rsi", "x": 0.47, "xanchor": "center", "xref": "paper", "y": 0.12, "yanchor": "bottom", "yref": "paper"}], "hovermode": "x unified", "plot_bgcolor": "rgba(0,0,0,0)", "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 600, "xaxis": {"anchor": "y", "domain": [0, 0.94], "matches": "x3", "range": [-20, 39], "rangeslider": {"visible": false}, "showgrid": false, "showspikes": true, "showticklabels": false, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "type": "category"}, "xaxis2": {"anchor": "y3", "domain": [0, 0.94], "matches": "x3", "range": [-20, 39], "showgrid": false, "showspikes": true, "showticklabels": false, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "type": "category"}, "xaxis3": {"anchor": "y5", "domain": [0, 0.94], "minor": {"nticks": 5, "ticklen": 5, "ticks": "outside"}, "nticks": 4, "range": [-20, 39], "showgrid": false, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1, "ticklen": 10, "ticks": "outside", "type": "category"}, "yaxis": {"anchor": "x", "domain": [0.44, 1], "gridcolor": "rgba(0, 0, 0, 0.1)", "range": [10.924999999999999, 15.728999519348145], "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis2": {"anchor": "x", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis3": {"anchor": "x2", "domain": [0.22, 0.33999999999999997], "gridcolor": "rgba(0, 0, 0, 0.1)", "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis4": {"anchor": "x2", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y3", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis5": {"anchor": "x3", "domain": [0, 0.12], "gridcolor": "rgba(0, 0, 0, 0.1)", "showgrid": true, "showspikes": true, "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}, "yaxis6": {"anchor": "x3", "gridcolor": "rgba(0, 0, 0, 0.1)", "overlaying": "y5", "showgrid": true, "showspikes": true, "side": "right", "spikecolor": "grey", "spikedash": "solid", "spikemode": "across", "spikesnap": "cursor", "spikethickness": 1}}}, "text/html": ["<div>                            <div id=\"227b9c01-e04f-42b1-84a1-55843dc577d2\" class=\"plotly-graph-div\" style=\"height:525px; width:600px;\"></div>            <script type=\"text/javascript\">                require([\"plotly\"], function(Plotly) {                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"227b9c01-e04f-42b1-84a1-55843dc577d2\")) {                    Plotly.newPlot(                        \"227b9c01-e04f-42b1-84a1-55843dc577d2\",                        [{\"close\":[12.16,12.25,12.12,12.09,12.12,12.08,12.04,12.16,12.28,12.21,12.35,12.3,11.73,11.89,12.1,12.07,12.21,12.28,12.15,12.14,12.17,12.21,12.03,11.81,11.82,11.67,12.26,12.2,12.68,12.36,12.46,12.38,13.62,13.04,12.28,12.68,12.69,12.61,13.87,13.38],\"decreasing\":{\"fillcolor\":\"#3DAA70\",\"line\":{\"color\":\"#3DAA70\"}},\"high\":[12.25,12.25,12.270000457763672,12.220000267028809,12.25,12.180000305175781,12.210000038146973,12.220000267028809,12.40999984741211,12.399999618530273,12.380000114440918,12.399999618530273,12.289999961853027,12.039999961853027,12.130000114440918,12.210000038146973,12.279999732971191,12.3100004196167,12.380000114440918,12.270000457763672,12.369999885559082,12.300000190734863,12.359999656677246,12.029999732971191,12.0,11.949999809265137,12.329999923706055,12.289999961853027,12.800000190734863,12.850000381469727,12.699999809265137,12.680000305175781,13.619999885559082,14.979999542236328,12.970000267028809,13.100000381469727,13.65999984741211,13.199999809265137,13.869999885559082,13.869999885559082],\"increasing\":{\"fillcolor\":\"rgba(255,255,255,0.9)\",\"line\":{\"color\":\"#FF4136\"}},\"line\":{\"width\":1},\"low\":[12.079999923706055,12.050000190734863,12.069999694824219,12.039999961853027,12.029999732971191,11.989999771118164,11.9399995803833,12.0600004196167,12.170000076293945,12.1899995803833,12.119999885559082,12.180000305175781,11.699999809265137,11.619999885559082,11.829999923706055,12.039999961853027,12.069999694824219,12.180000305175781,12.09000015258789,11.989999771118164,12.140000343322754,11.9399995803833,11.960000038146973,11.699999809265137,11.6899995803833,11.619999885559082,11.5,12.09000015258789,12.220000267028809,12.270000457763672,12.180000305175781,12.300000190734863,12.289999961853027,13.010000228881836,11.899999618530273,11.949999809265137,12.6899995803833,12.369999885559082,12.640000343322754,13.010000228881836],\"name\":\"K\\u7ebf\",\"open\":[12.109999656677246,12.130000114440918,12.270000457763672,12.100000381469727,12.09000015258789,12.119999885559082,12.140000343322754,12.100000381469727,12.210000038146973,12.279999732971191,12.149999618530273,12.359999656677246,12.289999961853027,11.619999885559082,11.890000343322754,12.149999618530273,12.260000228881836,12.210000038146973,12.279999732971191,12.1899995803833,12.140000343322754,12.170000076293945,12.329999923706055,12.029999732971191,11.789999961853027,11.829999923706055,11.699999809265137,12.260000228881836,12.25,12.699999809265137,12.359999656677246,12.460000038146973,12.329999923706055,14.979999542236328,12.84000015258789,11.979999542236328,13.0,12.930000305175781,12.640000343322754,13.729999542236328],\"x\":[\"2023-11-22\",\"2023-11-23\",\"2023-11-24\",\"2023-11-27\",\"2023-11-28\",\"2023-11-29\",\"2023-11-30\",\"2023-12-01\",\"2023-12-04\",\"2023-12-05\",\"2023-12-06\",\"2023-12-07\",\"2023-12-08\",\"2023-12-11\",\"2023-12-12\",\"2023-12-13\",\"2023-12-14\",\"2023-12-15\",\"2023-12-18\",\"2023-12-19\",\"2023-12-20\",\"2023-12-21\",\"2023-12-22\",\"2023-12-25\",\"2023-12-26\",\"2023-12-27\",\"2023-12-28\",\"2023-12-29\",\"2024-01-02\",\"2024-01-03\",\"2024-01-04\",\"2024-01-05\",\"2024-01-08\",\"2024-01-09\",\"2024-01-10\",\"2024-01-11\",\"2024-01-12\",\"2024-01-15\",\"2024-01-16\",\"2024-01-17\"],\"type\":\"candlestick\",\"xaxis\":\"x\",\"yaxis\":\"y\"},{\"marker\":{\"color\":[\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\",\"#3DAA70\",\"#FF4136\",\"#3DAA70\"]},\"showlegend\":false,\"x\":[\"2023-11-22\",\"2023-11-23\",\"2023-11-24\",\"2023-11-27\",\"2023-11-28\",\"2023-11-29\",\"2023-11-30\",\"2023-12-01\",\"2023-12-04\",\"2023-12-05\",\"2023-12-06\",\"2023-12-07\",\"2023-12-08\",\"2023-12-11\",\"2023-12-12\",\"2023-12-13\",\"2023-12-14\",\"2023-12-15\",\"2023-12-18\",\"2023-12-19\",\"2023-12-20\",\"2023-12-21\",\"2023-12-22\",\"2023-12-25\",\"2023-12-26\",\"2023-12-27\",\"2023-12-28\",\"2023-12-29\",\"2024-01-02\",\"2024-01-03\",\"2024-01-04\",\"2024-01-05\",\"2024-01-08\",\"2024-01-09\",\"2024-01-10\",\"2024-01-11\",\"2024-01-12\",\"2024-01-15\",\"2024-01-16\",\"2024-01-17\"],\"y\":[1031200.0,836000.0,776500.0,819100.0,920365.0,1068900.0,963100.0,930200.0,1290700.0,997800.0,988100.0,1103500.0,1460500.0,1664900.0,1202000.0,885300.0,959710.0,1247200.0,1396100.0,1051600.0,1162350.0,1422500.0,1104600.0,1151700.0,1701000.0,1152400.0,2218100.0,2046900.0,2602000.0,2032700.0,1941210.0,1775900.0,3714510.0,21711165.0,14649282.0,13062947.0,10687782.0,6902600.0,4790950.0,16492050.0],\"type\":\"bar\",\"xaxis\":\"x2\",\"yaxis\":\"y3\"},{\"showlegend\":false,\"x\":[\"2023-11-22\",\"2023-11-23\",\"2023-11-24\",\"2023-11-27\",\"2023-11-28\",\"2023-11-29\",\"2023-11-30\",\"2023-12-01\",\"2023-12-04\",\"2023-12-05\",\"2023-12-06\",\"2023-12-07\",\"2023-12-08\",\"2023-12-11\",\"2023-12-12\",\"2023-12-13\",\"2023-12-14\",\"2023-12-15\",\"2023-12-18\",\"2023-12-19\",\"2023-12-20\",\"2023-12-21\",\"2023-12-22\",\"2023-12-25\",\"2023-12-26\",\"2023-12-27\",\"2023-12-28\",\"2023-12-29\",\"2024-01-02\",\"2024-01-03\",\"2024-01-04\",\"2024-01-05\",\"2024-01-08\",\"2024-01-09\",\"2024-01-10\",\"2024-01-11\",\"2024-01-12\",\"2024-01-15\",\"2024-01-16\",\"2024-01-17\"],\"y\":[null,null,null,null,null,null,33.33333333333317,52.380952380952394,64.53900709219853,54.75330926594477,66.82548085406745,59.96832937450529,24.94789072731628,37.28462066593473,50.18205965041383,48.47316985854129,56.725644662608424,60.519531150896334,50.62785647529591,49.87537069072769,52.42117475379522,55.99699835710865,39.83166691912046,27.983458683307923,29.133266292411275,22.629785511907798,62.32561372630233,58.65326636089144,73.59134423358816,57.09046516727886,60.41861309079276,56.23163436969602,80.87857950190983,61.45437882702298,44.60848779563939,52.78308571937649,52.99121036098011,50.83990592555043,72.18335453730737,60.022298474538125],\"type\":\"scatter\",\"xaxis\":\"x3\",\"yaxis\":\"y5\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,0.94],\"matches\":\"x3\",\"showticklabels\":false,\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"type\":\"category\",\"range\":[-20,39],\"rangeslider\":{\"visible\":false}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.44,1.0],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\",\"range\":[10.924999999999999,15.728999519348145]},\"yaxis2\":{\"anchor\":\"x\",\"overlaying\":\"y\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"xaxis2\":{\"anchor\":\"y3\",\"domain\":[0.0,0.94],\"matches\":\"x3\",\"showticklabels\":false,\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"type\":\"category\",\"range\":[-20,39]},\"yaxis3\":{\"anchor\":\"x2\",\"domain\":[0.22,0.33999999999999997],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"yaxis4\":{\"anchor\":\"x2\",\"overlaying\":\"y3\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"xaxis3\":{\"anchor\":\"y5\",\"domain\":[0.0,0.94],\"showgrid\":false,\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikecolor\":\"grey\",\"spikedash\":\"solid\",\"spikethickness\":1,\"minor\":{\"nticks\":5,\"ticklen\":5,\"ticks\":\"outside\"},\"nticks\":4,\"ticklen\":10,\"ticks\":\"outside\",\"type\":\"category\",\"range\":[-20,39]},\"yaxis5\":{\"anchor\":\"x3\",\"domain\":[0.0,0.12],\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"yaxis6\":{\"anchor\":\"x3\",\"overlaying\":\"y5\",\"side\":\"right\",\"showspikes\":true,\"spikemode\":\"across\",\"spikesnap\":\"cursor\",\"spikedash\":\"solid\",\"spikecolor\":\"grey\",\"spikethickness\":1,\"showgrid\":true,\"gridcolor\":\"rgba(0, 0, 0, 0.1)\"},\"annotations\":[{\"font\":{\"size\":16},\"showarrow\":false,\"text\":\"volume\",\"x\":0.47,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":0.33999999999999997,\"yanchor\":\"bottom\",\"yref\":\"paper\"},{\"font\":{\"size\":16},\"showarrow\":false,\"text\":\"rsi\",\"x\":0.47,\"xanchor\":\"center\",\"xref\":\"paper\",\"y\":0.12,\"yanchor\":\"bottom\",\"yref\":\"paper\"}],\"hovermode\":\"x unified\",\"plot_bgcolor\":\"rgba(0,0,0,0)\",\"width\":600},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('227b9c01-e04f-42b1-84a1-55843dc577d2');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };                });            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 高开洗盘\n", "\n", "code = \"605303.XSHG\"\n", "end = datetime.date(2024, 1, 17)\n", "bars = await Stock.get_bars(code, 40, FrameType.DAY, end=end)\n", "cs = Candlestick(bars, ma_groups=[], width=600)\n", "cs.plot()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["除了使用$np.maximimum$这种 ufunc 之外，实际上$np.max$也可以用来完成这项任务，只是我们需要先将数组$A$和$B$堆叠成一个矩阵："]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["# 示例2\n", "A = np.arange(4)\n", "B = np.arange(3, 7)\n", "C = np.arange(8, 4, -1)\n", "\n", "Z = np.vstack((A,B,C))\n", "\n", "# 通过np.max求每列最大值\n", "r1 = np.max(Z, axis=0)\n", "\n", "# 通过np.maximum求最大值\n", "r2 = np.maximum.reduce([A, B, C])\n", "\n", "np.array_equal(r1, r2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["    # xpflag = [0] * 2\n", "    # for item in sliding_window_view(binned, window_shape = 3):\n", "    #     if np.array_equal([-1, -1, 1], item):\n", "    #         xpflag.append(1)\n", "    #     else:\n", "    #         xpflag.append(0)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/plain": ["[23]"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["# 示例3\n", "from numpy.lib.stride_tricks import sliding_window_view\n", "def feature_washout(bars, threshold=0.05):\n", "    \"\"\"返回在bars中最后一次洗盘结束的位置，-1表示最后一个bar,\n", "        0表示不存在洗盘模式\n", "    \"\"\"\n", "    close = bars[\"close\"]\n", "    opn = bars[\"open\"]\n", "    truerange = np.maximum(np.abs(close[1:] - close[:-1]), \n", "                           np.abs(opn-close)[1:]) \n", "    # 百分比化\n", "    tr = truerange / close[:-1]\n", "    sign = (opn < close)[1:] * 2 - 1\n", "    signed_tr = tr * sign\n", "    \n", "    encoded = np.select([signed_tr > threshold, \n", "                    signed_tr < -threshold], \n", "                    [1, -1], 0)\n", "\n", "    washouts = []\n", "    for i, patten in enumerate(sliding_window_view(encoded, window_shape = 4)):\n", "        if np.array_equal(patten, [1, -1, -1, 1]):\n", "            washouts.append(i)\n", "\n", "    return washouts\n", "feature_washout(bars)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "coursea", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 2}