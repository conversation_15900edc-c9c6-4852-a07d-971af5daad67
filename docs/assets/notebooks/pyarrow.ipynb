{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-01-02 11:14:28,330 I 635507 cfg4py.core:update_config:280 | configuration is\n", "alpha: {data_home: ~/zillionare/alpha/data, tts_server: 'http://127.0.0.1:5002/api/tts?'}\n", "backtest: {url: 'http://192.168.100.114:7080/backtest/api/trade/v0.5/'}\n", "influxdb: {bucket_name: zillionare, enable_compress: true, max_query_size: 5000, org: zillionare,\n", "  token: hwxHycJfp_t6bCOYe2MhEDW4QBOO4FDtgeBWnPR6bGZJGEZ_41m_OHtTJFZKyD2HsbVqkZM8rJNkMvjyoXCG6Q==,\n", "  url: 'http://192.168.100.101:58086'}\n", "notify: {dingtalk_access_token: 58df072143b52368086736cb38236753073ccde6537650cad1d5567747803563,\n", "  keyword: trader}\n", "pluto: {store: ~/zillionare/pluto/store}\n", "redis: {dsn: 'redis://192.168.100.101:56379'}\n", "tasks: {pooling: false, wr: false}\n", "\n", "2024-01-02 11:14:28,333 I 635507 /home/<USER>/miniconda3/envs/coursea/lib/python3.8/site-packages/omicron/dal/cache.py:init:94 | init redis cache...\n", "2024-01-02 11:14:28,346 I 635507 /home/<USER>/miniconda3/envs/coursea/lib/python3.8/site-packages/omicron/dal/cache.py:init:124 | redis cache is inited\n", "2024-01-02 11:14:28,455 I 635507 omicron.models.security:load_securities:333 | 7039 securities loaded, types: {'index', 'fjm', 'mmf', 'etf', 'reits', 'lof', 'stock'}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["init securities done\n"]}], "source": ["from coursea import *\n", "await init()\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["import pyarrow as pa\n", "\n", "code = \"000001.XSHE\"\n", "bars = await Stock.get_bars(code, 240, FrameType.MIN1)\n", "\n", "# 从BarsArray构建table\n", "schema = pa.schema([(\"frame\", pa.date64()),\n", "         (\"open\", pa.float64()),\n", "         (\"high\", pa.float64()),\n", "         (\"low\", pa.float64()),\n", "         (\"close\", pa.float64()),\n", "         (\"volume\", pa.float64()),\n", "         (\"money\", pa.float64()),\n", "         (\"factor\", pa.float64())\n", "])\n", "\n", "\n", "table = pa.Table.from_arrays([\n", "    bars[key] for key in bars.dtype.names\n", "], schema=schema)\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["pyarrow.Table\n", "frame: date64[ms]\n", "open: double\n", "high: double\n", "low: double\n", "close: double\n", "volume: double\n", "money: double\n", "factor: double\n", "----\n", "frame: [[2024-01-02,2024-01-02,2024-01-02,2024-01-02,2024-01-02,...,2024-01-02,2024-01-02,2024-01-02,2024-01-02,2024-01-02]]\n", "open: [[9.390000343322754,9.369999885559082,9.380000114440918,9.380000114440918,9.350000381469727,...,9.220000267028809,9.220000267028809,9.210000038146973,9.210000038146973,9.210000038146973]]\n", "high: [[9.420000076293945,9.380000114440918,9.380000114440918,9.380000114440918,9.350000381469727,...,9.220000267028809,9.220000267028809,9.210000038146973,9.210000038146973,9.210000038146973]]\n", "low: [[9.359999656677246,9.369999885559082,9.369999885559082,9.350000381469727,9.34000015258789,...,9.210000038146973,9.210000038146973,9.210000038146973,9.210000038146973,9.210000038146973]]\n", "close: [[9.369999885559082,9.380000114440918,9.369999885559082,9.350000381469727,9.34000015258789,...,9.210000038146973,9.220000267028809,9.210000038146973,9.210000038146973,9.210000038146973]]\n", "volume: [[4953300,713100,480300,2098800,738200,...,506100,674100,9700,0,1924828]]\n", "money: [[46514749.35,6686130,4502126,19652728,6900378,...,4663624,6212845.429999948,89416.42999994755,1.2899998426437378,17727666.73999989]]\n", "factor: [[126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172,...,126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172]]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["table\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["import pyarrow.parquet as pq\n", "\n", "pq.write_table(table, f\"/tmp/pyarrow/{code}.parquet\")\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["<pyarrow._dataset.FileSystemDataset at 0x7f384598f4c0>"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["import pyarrow.dataset as ds\n", "\n", "dataset = ds.dataset(\"/tmp/pyarrow\")\n", "dataset\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["pyarrow.Table\n", "frame: date32[day]\n", "open: double\n", "high: double\n", "low: double\n", "close: double\n", "volume: double\n", "money: double\n", "factor: double\n", "----\n", "frame: [[2024-01-02,2024-01-02,2024-01-02,2024-01-02,2024-01-02,...,2024-01-02,2024-01-02,2024-01-02,2024-01-02,2024-01-02]]\n", "open: [[9.390000343322754,9.369999885559082,9.380000114440918,9.380000114440918,9.350000381469727,...,9.220000267028809,9.220000267028809,9.210000038146973,9.210000038146973,9.210000038146973]]\n", "high: [[9.420000076293945,9.380000114440918,9.380000114440918,9.380000114440918,9.350000381469727,...,9.220000267028809,9.220000267028809,9.210000038146973,9.210000038146973,9.210000038146973]]\n", "low: [[9.359999656677246,9.369999885559082,9.369999885559082,9.350000381469727,9.34000015258789,...,9.210000038146973,9.210000038146973,9.210000038146973,9.210000038146973,9.210000038146973]]\n", "close: [[9.369999885559082,9.380000114440918,9.369999885559082,9.350000381469727,9.34000015258789,...,9.210000038146973,9.220000267028809,9.210000038146973,9.210000038146973,9.210000038146973]]\n", "volume: [[4953300,713100,480300,2098800,738200,...,506100,674100,9700,0,1924828]]\n", "money: [[46514749.35,6686130,4502126,19652728,6900378,...,4663624,6212845.429999948,89416.42999994755,1.2899998426437378,17727666.73999989]]\n", "factor: [[126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172,...,126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172]]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset.to_table()\n"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["import pyarrow as pa\n", "\n", "schema = pa.schema([\n", "        (\"symbol\", pa.string()),\n", "        (\"frame\", pa.date64()),\n", "        (\"open\", pa.float32()),\n", "        (\"high\", pa.float32()),\n", "        (\"low\", pa.float32()),\n", "        (\"close\", pa.float32()),\n", "        (\"volume\", pa.float64()),\n", "        (\"money\", pa.float64()),\n", "        (\"factor\", pa.float64())\n", "])\n"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [], "source": ["\n", "import arrow\n", "import pyarrow.parquet as pq\n", "\n", "async def save_1m_bars(codes, dt: datetime.datetime):\n", "    tables = None\n", "\n", "    for code in codes:\n", "        bars = await Stock.get_bars(code, 240, FrameType.MIN1, end=dt)\n", "        data = [[code] * len(bars)]\n", "        data.extend([\n", "                    bars[key] for key in bars.dtype.names\n", "                ])\n", "        table = pa.Table.from_arrays(data, schema=schema)\n", "        if tables is None:\n", "            tables = table\n", "        else:\n", "            tables = pa.concat_tables([tables, table])\n", "\n", "    # 写入磁盘\n", "    name = arrow.get(dt).format(\"YYYY-MM-DD\")\n", "    pq.write_table(tables, f\"/tmp/pyarrow/1m/{name}.parquet\")\n", "        \n", "codes = [\"000001.XSHE\", \"600000.XSHG\"]\n", "for i in (25, 26, 27, 28, 29):\n", "    dt = datetime.datetime(2023, 12, i, 15)\n", "    await save_1m_bars(codes, dt)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[01;34m/tmp/pyarrow\u001b[00m\n", "├── \u001b[01;34m1d\u001b[00m\n", "├── \u001b[01;34m1m\u001b[00m\n", "│   ├── 2023-12-25.parquet\n", "│   ├── 2023-12-26.parquet\n", "│   ├── 2023-12-27.parquet\n", "│   ├── 2023-12-28.parquet\n", "│   └── 2023-12-29.parquet\n", "└── \u001b[01;34mfactors\u001b[00m\n", "\n", "3 directories, 5 files\n"]}], "source": ["!tree /tmp/pyarrow\n"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"data": {"text/plain": ["['/tmp/pyarrow/1m/2023-12-25.parquet',\n", " '/tmp/pyarrow/1m/2023-12-26.parquet',\n", " '/tmp/pyarrow/1m/2023-12-27.parquet',\n", " '/tmp/pyarrow/1m/2023-12-28.parquet',\n", " '/tmp/pyarrow/1m/2023-12-29.parquet']"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["# 在分析程序中，加载数据\n", "\n", "import pyarrow.dataset as ds\n", "\n", "dataset = ds.dataset(\"/tmp/pyarrow/1m\")\n", "dataset.files\n"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"data": {"text/plain": ["pyarrow.Table\n", "symbol: string\n", "frame: date32[day]\n", "open: float\n", "high: float\n", "low: float\n", "close: float\n", "volume: double\n", "money: double\n", "factor: double\n", "----\n", "symbol: [[\"000001.XSHE\",\"000001.XSHE\",\"000001.XSHE\",\"000001.XSHE\",\"000001.XSHE\",...,\"600000.XSHG\",\"600000.XSHG\",\"600000.XSHG\",\"600000.XSHG\",\"600000.XSHG\"],[\"000001.XSHE\",\"000001.XSHE\",\"000001.XSHE\",\"000001.XSHE\",\"000001.XSHE\",...,\"600000.XSHG\",\"600000.XSHG\",\"600000.XSHG\",\"600000.XSHG\",\"600000.XSHG\"],...,[\"000001.XSHE\",\"000001.XSHE\",\"000001.XSHE\",\"000001.XSHE\",\"000001.XSHE\",...,\"600000.XSHG\",\"600000.XSHG\",\"600000.XSHG\",\"600000.XSHG\",\"600000.XSHG\"],[\"000001.XSHE\",\"000001.XSHE\",\"000001.XSHE\",\"000001.XSHE\",\"000001.XSHE\",...,\"600000.XSHG\",\"600000.XSHG\",\"600000.XSHG\",\"600000.XSHG\",\"600000.XSHG\"]]\n", "frame: [[2023-12-25,2023-12-25,2023-12-25,2023-12-25,2023-12-25,...,2023-12-25,2023-12-25,2023-12-25,2023-12-25,2023-12-25],[2023-12-26,2023-12-26,2023-12-26,2023-12-26,2023-12-26,...,2023-12-26,2023-12-26,2023-12-26,2023-12-26,2023-12-26],...,[2023-12-28,2023-12-28,2023-12-28,2023-12-28,2023-12-28,...,2023-12-28,2023-12-28,2023-12-28,2023-12-28,2023-12-28],[2023-12-29,2023-12-29,2023-12-29,2023-12-29,2023-12-29,...,2023-12-29,2023-12-29,2023-12-29,2023-12-29,2023-12-29]]\n", "open: [[9.18,9.17,9.17,9.18,9.17,...,6.6,6.59,6.6,6.6,6.58],[9.19,9.17,9.17,9.17,9.18,...,6.57,6.58,6.58,6.58,6.56],...,[9.11,9.13,9.13,9.14,9.15,...,6.68,6.68,6.68,6.68,6.68],[9.42,9.46,9.46,9.44,9.44,...,6.62,6.63,6.62,6.62,6.62]]\n", "high: [[9.19,9.17,9.18,9.18,9.18,...,6.6,6.6,6.6,6.6,6.58],[9.2,9.18,9.18,9.18,9.18,...,6.58,6.58,6.58,6.58,6.56],...,[9.12,9.16,9.14,9.15,9.17,...,6.69,6.69,6.68,6.68,6.68],[9.48,9.47,9.46,9.44,9.44,...,6.63,6.63,6.62,6.62,6.62]]\n", "low: [[9.15,9.16,9.17,9.17,9.17,...,6.58,6.58,6.6,6.6,6.58],[9.16,9.16,9.16,9.16,9.17,...,6.56,6.56,6.58,6.58,6.56],...,[9.08,9.12,9.13,9.14,9.15,...,6.67,6.68,6.68,6.68,6.68],[9.41,9.44,9.44,9.42,9.42,...,6.62,6.62,6.62,6.62,6.62]]\n", "close: [[9.17,9.17,9.18,9.17,9.17,...,6.58,6.6,6.6,6.6,6.58],[9.17,9.17,9.17,9.17,9.17,...,6.56,6.58,6.58,6.58,6.56],...,[9.12,9.13,9.14,9.15,9.16,...,6.69,6.68,6.68,6.68,6.68],[9.46,9.46,9.44,9.43,9.44,...,6.62,6.62,6.62,6.62,6.62]]\n", "volume: [[1383900,697000,697800,798500,385300,...,48100,55000,1100,0,182200],[974900,244500,325600,240800,257100,...,59100,69300,13000,0,39334],...,[2746954,3551200,575400,935069,688200,...,173300,91000,2100,0,129408],[3824300,1002800,743746,1062400,1420600,...,123600,225500,0,0,519880]]\n", "money: [[12688903,6389101,6403505,7324695,3536008,...,317079,362527,7260,0,1198876],[8949727,2242045,2986332,2208570,2358392,...,388257,455398,85540,0,258031],...,[25002303,32451022,5254699,8550104,6303831,...,1157592,607894,14028,0,864446],[36081692,9481177,7026266,10023791,13398608,...,818580,1493633,0,0,3441606]]\n", "factor: [[126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172,...,15.539982795715332,15.539982795715332,15.539982795715332,15.539982795715332,15.539982795715332],[126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172,...,15.539982795715332,15.539982795715332,15.539982795715332,15.539982795715332,15.539982795715332],...,[126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172,...,15.539982795715332,15.539982795715332,15.539982795715332,15.539982795715332,15.539982795715332],[126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172,126.92925262451172,...,15.539982795715332,15.539982795715332,15.539982795715332,15.539982795715332,15.539982795715332]]"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["table = dataset.to_table()\n", "table\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["如果数据量很大，这会导致内存不够用。因此，很多情况下，我们将使用RecordBatch:\n"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          symbol       frame  open  high   low  close     volume       money  \\\n", "0    000001.XSHE  2023-12-25  9.18  9.19  9.15   9.17  1383900.0  12688903.0   \n", "1    000001.XSHE  2023-12-25  9.17  9.17  9.16   9.17   697000.0   6389101.0   \n", "2    000001.XSHE  2023-12-25  9.17  9.18  9.17   9.18   697800.0   6403505.0   \n", "3    000001.XSHE  2023-12-25  9.18  9.18  9.17   9.17   798500.0   7324695.0   \n", "4    000001.XSHE  2023-12-25  9.17  9.18  9.17   9.17   385300.0   3536008.0   \n", "..           ...         ...   ...   ...   ...    ...        ...         ...   \n", "475  600000.XSHG  2023-12-25  6.60  6.60  6.58   6.58    48100.0    317079.0   \n", "476  600000.XSHG  2023-12-25  6.59  6.60  6.58   6.60    55000.0    362527.0   \n", "477  600000.XSHG  2023-12-25  6.60  6.60  6.60   6.60     1100.0      7260.0   \n", "478  600000.XSHG  2023-12-25  6.60  6.60  6.60   6.60        0.0         0.0   \n", "479  600000.XSHG  2023-12-25  6.58  6.58  6.58   6.58   182200.0   1198876.0   \n", "\n", "         factor  \n", "0    126.929253  \n", "1    126.929253  \n", "2    126.929253  \n", "3    126.929253  \n", "4    126.929253  \n", "..          ...  \n", "475   15.539983  \n", "476   15.539983  \n", "477   15.539983  \n", "478   15.539983  \n", "479   15.539983  \n", "\n", "[480 rows x 9 columns]\n", "          symbol       frame  open  high   low  close    volume      money  \\\n", "0    000001.XSHE  2023-12-26  9.19  9.20  9.16   9.17  974900.0  8949727.0   \n", "1    000001.XSHE  2023-12-26  9.17  9.18  9.16   9.17  244500.0  2242045.0   \n", "2    000001.XSHE  2023-12-26  9.17  9.18  9.16   9.17  325600.0  2986332.0   \n", "3    000001.XSHE  2023-12-26  9.17  9.18  9.16   9.17  240800.0  2208570.0   \n", "4    000001.XSHE  2023-12-26  9.18  9.18  9.17   9.17  257100.0  2358392.0   \n", "..           ...         ...   ...   ...   ...    ...       ...        ...   \n", "475  600000.XSHG  2023-12-26  6.57  6.58  6.56   6.56   59100.0   388257.0   \n", "476  600000.XSHG  2023-12-26  6.58  6.58  6.56   6.58   69300.0   455398.0   \n", "477  600000.XSHG  2023-12-26  6.58  6.58  6.58   6.58   13000.0    85540.0   \n", "478  600000.XSHG  2023-12-26  6.58  6.58  6.58   6.58       0.0        0.0   \n", "479  600000.XSHG  2023-12-26  6.56  6.56  6.56   6.56   39334.0   258031.0   \n", "\n", "         factor  \n", "0    126.929253  \n", "1    126.929253  \n", "2    126.929253  \n", "3    126.929253  \n", "4    126.929253  \n", "..          ...  \n", "475   15.539983  \n", "476   15.539983  \n", "477   15.539983  \n", "478   15.539983  \n", "479   15.539983  \n", "\n", "[480 rows x 9 columns]\n", "          symbol       frame  open  high   low  close    volume      money  \\\n", "0    000001.XSHE  2023-12-27  9.10  9.11  9.09   9.11  932358.0  8487493.0   \n", "1    000001.XSHE  2023-12-27  9.11  9.12  9.10   9.10  744400.0  6777093.0   \n", "2    000001.XSHE  2023-12-27  9.10  9.10  9.09   9.10  397053.0  3611385.0   \n", "3    000001.XSHE  2023-12-27  9.10  9.10  9.09   9.09  242900.0  2209217.0   \n", "4    000001.XSHE  2023-12-27  9.10  9.10  9.08   9.08  604147.0  5490592.0   \n", "..           ...         ...   ...   ...   ...    ...       ...        ...   \n", "475  600000.XSHG  2023-12-27  6.61  6.61  6.60   6.60  115800.0   765091.0   \n", "476  600000.XSHG  2023-12-27  6.61  6.61  6.60   6.61  128600.0   849386.0   \n", "477  600000.XSHG  2023-12-27  6.61  6.61  6.61   6.61       0.0        0.0   \n", "478  600000.XSHG  2023-12-27  6.61  6.61  6.61   6.61       0.0        0.0   \n", "479  600000.XSHG  2023-12-27  6.60  6.60  6.60   6.60  150800.0   995280.0   \n", "\n", "         factor  \n", "0    126.929253  \n", "1    126.929253  \n", "2    126.929253  \n", "3    126.929253  \n", "4    126.929253  \n", "..          ...  \n", "475   15.539983  \n", "476   15.539983  \n", "477   15.539983  \n", "478   15.539983  \n", "479   15.539983  \n", "\n", "[480 rows x 9 columns]\n", "          symbol       frame  open  high   low  close     volume       money  \\\n", "0    000001.XSHE  2023-12-28  9.11  9.12  9.08   9.12  2746954.0  25002303.0   \n", "1    000001.XSHE  2023-12-28  9.13  9.16  9.12   9.13  3551200.0  32451022.0   \n", "2    000001.XSHE  2023-12-28  9.13  9.14  9.13   9.14   575400.0   5254699.0   \n", "3    000001.XSHE  2023-12-28  9.14  9.15  9.14   9.15   935069.0   8550104.0   \n", "4    000001.XSHE  2023-12-28  9.15  9.17  9.15   9.16   688200.0   6303831.0   \n", "..           ...         ...   ...   ...   ...    ...        ...         ...   \n", "475  600000.XSHG  2023-12-28  6.68  6.69  6.67   6.69   173300.0   1157592.0   \n", "476  600000.XSHG  2023-12-28  6.68  6.69  6.68   6.68    91000.0    607894.0   \n", "477  600000.XSHG  2023-12-28  6.68  6.68  6.68   6.68     2100.0     14028.0   \n", "478  600000.XSHG  2023-12-28  6.68  6.68  6.68   6.68        0.0         0.0   \n", "479  600000.XSHG  2023-12-28  6.68  6.68  6.68   6.68   129408.0    864446.0   \n", "\n", "         factor  \n", "0    126.929253  \n", "1    126.929253  \n", "2    126.929253  \n", "3    126.929253  \n", "4    126.929253  \n", "..          ...  \n", "475   15.539983  \n", "476   15.539983  \n", "477   15.539983  \n", "478   15.539983  \n", "479   15.539983  \n", "\n", "[480 rows x 9 columns]\n", "          symbol       frame  open  high   low  close     volume       money  \\\n", "0    000001.XSHE  2023-12-29  9.42  9.48  9.41   9.46  3824300.0  36081692.0   \n", "1    000001.XSHE  2023-12-29  9.46  9.47  9.44   9.46  1002800.0   9481177.0   \n", "2    000001.XSHE  2023-12-29  9.46  9.46  9.44   9.44   743746.0   7026266.0   \n", "3    000001.XSHE  2023-12-29  9.44  9.44  9.42   9.43  1062400.0  10023791.0   \n", "4    000001.XSHE  2023-12-29  9.44  9.44  9.42   9.44  1420600.0  13398608.0   \n", "..           ...         ...   ...   ...   ...    ...        ...         ...   \n", "475  600000.XSHG  2023-12-29  6.62  6.63  6.62   6.62   123600.0    818580.0   \n", "476  600000.XSHG  2023-12-29  6.63  6.63  6.62   6.62   225500.0   1493633.0   \n", "477  600000.XSHG  2023-12-29  6.62  6.62  6.62   6.62        0.0         0.0   \n", "478  600000.XSHG  2023-12-29  6.62  6.62  6.62   6.62        0.0         0.0   \n", "479  600000.XSHG  2023-12-29  6.62  6.62  6.62   6.62   519880.0   3441606.0   \n", "\n", "         factor  \n", "0    126.929253  \n", "1    126.929253  \n", "2    126.929253  \n", "3    126.929253  \n", "4    126.929253  \n", "..          ...  \n", "475   15.539983  \n", "476   15.539983  \n", "477   15.539983  \n", "478   15.539983  \n", "479   15.539983  \n", "\n", "[480 rows x 9 columns]\n"]}], "source": ["for rb in dataset.to_batches():\n", "    print(rb.to_pandas())\n"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>frame</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>money</th>\n", "      <th>factor</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>000001.XSHE</td>\n", "      <td>2023-12-29</td>\n", "      <td>9.42</td>\n", "      <td>9.48</td>\n", "      <td>9.41</td>\n", "      <td>9.46</td>\n", "      <td>3824300.0</td>\n", "      <td>36081692.0</td>\n", "      <td>126.929253</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000001.XSHE</td>\n", "      <td>2023-12-29</td>\n", "      <td>9.46</td>\n", "      <td>9.47</td>\n", "      <td>9.44</td>\n", "      <td>9.46</td>\n", "      <td>1002800.0</td>\n", "      <td>9481177.0</td>\n", "      <td>126.929253</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>000001.XSHE</td>\n", "      <td>2023-12-29</td>\n", "      <td>9.46</td>\n", "      <td>9.46</td>\n", "      <td>9.44</td>\n", "      <td>9.44</td>\n", "      <td>743746.0</td>\n", "      <td>7026266.0</td>\n", "      <td>126.929253</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>000001.XSHE</td>\n", "      <td>2023-12-29</td>\n", "      <td>9.44</td>\n", "      <td>9.44</td>\n", "      <td>9.42</td>\n", "      <td>9.43</td>\n", "      <td>1062400.0</td>\n", "      <td>10023791.0</td>\n", "      <td>126.929253</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000001.XSHE</td>\n", "      <td>2023-12-29</td>\n", "      <td>9.44</td>\n", "      <td>9.44</td>\n", "      <td>9.42</td>\n", "      <td>9.44</td>\n", "      <td>1420600.0</td>\n", "      <td>13398608.0</td>\n", "      <td>126.929253</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>475</th>\n", "      <td>600000.XSHG</td>\n", "      <td>2023-12-29</td>\n", "      <td>6.62</td>\n", "      <td>6.63</td>\n", "      <td>6.62</td>\n", "      <td>6.62</td>\n", "      <td>123600.0</td>\n", "      <td>818580.0</td>\n", "      <td>15.539983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>476</th>\n", "      <td>600000.XSHG</td>\n", "      <td>2023-12-29</td>\n", "      <td>6.63</td>\n", "      <td>6.63</td>\n", "      <td>6.62</td>\n", "      <td>6.62</td>\n", "      <td>225500.0</td>\n", "      <td>1493633.0</td>\n", "      <td>15.539983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>477</th>\n", "      <td>600000.XSHG</td>\n", "      <td>2023-12-29</td>\n", "      <td>6.62</td>\n", "      <td>6.62</td>\n", "      <td>6.62</td>\n", "      <td>6.62</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>15.539983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>478</th>\n", "      <td>600000.XSHG</td>\n", "      <td>2023-12-29</td>\n", "      <td>6.62</td>\n", "      <td>6.62</td>\n", "      <td>6.62</td>\n", "      <td>6.62</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>15.539983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>479</th>\n", "      <td>600000.XSHG</td>\n", "      <td>2023-12-29</td>\n", "      <td>6.62</td>\n", "      <td>6.62</td>\n", "      <td>6.62</td>\n", "      <td>6.62</td>\n", "      <td>519880.0</td>\n", "      <td>3441606.0</td>\n", "      <td>15.539983</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>480 rows × 9 columns</p>\n", "</div>"], "text/plain": ["          symbol       frame  open  high   low  close     volume       money  \\\n", "0    000001.XSHE  2023-12-29  9.42  9.48  9.41   9.46  3824300.0  36081692.0   \n", "1    000001.XSHE  2023-12-29  9.46  9.47  9.44   9.46  1002800.0   9481177.0   \n", "2    000001.XSHE  2023-12-29  9.46  9.46  9.44   9.44   743746.0   7026266.0   \n", "3    000001.XSHE  2023-12-29  9.44  9.44  9.42   9.43  1062400.0  10023791.0   \n", "4    000001.XSHE  2023-12-29  9.44  9.44  9.42   9.44  1420600.0  13398608.0   \n", "..           ...         ...   ...   ...   ...    ...        ...         ...   \n", "475  600000.XSHG  2023-12-29  6.62  6.63  6.62   6.62   123600.0    818580.0   \n", "476  600000.XSHG  2023-12-29  6.63  6.63  6.62   6.62   225500.0   1493633.0   \n", "477  600000.XSHG  2023-12-29  6.62  6.62  6.62   6.62        0.0         0.0   \n", "478  600000.XSHG  2023-12-29  6.62  6.62  6.62   6.62        0.0         0.0   \n", "479  600000.XSHG  2023-12-29  6.62  6.62  6.62   6.62   519880.0   3441606.0   \n", "\n", "         factor  \n", "0    126.929253  \n", "1    126.929253  \n", "2    126.929253  \n", "3    126.929253  \n", "4    126.929253  \n", "..          ...  \n", "475   15.539983  \n", "476   15.539983  \n", "477   15.539983  \n", "478   15.539983  \n", "479   15.539983  \n", "\n", "[480 rows x 9 columns]"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["import pyarrow.compute as pc\n", "\n", "filter = (pc.field(\"frame\") > pc.scalar(datetime.datetime(2023, 12, 28, 15)))\n", "dataset.filter(filter).to_table().to_pandas()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "coursea", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 2}