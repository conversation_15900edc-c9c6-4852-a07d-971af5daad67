{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mRunning cells with 'cheese' requires the ipykernel package.\n", "\u001b[1;31mRun the following command to install 'ipykernel' into the Python environment. \n", "\u001b[1;31mCommand: 'conda install -n cheese ipykernel --update-deps --force-reinstall'"]}], "source": ["import akshare as ak\n", "from coursea import *\n", "await init()\n", "from omicron.extension import find_runs\n", "import pandas as pd\n", "\n", "import arrow\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20200303\n", "20240416\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>cnr</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-03-05</td>\n", "      <td>2020-03-09</td>\n", "      <td>-0.042150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-03-10</td>\n", "      <td>2020-03-18</td>\n", "      <td>-0.104779</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-03-25</td>\n", "      <td>2020-03-30</td>\n", "      <td>-0.040948</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-04-09</td>\n", "      <td>2020-04-13</td>\n", "      <td>-0.036343</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2020-04-22</td>\n", "      <td>2020-04-29</td>\n", "      <td>-0.047872</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>112</th>\n", "      <td>2024-01-11</td>\n", "      <td>2024-01-22</td>\n", "      <td>-0.108763</td>\n", "    </tr>\n", "    <tr>\n", "      <th>113</th>\n", "      <td>2024-01-25</td>\n", "      <td>2024-02-05</td>\n", "      <td>-0.192455</td>\n", "    </tr>\n", "    <tr>\n", "      <th>114</th>\n", "      <td>2024-03-20</td>\n", "      <td>2024-03-27</td>\n", "      <td>-0.069431</td>\n", "    </tr>\n", "    <tr>\n", "      <th>115</th>\n", "      <td>2024-04-01</td>\n", "      <td>2024-04-08</td>\n", "      <td>-0.028629</td>\n", "    </tr>\n", "    <tr>\n", "      <th>116</th>\n", "      <td>2024-04-11</td>\n", "      <td>2024-04-16</td>\n", "      <td>-0.058228</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>117 rows × 3 columns</p>\n", "</div>"], "text/plain": ["          start         end       cnr\n", "0    2020-03-05  2020-03-09 -0.042150\n", "1    2020-03-10  2020-03-18 -0.104779\n", "2    2020-03-25  2020-03-30 -0.040948\n", "3    2020-04-09  2020-04-13 -0.036343\n", "4    2020-04-22  2020-04-29 -0.047872\n", "..          ...         ...       ...\n", "112  2024-01-11  2024-01-22 -0.108763\n", "113  2024-01-25  2024-02-05 -0.192455\n", "114  2024-03-20  2024-03-27 -0.069431\n", "115  2024-04-01  2024-04-08 -0.028629\n", "116  2024-04-11  2024-04-16 -0.058228\n", "\n", "[117 rows x 3 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["now = datetime.datetime.now().date()\n", "start = tf.day_shift(now, -1000)\n", "\n", "start_date = start.strftime(\"%Y%m%d\")\n", "end_date = now.strftime(\"%Y%m%d\")\n", "\n", "print(start_date)\n", "print(end_date)\n", "# 通过akshare获取中证1000日线数据（近1000天）\n", "bars = ak.index_zh_a_hist(symbol=\"000852\", start_date=start_date, end_date=end_date)\n", "\n", "bars.rename(columns = {\n", "    \"日期\": \"frame\",\n", "    \"开盘\": \"open\",\n", "    \"最高\": \"high\",\n", "    \"最低\": \"low\",\n", "    \"收盘\": \"close\"\n", "}, inplace=True)\n", "\n", "bars[\"frame\"] = bars.frame.apply(lambda x: arrow.get(x).date())\n", "\n", "close = bars.close\n", "returns = close.pct_change()\n", "v, s, l = find_runs(returns <= 0)\n", "\n", "cum_neg_returns = []\n", "for vi, si, li in zip(v, s, l):\n", "    if vi and li > 1:\n", "        cum_neg_returns.append((bars.frame[si-1], bars.frame[si + li - 1], close[si + li - 1]/close[si-1] - 1))\n", "        \n", "r = pd.DataFrame(cum_neg_returns, columns=[\"start\", \"end\", \"cnr\"])\n", "r"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.08547008547008547"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# 3/27这一次下跌0.0694，此后继续下跌的概率\n", "\n", "p_decline = r.cnr.le(-0.06943).mean()\n", "p_decline"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.1452991452991453"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# 4/16这一次下跌0.058，此后继续下跌的概率\n", "\n", "p_decline = r.cnr.le(-0.058228).mean()\n", "p_decline"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["下跌幅度-5.0%, 反弹概率: 82.9%\n", "下跌幅度-5.6%, 反弹概率: 83.8%\n", "下跌幅度-6.1%, 反弹概率: 88.0%\n", "下跌幅度-6.7%, 反弹概率: 90.6%\n", "下跌幅度-7.2%, 反弹概率: 92.3%\n", "下跌幅度-7.8%, 反弹概率: 94.9%\n", "下跌幅度-8.3%, 反弹概率: 94.9%\n", "下跌幅度-8.9%, 反弹概率: 96.6%\n", "下跌幅度-9.4%, 反弹概率: 96.6%\n", "下跌幅度-10.0%, 反弹概率: 96.6%\n"]}], "source": ["import numpy as np\n", "for loss in np.linspace(-0.05, -0.1, 10):\n", "    print(f\"下跌幅度{loss:.1%}, 反弹概率: {1- r.cnr.le(loss).mean():.1%}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "coursea", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}