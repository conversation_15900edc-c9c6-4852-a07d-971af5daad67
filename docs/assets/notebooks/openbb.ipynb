{"cells": [{"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["from openbb import obb\n", "obb.account.login(pat=\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.52KrCbNqzry8lKQVeUYdeHyTeifsjRqByeUWpcQmeNk\")"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>cik</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AAPL</td>\n", "      <td>Apple Inc.</td>\n", "      <td>320193</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>APLE</td>\n", "      <td>Apple Hospitality REIT, Inc.</td>\n", "      <td>1418121</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MLP</td>\n", "      <td>MAUI LAND &amp; PINEAPPLE CO INC</td>\n", "      <td>63330</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  symbol                          name      cik\n", "0   AAPL                    Apple Inc.   320193\n", "1   APLE  Apple Hospitality REIT, Inc.  1418121\n", "2    MLP  MAUI LAND & PINEAPPLE CO INC    63330"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["from openbb import obb\n", "\n", "# 按名字查找股票代码等基本信息\n", "obb.equity.search(\"APPLE\", provider=\"sec\").to_df().head(3)"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>currency</th>\n", "      <th>stock_exchange</th>\n", "      <th>exchange_short_name</th>\n", "      <th>symbol</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>S&amp;P/TSX Capped Industrials Index</td>\n", "      <td>CAD</td>\n", "      <td>Toronto Stock Exchange</td>\n", "      <td>INDEX</td>\n", "      <td>^TTIN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>S&amp;P/TSX Capped Consumer Discretionary</td>\n", "      <td>CAD</td>\n", "      <td>Toronto Stock Exchange</td>\n", "      <td>INDEX</td>\n", "      <td>^TTCD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Solactive Equal Weight Canada Oil &amp; Gas Index</td>\n", "      <td>EUR</td>\n", "      <td>Stuttgart</td>\n", "      <td>INDEX</td>\n", "      <td>DE000SLA30S3.SG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>OMX Stockholm 30 Index</td>\n", "      <td>SEK</td>\n", "      <td>Stockholm Stock Exchange</td>\n", "      <td>INDEX</td>\n", "      <td>^OMXS30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Wilshire 5000 Total Market Index</td>\n", "      <td>USD</td>\n", "      <td>NYSE</td>\n", "      <td>INDEX</td>\n", "      <td>^W5000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>All Ordinaries</td>\n", "      <td>AUD</td>\n", "      <td>ASX</td>\n", "      <td>INDEX</td>\n", "      <td>^AORD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>Alerian MLP Index</td>\n", "      <td>USD</td>\n", "      <td>NYSE</td>\n", "      <td>INDEX</td>\n", "      <td>^AMZ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>AEX Index</td>\n", "      <td>EUR</td>\n", "      <td>Amsterdam</td>\n", "      <td>INDEX</td>\n", "      <td>^AEX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>13 Week Treasury Bill</td>\n", "      <td>USD</td>\n", "      <td>ICE Futures</td>\n", "      <td>INDEX</td>\n", "      <td>^IRX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>191</th>\n", "      <td>1/100 Dow Jones Industrial Average</td>\n", "      <td>USD</td>\n", "      <td>Chicago Options</td>\n", "      <td>INDEX</td>\n", "      <td>^DJX</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>192 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                                              name currency  \\\n", "0                 S&P/TSX Capped Industrials Index      CAD   \n", "1            S&P/TSX Capped Consumer Discretionary      CAD   \n", "2    Solactive Equal Weight Canada Oil & Gas Index      EUR   \n", "3                           OMX Stockholm 30 Index      SEK   \n", "4                 Wilshire 5000 Total Market Index      USD   \n", "..                                             ...      ...   \n", "187                                 All Ordinaries      AUD   \n", "188                              Alerian MLP Index      USD   \n", "189                                      AEX Index      EUR   \n", "190                          13 Week Treasury Bill      USD   \n", "191             1/100 Dow Jones Industrial Average      USD   \n", "\n", "               stock_exchange exchange_short_name           symbol  \n", "0      Toronto Stock Exchange               INDEX            ^TTIN  \n", "1      Toronto Stock Exchange               INDEX            ^TTCD  \n", "2                   Stuttgart               INDEX  DE000SLA30S3.SG  \n", "3    Stockholm Stock Exchange               INDEX          ^OMXS30  \n", "4                        NYSE               INDEX           ^W5000  \n", "..                        ...                 ...              ...  \n", "187                       ASX               INDEX            ^AORD  \n", "188                      NYSE               INDEX             ^AMZ  \n", "189                 Amsterdam               INDEX             ^AEX  \n", "190               ICE Futures               INDEX             ^IRX  \n", "191           Chicago Options               INDEX             ^DJX  \n", "\n", "[192 rows x 5 columns]"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取指数列表\n", "indices = obb.index.available(provider=\"fmp\").to_df()\n", "indices"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>vwap</th>\n", "      <th>adj_close</th>\n", "      <th>unadjusted_volume</th>\n", "      <th>change</th>\n", "      <th>change_percent</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-08-07</th>\n", "      <td>206.90</td>\n", "      <td>213.64</td>\n", "      <td>206.39</td>\n", "      <td>209.820</td>\n", "      <td>63516417</td>\n", "      <td>209.1875</td>\n", "      <td>209.580</td>\n", "      <td>63516417.0</td>\n", "      <td>2.92</td>\n", "      <td>0.014100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-08</th>\n", "      <td>213.11</td>\n", "      <td>214.20</td>\n", "      <td>208.83</td>\n", "      <td>213.310</td>\n", "      <td>47161149</td>\n", "      <td>212.3625</td>\n", "      <td>213.060</td>\n", "      <td>47161149.0</td>\n", "      <td>0.20</td>\n", "      <td>0.000938</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-09</th>\n", "      <td>212.10</td>\n", "      <td>216.78</td>\n", "      <td>211.97</td>\n", "      <td>216.240</td>\n", "      <td>42201646</td>\n", "      <td>214.2725</td>\n", "      <td>216.240</td>\n", "      <td>42201646.0</td>\n", "      <td>4.14</td>\n", "      <td>0.019500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-12</th>\n", "      <td>216.07</td>\n", "      <td>219.51</td>\n", "      <td>215.60</td>\n", "      <td>217.530</td>\n", "      <td>37575198</td>\n", "      <td>217.1775</td>\n", "      <td>217.530</td>\n", "      <td>37575198.0</td>\n", "      <td>1.46</td>\n", "      <td>0.006757</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-13</th>\n", "      <td>219.01</td>\n", "      <td>221.47</td>\n", "      <td>219.01</td>\n", "      <td>220.459</td>\n", "      <td>5944328</td>\n", "      <td>220.3100</td>\n", "      <td>220.459</td>\n", "      <td>5944328.0</td>\n", "      <td>1.45</td>\n", "      <td>0.006616</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              open    high     low    close    volume      vwap  adj_close  \\\n", "date                                                                         \n", "2024-08-07  206.90  213.64  206.39  209.820  63516417  209.1875    209.580   \n", "2024-08-08  213.11  214.20  208.83  213.310  47161149  212.3625    213.060   \n", "2024-08-09  212.10  216.78  211.97  216.240  42201646  214.2725    216.240   \n", "2024-08-12  216.07  219.51  215.60  217.530  37575198  217.1775    217.530   \n", "2024-08-13  219.01  221.47  219.01  220.459   5944328  220.3100    220.459   \n", "\n", "            unadjusted_volume  change  change_percent  \n", "date                                                   \n", "2024-08-07         63516417.0    2.92        0.014100  \n", "2024-08-08         47161149.0    0.20        0.000938  \n", "2024-08-09         42201646.0    4.14        0.019500  \n", "2024-08-12         37575198.0    1.46        0.006757  \n", "2024-08-13          5944328.0    1.45        0.006616  "]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取股票行情报价\n", "obb.equity.price.historical(\"AAPL\", provider=\"fmp\").to_df().tail()"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>period_ending</th>\n", "      <th>fiscal_period</th>\n", "      <th>fiscal_year</th>\n", "      <th>filing_date</th>\n", "      <th>accepted_date</th>\n", "      <th>reported_currency</th>\n", "      <th>net_income</th>\n", "      <th>depreciation_and_amortization</th>\n", "      <th>stock_based_compensation</th>\n", "      <th>change_in_working_capital</th>\n", "      <th>...</th>\n", "      <th>cash_at_beginning_of_period</th>\n", "      <th>cash_at_end_of_period</th>\n", "      <th>operating_cash_flow</th>\n", "      <th>capital_expenditure</th>\n", "      <th>free_cash_flow</th>\n", "      <th>link</th>\n", "      <th>final_link</th>\n", "      <th>deferred_income_tax</th>\n", "      <th>acquisitions</th>\n", "      <th>issuance_of_common_equity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-09-30</td>\n", "      <td>FY</td>\n", "      <td>2023</td>\n", "      <td>2023-11-03</td>\n", "      <td>2023-11-02 18:08:27</td>\n", "      <td>USD</td>\n", "      <td>9.699500e+10</td>\n", "      <td>1.151900e+10</td>\n", "      <td>1.083300e+10</td>\n", "      <td>-6.577000e+09</td>\n", "      <td>...</td>\n", "      <td>2.497700e+10</td>\n", "      <td>3.073700e+10</td>\n", "      <td>1.105430e+11</td>\n", "      <td>-1.095900e+10</td>\n", "      <td>9.958400e+10</td>\n", "      <td>https://www.sec.gov/Archives/edgar/data/320193...</td>\n", "      <td>https://www.sec.gov/Archives/edgar/data/320193...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-09-24</td>\n", "      <td>FY</td>\n", "      <td>2022</td>\n", "      <td>2022-10-28</td>\n", "      <td>2022-10-27 18:01:14</td>\n", "      <td>USD</td>\n", "      <td>9.980300e+10</td>\n", "      <td>1.110400e+10</td>\n", "      <td>9.038000e+09</td>\n", "      <td>1.200000e+09</td>\n", "      <td>...</td>\n", "      <td>3.592900e+10</td>\n", "      <td>2.497700e+10</td>\n", "      <td>1.221510e+11</td>\n", "      <td>-1.070800e+10</td>\n", "      <td>1.114430e+11</td>\n", "      <td>https://www.sec.gov/Archives/edgar/data/320193...</td>\n", "      <td>https://www.sec.gov/Archives/edgar/data/320193...</td>\n", "      <td>8.950000e+08</td>\n", "      <td>-3.060000e+08</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2021-09-25</td>\n", "      <td>FY</td>\n", "      <td>2021</td>\n", "      <td>2021-10-29</td>\n", "      <td>2021-10-28 18:04:28</td>\n", "      <td>USD</td>\n", "      <td>9.468000e+10</td>\n", "      <td>1.128400e+10</td>\n", "      <td>7.906000e+09</td>\n", "      <td>-4.911000e+09</td>\n", "      <td>...</td>\n", "      <td>3.978900e+10</td>\n", "      <td>3.592900e+10</td>\n", "      <td>1.040380e+11</td>\n", "      <td>-1.108500e+10</td>\n", "      <td>9.295300e+10</td>\n", "      <td>https://www.sec.gov/Archives/edgar/data/320193...</td>\n", "      <td>https://www.sec.gov/Archives/edgar/data/320193...</td>\n", "      <td>-4.774000e+09</td>\n", "      <td>-3.300000e+07</td>\n", "      <td>1.105000e+09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-09-26</td>\n", "      <td>FY</td>\n", "      <td>2020</td>\n", "      <td>2020-10-30</td>\n", "      <td>2020-10-29 18:06:25</td>\n", "      <td>USD</td>\n", "      <td>5.741100e+10</td>\n", "      <td>1.105600e+10</td>\n", "      <td>6.829000e+09</td>\n", "      <td>5.690000e+09</td>\n", "      <td>...</td>\n", "      <td>5.022400e+10</td>\n", "      <td>3.978900e+10</td>\n", "      <td>8.067400e+10</td>\n", "      <td>-7.309000e+09</td>\n", "      <td>7.336500e+10</td>\n", "      <td>https://www.sec.gov/Archives/edgar/data/320193...</td>\n", "      <td>https://www.sec.gov/Archives/edgar/data/320193...</td>\n", "      <td>-2.150000e+08</td>\n", "      <td>-1.524000e+09</td>\n", "      <td>8.800000e+08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2019-09-28</td>\n", "      <td>FY</td>\n", "      <td>2019</td>\n", "      <td>2019-10-31</td>\n", "      <td>2019-10-30 18:12:36</td>\n", "      <td>USD</td>\n", "      <td>5.525600e+10</td>\n", "      <td>1.254700e+10</td>\n", "      <td>6.068000e+09</td>\n", "      <td>-3.488000e+09</td>\n", "      <td>...</td>\n", "      <td>2.591300e+10</td>\n", "      <td>5.022400e+10</td>\n", "      <td>6.939100e+10</td>\n", "      <td>-1.049500e+10</td>\n", "      <td>5.889600e+10</td>\n", "      <td>https://www.sec.gov/Archives/edgar/data/320193...</td>\n", "      <td>https://www.sec.gov/Archives/edgar/data/320193...</td>\n", "      <td>-3.400000e+08</td>\n", "      <td>-6.240000e+08</td>\n", "      <td>7.810000e+08</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 37 columns</p>\n", "</div>"], "text/plain": ["  period_ending fiscal_period  fiscal_year filing_date       accepted_date  \\\n", "0    2023-09-30            FY         2023  2023-11-03 2023-11-02 18:08:27   \n", "1    2022-09-24            FY         2022  2022-10-28 2022-10-27 18:01:14   \n", "2    2021-09-25            FY         2021  2021-10-29 2021-10-28 18:04:28   \n", "3    2020-09-26            FY         2020  2020-10-30 2020-10-29 18:06:25   \n", "4    2019-09-28            FY         2019  2019-10-31 2019-10-30 18:12:36   \n", "\n", "  reported_currency    net_income  depreciation_and_amortization  \\\n", "0               USD  9.699500e+10                   1.151900e+10   \n", "1               USD  9.980300e+10                   1.110400e+10   \n", "2               USD  9.468000e+10                   1.128400e+10   \n", "3               USD  5.741100e+10                   1.105600e+10   \n", "4               USD  5.525600e+10                   1.254700e+10   \n", "\n", "   stock_based_compensation  change_in_working_capital  ...  \\\n", "0              1.083300e+10              -6.577000e+09  ...   \n", "1              9.038000e+09               1.200000e+09  ...   \n", "2              7.906000e+09              -4.911000e+09  ...   \n", "3              6.829000e+09               5.690000e+09  ...   \n", "4              6.068000e+09              -3.488000e+09  ...   \n", "\n", "   cash_at_beginning_of_period  cash_at_end_of_period  operating_cash_flow  \\\n", "0                 2.497700e+10           3.073700e+10         1.105430e+11   \n", "1                 3.592900e+10           2.497700e+10         1.221510e+11   \n", "2                 3.978900e+10           3.592900e+10         1.040380e+11   \n", "3                 5.022400e+10           3.978900e+10         8.067400e+10   \n", "4                 2.591300e+10           5.022400e+10         6.939100e+10   \n", "\n", "   capital_expenditure  free_cash_flow  \\\n", "0        -1.095900e+10    9.958400e+10   \n", "1        -1.070800e+10    1.114430e+11   \n", "2        -1.108500e+10    9.295300e+10   \n", "3        -7.309000e+09    7.336500e+10   \n", "4        -1.049500e+10    5.889600e+10   \n", "\n", "                                                link  \\\n", "0  https://www.sec.gov/Archives/edgar/data/320193...   \n", "1  https://www.sec.gov/Archives/edgar/data/320193...   \n", "2  https://www.sec.gov/Archives/edgar/data/320193...   \n", "3  https://www.sec.gov/Archives/edgar/data/320193...   \n", "4  https://www.sec.gov/Archives/edgar/data/320193...   \n", "\n", "                                          final_link  deferred_income_tax  \\\n", "0  https://www.sec.gov/Archives/edgar/data/320193...                  NaN   \n", "1  https://www.sec.gov/Archives/edgar/data/320193...         8.950000e+08   \n", "2  https://www.sec.gov/Archives/edgar/data/320193...        -4.774000e+09   \n", "3  https://www.sec.gov/Archives/edgar/data/320193...        -2.150000e+08   \n", "4  https://www.sec.gov/Archives/edgar/data/320193...        -3.400000e+08   \n", "\n", "   acquisitions  issuance_of_common_equity  \n", "0           NaN                        NaN  \n", "1 -3.060000e+08                        NaN  \n", "2 -3.300000e+07               1.105000e+09  \n", "3 -1.524000e+09               8.800000e+08  \n", "4 -6.240000e+08               7.810000e+08  \n", "\n", "[5 rows x 37 columns]"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["# 基本面研究\n", "obb.equity.fundamental.cash(\"AAPL\", provider='fmp').to_df().tail()"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>asset_type</th>\n", "      <th>name</th>\n", "      <th>bid</th>\n", "      <th>bid_size</th>\n", "      <th>ask</th>\n", "      <th>ask_size</th>\n", "      <th>last_price</th>\n", "      <th>last_tick</th>\n", "      <th>last_timestamp</th>\n", "      <th>...</th>\n", "      <th>iv30_annual_low</th>\n", "      <th>hv30_annual_low</th>\n", "      <th>iv60_annual_high</th>\n", "      <th>hv60_annual_high</th>\n", "      <th>iv60_annual_low</th>\n", "      <th>hv60_annual_low</th>\n", "      <th>iv90_annual_high</th>\n", "      <th>hv90_annual_high</th>\n", "      <th>iv90_annual_low</th>\n", "      <th>hv90_annual_low</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AAPL</td>\n", "      <td>stock</td>\n", "      <td>APPLE INC COM</td>\n", "      <td>218.81</td>\n", "      <td>4</td>\n", "      <td>219.12</td>\n", "      <td>10</td>\n", "      <td>218.75</td>\n", "      <td>up</td>\n", "      <td>2024-08-12 15:59:59</td>\n", "      <td>...</td>\n", "      <td>0.15278</td>\n", "      <td>0.124114</td>\n", "      <td>0.33212</td>\n", "      <td>0.28291</td>\n", "      <td>0.16292</td>\n", "      <td>0.152139</td>\n", "      <td>0.32144</td>\n", "      <td>0.274037</td>\n", "      <td>0.18538</td>\n", "      <td>0.170369</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 31 columns</p>\n", "</div>"], "text/plain": ["  symbol asset_type           name     bid  bid_size     ask  ask_size  \\\n", "0   AAPL      stock  APPLE INC COM  218.81         4  219.12        10   \n", "\n", "   last_price last_tick      last_timestamp  ...  iv30_annual_low  \\\n", "0      218.75        up 2024-08-12 15:59:59  ...          0.15278   \n", "\n", "   hv30_annual_low  iv60_annual_high  hv60_annual_high  iv60_annual_low  \\\n", "0         0.124114           0.33212           0.28291          0.16292   \n", "\n", "   hv60_annual_low  iv90_annual_high  hv90_annual_high  iv90_annual_low  \\\n", "0         0.152139           0.32144          0.274037          0.18538   \n", "\n", "   hv90_annual_low  \n", "0         0.170369  \n", "\n", "[1 rows x 31 columns]"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["# 股票实时价格，注意按有Nasdaq等交易所要求，实时价格有15分钟延时\n", "quote_data = obb.equity.price.quote(symbol=\"AAPL\")\n", "quote_data.to_df().tail()"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>sector</th>\n", "      <th>sub_sector</th>\n", "      <th>headquarter</th>\n", "      <th>date_first_added</th>\n", "      <th>cik</th>\n", "      <th>founded</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AMZN</td>\n", "      <td>Amazon.com, Inc.amazon.com</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Seattle, WA</td>\n", "      <td>2024-02-26</td>\n", "      <td>1018724</td>\n", "      <td>1994-07-05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AMGN</td>\n", "      <td>Amgen Inc.</td>\n", "      <td>Healthcare</td>\n", "      <td>Healthcare</td>\n", "      <td>Thousand Oaks, CA</td>\n", "      <td>2020-08-31</td>\n", "      <td>318154</td>\n", "      <td>1980-04-08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CRM</td>\n", "      <td>Salesforce, Inc.</td>\n", "      <td>Technology</td>\n", "      <td>Technology</td>\n", "      <td>San Francisco, CA</td>\n", "      <td>2020-08-31</td>\n", "      <td>1108524</td>\n", "      <td>1999-02-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>HON</td>\n", "      <td>Honeywell International Inc.</td>\n", "      <td>Industrials</td>\n", "      <td>Industrials</td>\n", "      <td>Charlotte, NC</td>\n", "      <td>2020-08-31</td>\n", "      <td>773840</td>\n", "      <td>1906-01-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>GS</td>\n", "      <td>The Goldman Sachs Group, Inc.</td>\n", "      <td>Financial Services</td>\n", "      <td>Financial Services</td>\n", "      <td>New York, NY</td>\n", "      <td>2019-04-02</td>\n", "      <td>886982</td>\n", "      <td>1869-01-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>AAPL</td>\n", "      <td>Apple Inc.</td>\n", "      <td>Technology</td>\n", "      <td>Technology</td>\n", "      <td>Cupertino, CA</td>\n", "      <td>2015-03-19</td>\n", "      <td>320193</td>\n", "      <td>1976-04-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>NKE</td>\n", "      <td>Nike, Inc.</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Beaverton, OR</td>\n", "      <td>2013-09-20</td>\n", "      <td>320187</td>\n", "      <td>1964-01-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>V</td>\n", "      <td>Visa Inc.</td>\n", "      <td>Financial Services</td>\n", "      <td>Financial Services</td>\n", "      <td>San Francisco, CA</td>\n", "      <td>2013-09-20</td>\n", "      <td>1403161</td>\n", "      <td>1958-09-18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>UNH</td>\n", "      <td>UnitedHealth Group Incorporated</td>\n", "      <td>Healthcare</td>\n", "      <td>Healthcare</td>\n", "      <td>Minnetonka, MN</td>\n", "      <td>2012-09-24</td>\n", "      <td>731766</td>\n", "      <td>1977-01-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>CSCO</td>\n", "      <td>Cisco Systems, Inc.</td>\n", "      <td>Technology</td>\n", "      <td>Technology</td>\n", "      <td>San Jose, CA</td>\n", "      <td>2009-06-08</td>\n", "      <td>858877</td>\n", "      <td>1984-12-10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>TRV</td>\n", "      <td>The Travelers Companies, Inc.</td>\n", "      <td>Financial Services</td>\n", "      <td>Financial Services</td>\n", "      <td>New York, NY</td>\n", "      <td>2009-06-08</td>\n", "      <td>86312</td>\n", "      <td>1853-01-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>CVX</td>\n", "      <td>Chevron Corporation</td>\n", "      <td>Energy</td>\n", "      <td>Energy</td>\n", "      <td>San <PERSON>, CA</td>\n", "      <td>2008-02-19</td>\n", "      <td>93410</td>\n", "      <td>1879-09-10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>VZ</td>\n", "      <td>Verizon Communications Inc.</td>\n", "      <td>Communication Services</td>\n", "      <td>Communication Services</td>\n", "      <td>New York, NY</td>\n", "      <td>2004-04-08</td>\n", "      <td>732712</td>\n", "      <td>1983-10-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>HD</td>\n", "      <td>The Home Depot, Inc.</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Atlanta, GA</td>\n", "      <td>1999-11-01</td>\n", "      <td>354950</td>\n", "      <td>1978-02-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>INTC</td>\n", "      <td>Intel Corporation</td>\n", "      <td>Technology</td>\n", "      <td>Technology</td>\n", "      <td>Santa Clara, CA</td>\n", "      <td>1999-11-01</td>\n", "      <td>50863</td>\n", "      <td>1968-07-18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>MSFT</td>\n", "      <td>Microsoft Corporation</td>\n", "      <td>Technology</td>\n", "      <td>Technology</td>\n", "      <td>Redmond, WA</td>\n", "      <td>1999-11-01</td>\n", "      <td>789019</td>\n", "      <td>1975-04-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>JNJ</td>\n", "      <td>Johnson &amp; Johnson</td>\n", "      <td>Healthcare</td>\n", "      <td>Healthcare</td>\n", "      <td>New Brunswick, NJ</td>\n", "      <td>1997-03-17</td>\n", "      <td>200406</td>\n", "      <td>1886-01-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>WMT</td>\n", "      <td>Walmart Inc.</td>\n", "      <td>Consumer Defensive</td>\n", "      <td>Consumer Defensive</td>\n", "      <td>Bentonville, AR</td>\n", "      <td>1997-03-17</td>\n", "      <td>104169</td>\n", "      <td>1962-07-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>CAT</td>\n", "      <td>Caterpillar Inc.</td>\n", "      <td>Industrials</td>\n", "      <td>Industrials</td>\n", "      <td>Deerfield, IL</td>\n", "      <td>1991-05-06</td>\n", "      <td>18230</td>\n", "      <td>1925-04-15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>DIS</td>\n", "      <td>The Walt Disney Company</td>\n", "      <td>Communication Services</td>\n", "      <td>Communication Services</td>\n", "      <td>Burbank, CA</td>\n", "      <td>1991-05-06</td>\n", "      <td>1744489</td>\n", "      <td>1923-10-16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>DOW</td>\n", "      <td>Dow Inc.</td>\n", "      <td>Basic Materials</td>\n", "      <td>Basic Materials</td>\n", "      <td>Midland, MI</td>\n", "      <td>1991-05-06</td>\n", "      <td>1751788</td>\n", "      <td>2019-04-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>JPM</td>\n", "      <td>JPMorgan Chase &amp; Co.</td>\n", "      <td>Financial Services</td>\n", "      <td>Financial Services</td>\n", "      <td>New York, NY</td>\n", "      <td>1991-05-06</td>\n", "      <td>19617</td>\n", "      <td>2000-12-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>BA</td>\n", "      <td>The Boeing Company</td>\n", "      <td>Industrials</td>\n", "      <td>Industrials</td>\n", "      <td>Arlington, VA</td>\n", "      <td>1987-03-12</td>\n", "      <td>12927</td>\n", "      <td>1916-07-15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>KO</td>\n", "      <td>The Coca-Cola Company</td>\n", "      <td>Consumer Defensive</td>\n", "      <td>Consumer Defensive</td>\n", "      <td>Atlanta, GA</td>\n", "      <td>1987-03-12</td>\n", "      <td>21344</td>\n", "      <td>1892-01-29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>MCD</td>\n", "      <td>McDonald's Corporation</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Consumer Cyclical</td>\n", "      <td>Chicago, IL</td>\n", "      <td>1985-10-30</td>\n", "      <td>63908</td>\n", "      <td>1940-05-15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>AXP</td>\n", "      <td>American Express Company</td>\n", "      <td>Financial Services</td>\n", "      <td>Financial Services</td>\n", "      <td>New York, NY</td>\n", "      <td>1982-08-30</td>\n", "      <td>4962</td>\n", "      <td>1850-03-18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>IBM</td>\n", "      <td>International Business Machines Corporation</td>\n", "      <td>Technology</td>\n", "      <td>Technology</td>\n", "      <td>Armonk, NY</td>\n", "      <td>1979-06-29</td>\n", "      <td>51143</td>\n", "      <td>1924-06-16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>MRK</td>\n", "      <td>Merck &amp; Co., Inc.</td>\n", "      <td>Healthcare</td>\n", "      <td>Healthcare</td>\n", "      <td>Kenilworth, NJ</td>\n", "      <td>1979-06-29</td>\n", "      <td>310158</td>\n", "      <td>1891-01-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>MMM</td>\n", "      <td>3M Company</td>\n", "      <td>Industrials</td>\n", "      <td>Industrials</td>\n", "      <td><PERSON>, MN</td>\n", "      <td>1976-08-09</td>\n", "      <td>66740</td>\n", "      <td>1902-06-13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>PG</td>\n", "      <td>The Procter &amp; Gamble Company</td>\n", "      <td>Consumer Defensive</td>\n", "      <td>Consumer Defensive</td>\n", "      <td>Cincinnati, OH</td>\n", "      <td>1932-05-26</td>\n", "      <td>80424</td>\n", "      <td>1837-10-31</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   symbol                                         name  \\\n", "0    AMZN                   Amazon.com, Inc.amazon.com   \n", "1    AMGN                                   Amgen Inc.   \n", "2     CRM                             Salesforce, Inc.   \n", "3     HON                 Honeywell International Inc.   \n", "4      GS                The Goldman Sachs Group, Inc.   \n", "5    AAPL                                   Apple Inc.   \n", "6     NKE                                   Nike, Inc.   \n", "7       V                                    Visa Inc.   \n", "8     UNH              UnitedHealth Group Incorporated   \n", "9    CSCO                          Cisco Systems, Inc.   \n", "10    TRV                The Travelers Companies, Inc.   \n", "11    CVX                          Chevron Corporation   \n", "12     VZ                  Verizon Communications Inc.   \n", "13     HD                         The Home Depot, Inc.   \n", "14   INTC                            Intel Corporation   \n", "15   MSFT                        Microsoft Corporation   \n", "16    JNJ                            Johnson & Johnson   \n", "17    WMT                                 Walmart Inc.   \n", "18    CAT                             Caterpillar Inc.   \n", "19    DIS                      The Walt Disney Company   \n", "20    DOW                                     Dow Inc.   \n", "21    JPM                         JPMorgan Chase & Co.   \n", "22     BA                           The Boeing Company   \n", "23     KO                        The Coca-Cola Company   \n", "24    MCD                       McDonald's Corporation   \n", "25    AXP                     American Express Company   \n", "26    IBM  International Business Machines Corporation   \n", "27    MRK                            Merck & Co., Inc.   \n", "28    MMM                                   3M Company   \n", "29     PG                 The Procter & Gamble Company   \n", "\n", "                    sector              sub_sector        headquarter  \\\n", "0        Consumer Cyclical       Consumer Cyclical        Seattle, WA   \n", "1               Healthcare              Healthcare  Thousand Oaks, CA   \n", "2               Technology              Technology  San Francisco, CA   \n", "3              Industrials             Industrials      Charlotte, NC   \n", "4       Financial Services      Financial Services       New York, NY   \n", "5               Technology              Technology      Cupertino, CA   \n", "6        Consumer Cyclical       Consumer Cyclical      Beaverton, OR   \n", "7       Financial Services      Financial Services  San Francisco, CA   \n", "8               Healthcare              Healthcare     Minnetonka, MN   \n", "9               Technology              Technology       San Jose, CA   \n", "10      Financial Services      Financial Services       New York, NY   \n", "11                  Energy                  Energy      San Ramon, CA   \n", "12  Communication Services  Communication Services       New York, NY   \n", "13       Consumer Cyclical       Consumer Cyclical        Atlanta, GA   \n", "14              Technology              Technology    Santa Clara, CA   \n", "15              Technology              Technology        Redmond, WA   \n", "16              Healthcare              Healthcare  New Brunswick, NJ   \n", "17      Consumer Defensive      Consumer Defensive    Bentonville, AR   \n", "18             Industrials             Industrials      Deerfield, IL   \n", "19  Communication Services  Communication Services        Burbank, CA   \n", "20         Basic Materials         Basic Materials        Midland, MI   \n", "21      Financial Services      Financial Services       New York, NY   \n", "22             Industrials             Industrials      Arlington, VA   \n", "23      Consumer Defensive      Consumer Defensive        Atlanta, GA   \n", "24       Consumer Cyclical       Consumer Cyclical        Chicago, IL   \n", "25      Financial Services      Financial Services       New York, NY   \n", "26              Technology              Technology         Armonk, NY   \n", "27              Healthcare              Healthcare     Kenilworth, NJ   \n", "28             Industrials             Industrials     Saint Paul, MN   \n", "29      Consumer Defensive      Consumer Defensive     Cincinnati, OH   \n", "\n", "   date_first_added      cik     founded  \n", "0        2024-02-26  1018724  1994-07-05  \n", "1        2020-08-31   318154  1980-04-08  \n", "2        2020-08-31  1108524  1999-02-03  \n", "3        2020-08-31   773840  1906-01-01  \n", "4        2019-04-02   886982  1869-01-01  \n", "5        2015-03-19   320193  1976-04-01  \n", "6        2013-09-20   320187  1964-01-25  \n", "7        2013-09-20  1403161  1958-09-18  \n", "8        2012-09-24   731766  1977-01-01  \n", "9        2009-06-08   858877  1984-12-10  \n", "10       2009-06-08    86312  1853-01-01  \n", "11       2008-02-19    93410  1879-09-10  \n", "12       2004-04-08   732712  1983-10-07  \n", "13       1999-11-01   354950  1978-02-06  \n", "14       1999-11-01    50863  1968-07-18  \n", "15       1999-11-01   789019  1975-04-04  \n", "16       1997-03-17   200406  1886-01-01  \n", "17       1997-03-17   104169  1962-07-02  \n", "18       1991-05-06    18230  1925-04-15  \n", "19       1991-05-06  1744489  1923-10-16  \n", "20       1991-05-06  1751788  2019-04-01  \n", "21       1991-05-06    19617  2000-12-01  \n", "22       1987-03-12    12927  1916-07-15  \n", "23       1987-03-12    21344  1892-01-29  \n", "24       1985-10-30    63908  1940-05-15  \n", "25       1982-08-30     4962  1850-03-18  \n", "26       1979-06-29    51143  1924-06-16  \n", "27       1979-06-29   310158  1891-01-01  \n", "28       1976-08-09    66740  1902-06-13  \n", "29       1932-05-26    80424  1837-10-31  "]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取指数成份股, dowjones, nasdaq, sp500(无权限)\n", "\n", "obb.index.constituents(\"dowjones\", provider='fmp').to_df()"]}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}