{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["这几天，巴菲特大幅减仓，持有现金量超历史的消息，重创了全球股市。在感叹股神的智慧之余，我们还能学到哪些东西呢？\n", "\n", "今天我们从巴菲特的一则赌约说起。这则赌约是2006年，巴菲特在股东大会上提出的：一只简简单单跟踪美股市场的基金, 能够击败任何一位自信满满的对冲基金经理。\n", "\n", "巴菲特是对的。不过，我们今天不谈如何跟踪指数 -- 这是各种指增策略已经在做的事情 -- 我们将探讨个股纳入指数后的表现，并且要指出指数编制的一个新动向，以及它背后反映的政策导向。\n", "\n", "数据来自万得。一共是4个excel，分别是历年各大股指编制数据。\n", "\n", "我们先看看上证50的：\n", "\n", "这是一个3000多行，50列的数据。索引是交易日，单格值则是每个交易日，编排指数的标的。这些标的的变化，反映了高层的意志及行业的业绩趋势。\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>...</th>\n", "      <th>40</th>\n", "      <th>41</th>\n", "      <th>42</th>\n", "      <th>43</th>\n", "      <th>44</th>\n", "      <th>45</th>\n", "      <th>46</th>\n", "      <th>47</th>\n", "      <th>48</th>\n", "      <th>49</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Unnamed: 0</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2010-01-04</th>\n", "      <td>600000.SH</td>\n", "      <td>600005.SH</td>\n", "      <td>600015.SH</td>\n", "      <td>600016.SH</td>\n", "      <td>600018.SH</td>\n", "      <td>600019.SH</td>\n", "      <td>600028.SH</td>\n", "      <td>600029.SH</td>\n", "      <td>600030.SH</td>\n", "      <td>600036.SH</td>\n", "      <td>...</td>\n", "      <td>601668.SH</td>\n", "      <td>601727.SH</td>\n", "      <td>601766.SH</td>\n", "      <td>601857.SH</td>\n", "      <td>601898.SH</td>\n", "      <td>601899.SH</td>\n", "      <td>601919.SH</td>\n", "      <td>601939.SH</td>\n", "      <td>601958.SH</td>\n", "      <td>601988.SH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-01-05</th>\n", "      <td>600000.SH</td>\n", "      <td>600005.SH</td>\n", "      <td>600015.SH</td>\n", "      <td>600016.SH</td>\n", "      <td>600018.SH</td>\n", "      <td>600019.SH</td>\n", "      <td>600028.SH</td>\n", "      <td>600029.SH</td>\n", "      <td>600030.SH</td>\n", "      <td>600036.SH</td>\n", "      <td>...</td>\n", "      <td>601668.SH</td>\n", "      <td>601727.SH</td>\n", "      <td>601766.SH</td>\n", "      <td>601857.SH</td>\n", "      <td>601898.SH</td>\n", "      <td>601899.SH</td>\n", "      <td>601919.SH</td>\n", "      <td>601939.SH</td>\n", "      <td>601958.SH</td>\n", "      <td>601988.SH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-01-06</th>\n", "      <td>600000.SH</td>\n", "      <td>600005.SH</td>\n", "      <td>600015.SH</td>\n", "      <td>600016.SH</td>\n", "      <td>600018.SH</td>\n", "      <td>600019.SH</td>\n", "      <td>600028.SH</td>\n", "      <td>600029.SH</td>\n", "      <td>600030.SH</td>\n", "      <td>600036.SH</td>\n", "      <td>...</td>\n", "      <td>601668.SH</td>\n", "      <td>601727.SH</td>\n", "      <td>601766.SH</td>\n", "      <td>601857.SH</td>\n", "      <td>601898.SH</td>\n", "      <td>601899.SH</td>\n", "      <td>601919.SH</td>\n", "      <td>601939.SH</td>\n", "      <td>601958.SH</td>\n", "      <td>601988.SH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-01-07</th>\n", "      <td>600000.SH</td>\n", "      <td>600005.SH</td>\n", "      <td>600015.SH</td>\n", "      <td>600016.SH</td>\n", "      <td>600018.SH</td>\n", "      <td>600019.SH</td>\n", "      <td>600028.SH</td>\n", "      <td>600029.SH</td>\n", "      <td>600030.SH</td>\n", "      <td>600036.SH</td>\n", "      <td>...</td>\n", "      <td>601668.SH</td>\n", "      <td>601727.SH</td>\n", "      <td>601766.SH</td>\n", "      <td>601857.SH</td>\n", "      <td>601898.SH</td>\n", "      <td>601899.SH</td>\n", "      <td>601919.SH</td>\n", "      <td>601939.SH</td>\n", "      <td>601958.SH</td>\n", "      <td>601988.SH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-01-08</th>\n", "      <td>600000.SH</td>\n", "      <td>600005.SH</td>\n", "      <td>600015.SH</td>\n", "      <td>600016.SH</td>\n", "      <td>600018.SH</td>\n", "      <td>600019.SH</td>\n", "      <td>600028.SH</td>\n", "      <td>600029.SH</td>\n", "      <td>600030.SH</td>\n", "      <td>600036.SH</td>\n", "      <td>...</td>\n", "      <td>601668.SH</td>\n", "      <td>601727.SH</td>\n", "      <td>601766.SH</td>\n", "      <td>601857.SH</td>\n", "      <td>601898.SH</td>\n", "      <td>601899.SH</td>\n", "      <td>601919.SH</td>\n", "      <td>601939.SH</td>\n", "      <td>601958.SH</td>\n", "      <td>601988.SH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-07-30</th>\n", "      <td>600809.SH</td>\n", "      <td>688041.SH</td>\n", "      <td>600941.SH</td>\n", "      <td>601728.SH</td>\n", "      <td>688981.SH</td>\n", "      <td>601658.SH</td>\n", "      <td>688012.SH</td>\n", "      <td>603259.SH</td>\n", "      <td>688111.SH</td>\n", "      <td>603501.SH</td>\n", "      <td>...</td>\n", "      <td>600276.SH</td>\n", "      <td>600309.SH</td>\n", "      <td>600150.SH</td>\n", "      <td>600436.SH</td>\n", "      <td>600900.SH</td>\n", "      <td>601166.SH</td>\n", "      <td>600406.SH</td>\n", "      <td>601398.SH</td>\n", "      <td>601288.SH</td>\n", "      <td>600031.SH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-07-31</th>\n", "      <td>600809.SH</td>\n", "      <td>688041.SH</td>\n", "      <td>600941.SH</td>\n", "      <td>601728.SH</td>\n", "      <td>688981.SH</td>\n", "      <td>601658.SH</td>\n", "      <td>688012.SH</td>\n", "      <td>603259.SH</td>\n", "      <td>688111.SH</td>\n", "      <td>603501.SH</td>\n", "      <td>...</td>\n", "      <td>600276.SH</td>\n", "      <td>600309.SH</td>\n", "      <td>600150.SH</td>\n", "      <td>600436.SH</td>\n", "      <td>600900.SH</td>\n", "      <td>601166.SH</td>\n", "      <td>600406.SH</td>\n", "      <td>601398.SH</td>\n", "      <td>601288.SH</td>\n", "      <td>600031.SH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-01</th>\n", "      <td>600809.SH</td>\n", "      <td>688041.SH</td>\n", "      <td>600941.SH</td>\n", "      <td>601728.SH</td>\n", "      <td>688981.SH</td>\n", "      <td>601658.SH</td>\n", "      <td>688012.SH</td>\n", "      <td>603259.SH</td>\n", "      <td>688111.SH</td>\n", "      <td>603501.SH</td>\n", "      <td>...</td>\n", "      <td>600276.SH</td>\n", "      <td>600309.SH</td>\n", "      <td>600150.SH</td>\n", "      <td>600436.SH</td>\n", "      <td>600900.SH</td>\n", "      <td>601166.SH</td>\n", "      <td>600406.SH</td>\n", "      <td>601398.SH</td>\n", "      <td>601288.SH</td>\n", "      <td>600031.SH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-02</th>\n", "      <td>600809.SH</td>\n", "      <td>688041.SH</td>\n", "      <td>600941.SH</td>\n", "      <td>601728.SH</td>\n", "      <td>688981.SH</td>\n", "      <td>601658.SH</td>\n", "      <td>688012.SH</td>\n", "      <td>603259.SH</td>\n", "      <td>688111.SH</td>\n", "      <td>603501.SH</td>\n", "      <td>...</td>\n", "      <td>600276.SH</td>\n", "      <td>600309.SH</td>\n", "      <td>600150.SH</td>\n", "      <td>600436.SH</td>\n", "      <td>600900.SH</td>\n", "      <td>601166.SH</td>\n", "      <td>600406.SH</td>\n", "      <td>601398.SH</td>\n", "      <td>601288.SH</td>\n", "      <td>600031.SH</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-05</th>\n", "      <td>600809.SH</td>\n", "      <td>688041.SH</td>\n", "      <td>600941.SH</td>\n", "      <td>601728.SH</td>\n", "      <td>688981.SH</td>\n", "      <td>601658.SH</td>\n", "      <td>688012.SH</td>\n", "      <td>603259.SH</td>\n", "      <td>688111.SH</td>\n", "      <td>603501.SH</td>\n", "      <td>...</td>\n", "      <td>600276.SH</td>\n", "      <td>600309.SH</td>\n", "      <td>600150.SH</td>\n", "      <td>600436.SH</td>\n", "      <td>600900.SH</td>\n", "      <td>601166.SH</td>\n", "      <td>600406.SH</td>\n", "      <td>601398.SH</td>\n", "      <td>601288.SH</td>\n", "      <td>600031.SH</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3544 rows × 50 columns</p>\n", "</div>"], "text/plain": ["                    0          1          2          3          4          5  \\\n", "Unnamed: 0                                                                     \n", "2010-01-04  600000.SH  600005.SH  600015.SH  600016.SH  600018.SH  600019.SH   \n", "2010-01-05  600000.SH  600005.SH  600015.SH  600016.SH  600018.SH  600019.SH   \n", "2010-01-06  600000.SH  600005.SH  600015.SH  600016.SH  600018.SH  600019.SH   \n", "2010-01-07  600000.SH  600005.SH  600015.SH  600016.SH  600018.SH  600019.SH   \n", "2010-01-08  600000.SH  600005.SH  600015.SH  600016.SH  600018.SH  600019.SH   \n", "...               ...        ...        ...        ...        ...        ...   \n", "2024-07-30  600809.SH  688041.SH  600941.SH  601728.SH  688981.SH  601658.SH   \n", "2024-07-31  600809.SH  688041.SH  600941.SH  601728.SH  688981.SH  601658.SH   \n", "2024-08-01  600809.SH  688041.SH  600941.SH  601728.SH  688981.SH  601658.SH   \n", "2024-08-02  600809.SH  688041.SH  600941.SH  601728.SH  688981.SH  601658.SH   \n", "2024-08-05  600809.SH  688041.SH  600941.SH  601728.SH  688981.SH  601658.SH   \n", "\n", "                    6          7          8          9  ...         40  \\\n", "Unnamed: 0                                              ...              \n", "2010-01-04  600028.SH  600029.SH  600030.SH  600036.SH  ...  601668.SH   \n", "2010-01-05  600028.SH  600029.SH  600030.SH  600036.SH  ...  601668.SH   \n", "2010-01-06  600028.SH  600029.SH  600030.SH  600036.SH  ...  601668.SH   \n", "2010-01-07  600028.SH  600029.SH  600030.SH  600036.SH  ...  601668.SH   \n", "2010-01-08  600028.SH  600029.SH  600030.SH  600036.SH  ...  601668.SH   \n", "...               ...        ...        ...        ...  ...        ...   \n", "2024-07-30  688012.SH  603259.SH  688111.SH  603501.SH  ...  600276.SH   \n", "2024-07-31  688012.SH  603259.SH  688111.SH  603501.SH  ...  600276.SH   \n", "2024-08-01  688012.SH  603259.SH  688111.SH  603501.SH  ...  600276.SH   \n", "2024-08-02  688012.SH  603259.SH  688111.SH  603501.SH  ...  600276.SH   \n", "2024-08-05  688012.SH  603259.SH  688111.SH  603501.SH  ...  600276.SH   \n", "\n", "                   41         42         43         44         45         46  \\\n", "Unnamed: 0                                                                     \n", "2010-01-04  601727.SH  601766.SH  601857.SH  601898.SH  601899.SH  601919.SH   \n", "2010-01-05  601727.SH  601766.SH  601857.SH  601898.SH  601899.SH  601919.SH   \n", "2010-01-06  601727.SH  601766.SH  601857.SH  601898.SH  601899.SH  601919.SH   \n", "2010-01-07  601727.SH  601766.SH  601857.SH  601898.SH  601899.SH  601919.SH   \n", "2010-01-08  601727.SH  601766.SH  601857.SH  601898.SH  601899.SH  601919.SH   \n", "...               ...        ...        ...        ...        ...        ...   \n", "2024-07-30  600309.SH  600150.SH  600436.SH  600900.SH  601166.SH  600406.SH   \n", "2024-07-31  600309.SH  600150.SH  600436.SH  600900.SH  601166.SH  600406.SH   \n", "2024-08-01  600309.SH  600150.SH  600436.SH  600900.SH  601166.SH  600406.SH   \n", "2024-08-02  600309.SH  600150.SH  600436.SH  600900.SH  601166.SH  600406.SH   \n", "2024-08-05  600309.SH  600150.SH  600436.SH  600900.SH  601166.SH  600406.SH   \n", "\n", "                   47         48         49  \n", "Unnamed: 0                                   \n", "2010-01-04  601939.SH  601958.SH  601988.SH  \n", "2010-01-05  601939.SH  601958.SH  601988.SH  \n", "2010-01-06  601939.SH  601958.SH  601988.SH  \n", "2010-01-07  601939.SH  601958.SH  601988.SH  \n", "2010-01-08  601939.SH  601958.SH  601988.SH  \n", "...               ...        ...        ...  \n", "2024-07-30  601398.SH  601288.SH  600031.SH  \n", "2024-07-31  601398.SH  601288.SH  600031.SH  \n", "2024-08-01  601398.SH  601288.SH  600031.SH  \n", "2024-08-02  601398.SH  601288.SH  600031.SH  \n", "2024-08-05  601398.SH  601288.SH  600031.SH  \n", "\n", "[3544 rows x 50 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "sz50 = pd.read_excel(\"/tmp/sz50stocks.xlsx\")\n", "sz50 = sz50.set_index(\"Unnamed: 0\")"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["沪深300\n", "2020-12-14 00:00:00 4 {'688008.SH', '688012.SH', '688009.SH', '688036.SH'}\n", "2021-06-15 00:00:00 5 {'688396.SH', '688111.SH', '688009.SH', '688363.SH', '688169.SH', '688126.SH', '688008.SH', '688012.SH', '688036.SH'}\n", "2021-12-13 00:00:00 3 {'688396.SH', '688111.SH', '688009.SH', '688363.SH', '688169.SH', '688981.SH', '688126.SH', '688008.SH', '688599.SH', '688012.SH', '688036.SH', '688561.SH'}\n", "2022-06-13 00:00:00 1 {'688396.SH', '688111.SH', '688363.SH', '688169.SH', '688012.SH', '688981.SH', '688005.SH', '688126.SH', '688008.SH', '688599.SH', '688065.SH', '688036.SH', '688561.SH'}\n", "2022-12-12 00:00:00 2 {'688303.SH', '688396.SH', '688111.SH', '688363.SH', '688169.SH', '688012.SH', '688981.SH', '688005.SH', '688126.SH', '688008.SH', '688599.SH', '688065.SH', '688187.SH', '688036.SH', '688561.SH'}\n", "2023-12-11 00:00:00 2 {'688303.SH', '688396.SH', '688111.SH', '688363.SH', '688223.SH', '688271.SH', '688187.SH', '688012.SH', '688256.SH', '688981.SH', '688126.SH', '688008.SH', '688599.SH', '688065.SH', '688041.SH', '688036.SH', '688561.SH'}\n", "中证1000\n", "2020-12-14 00:00:00 19 {'688018.SH', '688116.SH', '688033.SH', '688366.SH', '688030.SH', '688006.SH', '688066.SH', '688098.SH', '688019.SH', '688001.SH', '688388.SH', '688108.SH', '688139.SH', '688005.SH', '688007.SH', '688016.SH', '688122.SH', '688188.SH', '688020.SH'}\n", "2021-06-15 00:00:00 22 {'688222.SH', '688018.SH', '688116.SH', '688198.SH', '688033.SH', '688366.SH', '688030.SH', '688006.SH', '688100.SH', '688365.SH', '688298.SH', '688066.SH', '688098.SH', '688333.SH', '688037.SH', '688200.SH', '688086.SH', '688019.SH', '688166.SH', '688278.SH', '688001.SH', '688388.SH', '688108.SH', '688158.SH', '688268.SH', '688139.SH', '688266.SH', '688023.SH', '688005.SH', '688007.SH', '688318.SH', '688202.SH', '688389.SH', '688016.SH', '688085.SH', '688122.SH', '688368.SH', '688177.SH', '688188.SH', '688020.SH', '688399.SH'}\n", "2021-12-13 00:00:00 17 {'688017.SH', '688505.SH', '688133.SH', '688339.SH', '688598.SH', '688106.SH', '688222.SH', '688018.SH', '688116.SH', '688198.SH', '688277.SH', '688033.SH', '688366.SH', '688030.SH', '688100.SH', '688365.SH', '688298.SH', '688066.SH', '688526.SH', '688390.SH', '688788.SH', '688333.SH', '688050.SH', '688037.SH', '688095.SH', '688200.SH', '688086.SH', '688019.SH', '688166.SH', '688278.SH', '688301.SH', '688001.SH', '688388.SH', '688568.SH', '688158.SH', '688508.SH', '688268.SH', '688139.SH', '688027.SH', '688266.SH', '688408.SH', '688023.SH', '688055.SH', '688007.SH', '688202.SH', '688127.SH', '688318.SH', '688389.SH', '688520.SH', '688016.SH', '688516.SH', '688085.SH', '688122.SH', '688586.SH', '688368.SH', '688177.SH', '688020.SH', '688399.SH'}\n", "2022-02-14 00:00:00 1 {'688017.SH', '688505.SH', '688133.SH', '688339.SH', '688598.SH', '688106.SH', '688222.SH', '688018.SH', '688116.SH', '688198.SH', '688277.SH', '688033.SH', '688366.SH', '688030.SH', '688100.SH', '688365.SH', '688298.SH', '688066.SH', '688526.SH', '688390.SH', '688788.SH', '688333.SH', '688050.SH', '688037.SH', '688095.SH', '688200.SH', '688086.SH', '688019.SH', '688166.SH', '688278.SH', '688301.SH', '688001.SH', '688388.SH', '688568.SH', '688158.SH', '688508.SH', '688268.SH', '688139.SH', '688027.SH', '688266.SH', '688408.SH', '688023.SH', '688055.SH', '688007.SH', '688202.SH', '688127.SH', '688318.SH', '688389.SH', '688520.SH', '688016.SH', '688516.SH', '688085.SH', '688122.SH', '688586.SH', '688368.SH', '688177.SH', '688020.SH', '688399.SH', '688356.SH'}\n", "2022-06-13 00:00:00 8 {'688017.SH', '688505.SH', '688133.SH', '688063.SH', '688315.SH', '688696.SH', '688083.SH', '688339.SH', '688598.SH', '688106.SH', '688018.SH', '688116.SH', '688198.SH', '688277.SH', '688033.SH', '688366.SH', '688686.SH', '688680.SH', '688100.SH', '688365.SH', '688298.SH', '688066.SH', '688526.SH', '688788.SH', '688333.SH', '688050.SH', '688037.SH', '688095.SH', '688200.SH', '688278.SH', '688019.SH', '688166.SH', '688383.SH', '688301.SH', '688001.SH', '688608.SH', '688388.SH', '688568.SH', '688158.SH', '688508.SH', '688268.SH', '688139.SH', '688027.SH', '688266.SH', '688408.SH', '688128.SH', '688023.SH', '688055.SH', '688007.SH', '688202.SH', '688127.SH', '688318.SH', '688389.SH', '688520.SH', '688016.SH', '688516.SH', '688085.SH', '688617.SH', '688233.SH', '688586.SH', '688368.SH', '688699.SH', '688222.SH', '688578.SH', '688020.SH', '688399.SH', '688356.SH'}\n", "2022-12-12 00:00:00 16 {'688133.SH', '688339.SH', '688556.SH', '688798.SH', '688707.SH', '688298.SH', '688526.SH', '688001.SH', '688202.SH', '688127.SH', '688016.SH', '688516.SH', '688085.SH', '688617.SH', '688586.SH', '688639.SH', '688711.SH', '688083.SH', '688676.SH', '688330.SH', '688321.SH', '688198.SH', '688277.SH', '688033.SH', '688366.SH', '688686.SH', '688700.SH', '688066.SH', '688037.SH', '688660.SH', '688568.SH', '688158.SH', '688508.SH', '688139.SH', '688131.SH', '688266.SH', '688408.SH', '688800.SH', '688023.SH', '688318.SH', '688389.SH', '688161.SH', '688518.SH', '688233.SH', '688368.SH', '688575.SH', '688789.SH', '688356.SH', '688276.SH', '688017.SH', '688018.SH', '688680.SH', '688100.SH', '688333.SH', '688050.SH', '688383.SH', '688166.SH', '688776.SH', '688559.SH', '688128.SH', '688520.SH', '688499.SH', '688778.SH', '688578.SH', '688148.SH', '688315.SH', '688696.SH', '688598.SH', '688425.SH', '688106.SH', '688739.SH', '688788.SH', '688278.SH', '688019.SH', '688608.SH', '688733.SH', '688388.SH', '688268.SH', '688027.SH', '688007.SH', '688699.SH', '688200.SH', '688399.SH'}\n", "2023-06-12 00:00:00 15 {'688133.SH', '688339.SH', '688556.SH', '688798.SH', '688153.SH', '688707.SH', '688298.SH', '688526.SH', '688001.SH', '688202.SH', '688127.SH', '688016.SH', '688337.SH', '688085.SH', '688617.SH', '688586.SH', '688190.SH', '688232.SH', '688639.SH', '688711.SH', '688083.SH', '688676.SH', '688330.SH', '688321.SH', '688198.SH', '688277.SH', '688033.SH', '688366.SH', '688686.SH', '688700.SH', '688066.SH', '688091.SH', '688037.SH', '688660.SH', '688167.SH', '688568.SH', '688158.SH', '688508.SH', '688139.SH', '688131.SH', '688266.SH', '688408.SH', '688800.SH', '688023.SH', '688262.SH', '688318.SH', '688389.SH', '688161.SH', '688518.SH', '688233.SH', '688368.SH', '688575.SH', '688789.SH', '688356.SH', '688110.SH', '688017.SH', '688206.SH', '688018.SH', '688680.SH', '688100.SH', '688333.SH', '688050.SH', '688383.SH', '688166.SH', '688261.SH', '688776.SH', '688559.SH', '688128.SH', '688499.SH', '688578.SH', '688148.SH', '688315.SH', '688696.SH', '688598.SH', '688425.SH', '688106.SH', '688046.SH', '688238.SH', '688173.SH', '688270.SH', '688331.SH', '688235.SH', '688739.SH', '688278.SH', '688019.SH', '688608.SH', '688733.SH', '688388.SH', '688268.SH', '688027.SH', '688048.SH', '688123.SH', '688007.SH', '688699.SH', '688088.SH', '688239.SH', '688399.SH', '688326.SH'}\n", "2023-07-17 00:00:00 1 {'688209.SH', '688133.SH', '688339.SH', '688556.SH', '688798.SH', '688153.SH', '688707.SH', '688298.SH', '688526.SH', '688001.SH', '688202.SH', '688127.SH', '688016.SH', '688337.SH', '688085.SH', '688617.SH', '688586.SH', '688190.SH', '688232.SH', '688639.SH', '688711.SH', '688083.SH', '688676.SH', '688330.SH', '688321.SH', '688198.SH', '688277.SH', '688033.SH', '688366.SH', '688686.SH', '688700.SH', '688066.SH', '688091.SH', '688037.SH', '688660.SH', '688167.SH', '688568.SH', '688158.SH', '688508.SH', '688139.SH', '688131.SH', '688266.SH', '688408.SH', '688800.SH', '688023.SH', '688262.SH', '688318.SH', '688389.SH', '688161.SH', '688518.SH', '688233.SH', '688368.SH', '688575.SH', '688789.SH', '688356.SH', '688110.SH', '688017.SH', '688206.SH', '688018.SH', '688680.SH', '688100.SH', '688333.SH', '688050.SH', '688383.SH', '688166.SH', '688261.SH', '688776.SH', '688559.SH', '688128.SH', '688499.SH', '688578.SH', '688148.SH', '688315.SH', '688696.SH', '688598.SH', '688425.SH', '688106.SH', '688046.SH', '688238.SH', '688173.SH', '688270.SH', '688331.SH', '688235.SH', '688739.SH', '688278.SH', '688019.SH', '688608.SH', '688733.SH', '688388.SH', '688268.SH', '688027.SH', '688048.SH', '688123.SH', '688007.SH', '688699.SH', '688088.SH', '688239.SH', '688399.SH', '688326.SH'}\n", "2023-12-11 00:00:00 12 {'688209.SH', '688133.SH', '688339.SH', '688556.SH', '688798.SH', '688097.SH', '688707.SH', '688298.SH', '688526.SH', '688031.SH', '688439.SH', '688001.SH', '688202.SH', '688127.SH', '688016.SH', '688337.SH', '688617.SH', '688630.SH', '688586.SH', '688190.SH', '688232.SH', '688152.SH', '688639.SH', '688711.SH', '688083.SH', '688676.SH', '688700.SH', '688321.SH', '688198.SH', '688033.SH', '688366.SH', '688686.SH', '688275.SH', '688380.SH', '688403.SH', '688066.SH', '688091.SH', '688037.SH', '688660.SH', '688167.SH', '688568.SH', '688158.SH', '688508.SH', '688139.SH', '688131.SH', '688266.SH', '688408.SH', '688327.SH', '688800.SH', '688023.SH', '688262.SH', '688318.SH', '688389.SH', '688161.SH', '688518.SH', '688368.SH', '688575.SH', '688789.SH', '688356.SH', '688110.SH', '688017.SH', '688206.SH', '688018.SH', '688116.SH', '688680.SH', '688100.SH', '688333.SH', '688050.SH', '688383.SH', '688166.SH', '688261.SH', '688776.SH', '688559.SH', '688128.SH', '688409.SH', '688392.SH', '688499.SH', '688185.SH', '688578.SH', '688213.SH', '688400.SH', '688315.SH', '688192.SH', '688696.SH', '688598.SH', '688106.SH', '688046.SH', '688238.SH', '688351.SH', '688596.SH', '688173.SH', '688270.SH', '688235.SH', '688739.SH', '688278.SH', '688019.SH', '688322.SH', '688608.SH', '688733.SH', '688388.SH', '688268.SH', '688289.SH', '688027.SH', '688048.SH', '688123.SH', '688007.SH', '688381.SH', '688372.SH', '688088.SH', '688239.SH', '688326.SH'}\n", "2024-06-18 00:00:00 13 {'688209.SH', '688352.SH', '688339.SH', '688076.SH', '688556.SH', '688798.SH', '688707.SH', '688097.SH', '688298.SH', '688526.SH', '688439.SH', '688031.SH', '688141.SH', '688001.SH', '688484.SH', '688147.SH', '688432.SH', '688202.SH', '688127.SH', '688016.SH', '688337.SH', '688630.SH', '688586.SH', '688190.SH', '688177.SH', '688690.SH', '688232.SH', '688152.SH', '688711.SH', '688639.SH', '688083.SH', '688676.SH', '688336.SH', '688700.SH', '688321.SH', '688033.SH', '688503.SH', '688686.SH', '688366.SH', '688380.SH', '688403.SH', '688066.SH', '688091.SH', '688037.SH', '688433.SH', '688146.SH', '688167.SH', '688568.SH', '688158.SH', '688508.SH', '688139.SH', '688131.SH', '688266.SH', '688408.SH', '688327.SH', '688800.SH', '688023.SH', '688262.SH', '688389.SH', '688318.SH', '688161.SH', '688518.SH', '688368.SH', '688575.SH', '688789.SH', '688110.SH', '688206.SH', '688018.SH', '688116.SH', '688208.SH', '688680.SH', '688100.SH', '688343.SH', '688333.SH', '688050.SH', '688658.SH', '688383.SH', '688166.SH', '688105.SH', '688261.SH', '688776.SH', '688559.SH', '688409.SH', '688128.SH', '688392.SH', '688515.SH', '688185.SH', '688578.SH', '688400.SH', '688315.SH', '688192.SH', '688696.SH', '688598.SH', '688106.SH', '688046.SH', '688351.SH', '688596.SH', '688173.SH', '688270.SH', '688235.SH', '688739.SH', '688531.SH', '688278.SH', '688019.SH', '688322.SH', '688608.SH', '688388.SH', '688362.SH', '688502.SH', '688268.SH', '688289.SH', '688498.SH', '688027.SH', '688062.SH', '688048.SH', '688123.SH', '688007.SH', '688381.SH', '688372.SH', '688279.SH', '688088.SH', '688239.SH', '688183.SH', '688326.SH'}\n", "上证50\n", "2022-12-12 00:00:00 1 {'688599.SH'}\n", "2023-06-12 00:00:00 1 {'688599.SH', '688111.SH'}\n", "2023-12-11 00:00:00 2 {'688111.SH', '688599.SH', '688981.SH', '688041.SH'}\n", "中证500\n", "2020-12-14 00:00:00 5 {'688002.SH', '688029.SH', '688099.SH', '688088.SH', '688321.SH'}\n", "2021-06-15 00:00:00 1 {'688002.SH', '688029.SH', '688099.SH', '688088.SH', '688321.SH', '688208.SH'}\n", "2021-12-13 00:00:00 7 {'688521.SH', '688002.SH', '688029.SH', '688289.SH', '688099.SH', '688536.SH', '688088.SH', '688208.SH', '688005.SH', '688321.SH', '688188.SH', '688065.SH', '688006.SH'}\n", "2022-06-13 00:00:00 6 {'688819.SH', '688002.SH', '688029.SH', '688321.SH', '688256.SH', '688208.SH', '688006.SH', '688009.SH', '688390.SH', '688099.SH', '689009.SH', '688180.SH', '688289.SH', '688521.SH', '688185.SH', '688777.SH', '688122.SH', '688088.SH', '688188.SH'}\n", "2022-12-12 00:00:00 7 {'688690.SH', '688538.SH', '688819.SH', '688002.SH', '688029.SH', '688063.SH', '688116.SH', '688256.SH', '688208.SH', '688006.SH', '688009.SH', '688390.SH', '688099.SH', '688301.SH', '689009.SH', '688289.SH', '688567.SH', '688521.SH', '688779.SH', '688185.SH', '688777.SH', '688122.SH', '688088.SH', '688385.SH', '688188.SH', '688772.SH'}\n", "2023-06-12 00:00:00 14 {'688690.SH', '688276.SH', '688072.SH', '688538.SH', '688819.SH', '688002.SH', '688029.SH', '688116.SH', '688208.SH', '688256.SH', '688006.SH', '688009.SH', '688281.SH', '688107.SH', '688082.SH', '688234.SH', '688099.SH', '688390.SH', '688301.SH', '689009.SH', '688220.SH', '688052.SH', '688105.SH', '688289.SH', '688567.SH', '688169.SH', '688520.SH', '688516.SH', '688521.SH', '688779.SH', '688185.SH', '688777.SH', '688778.SH', '688295.SH', '688536.SH', '688772.SH', '688385.SH', '688188.SH', '688200.SH', '688248.SH'}\n", "2023-12-11 00:00:00 10 {'688690.SH', '688276.SH', '688072.SH', '688538.SH', '688819.SH', '688002.SH', '688029.SH', '688063.SH', '688425.SH', '688208.SH', '688006.SH', '688153.SH', '688331.SH', '688281.SH', '688107.SH', '688375.SH', '688082.SH', '688234.SH', '688099.SH', '688390.SH', '688301.SH', '689009.SH', '688220.SH', '688032.SH', '688180.SH', '688052.SH', '688105.SH', '688567.SH', '688297.SH', '688169.SH', '688005.SH', '688520.SH', '688387.SH', '688120.SH', '688728.SH', '688114.SH', '688349.SH', '688516.SH', '688521.SH', '688779.SH', '688778.SH', '688295.SH', '688122.SH', '688536.SH', '688772.SH', '688385.SH', '688188.SH', '688200.SH', '688248.SH', '688348.SH'}\n", "2024-06-18 00:00:00 3 {'688276.SH', '688017.SH', '688538.SH', '688819.SH', '688063.SH', '688002.SH', '688029.SH', '688425.SH', '688006.SH', '688561.SH', '688153.SH', '688475.SH', '688331.SH', '688281.SH', '688375.SH', '688107.SH', '688390.SH', '688525.SH', '688234.SH', '688099.SH', '688172.SH', '688065.SH', '688301.SH', '689009.SH', '688220.SH', '688032.SH', '688180.SH', '688052.SH', '688567.SH', '688297.SH', '688169.SH', '688005.SH', '688520.SH', '688387.SH', '688120.SH', '688114.SH', '688349.SH', '688516.SH', '688521.SH', '688779.SH', '688617.SH', '688777.SH', '688778.SH', '688295.SH', '688122.SH', '688536.SH', '688772.SH', '688385.SH', '688188.SH', '688200.SH', '688248.SH', '688213.SH', '688348.SH'}\n"]}], "source": ["from collections import defaultdict\n", "import pandas as pd\n", "import re\n", "import matplotlib.pyplot as plt\n", "\n", "index_names = {\n", "    \"50\": \"sz50\",\n", "    \"300\": \"hs300\",\n", "    \"500\": \"zz500\",\n", "    \"1000\": \"zz1000\",\n", "}\n", "\n", "# 假设 df 是你的 DataFrame\n", "# df.index 是日期，df 的列是股票代码（字符串）\n", "df = sz50\n", "data = defaultdict(list)\n", "for file in [\"/tmp/hs300stocks.xlsx\",  \"/tmp/zz1000stocks.xlsx\", \"/tmp/sz50stocks.xlsx\",  \"/tmp/zz500stocks.xlsx\"]:\n", "    last = None\n", "    df = pd.read_excel(file, index_col=0)\n", "    matched = re.search(r'(\\d+)', file)\n", "    index = matched.group(1)\n", "    print(index_names.get(index))\n", "    for key, value in df.to_dict(orient='index').items():\n", "        try:\n", "            if last is None:\n", "                last = set(filter(lambda x: x.startswith('68'), value.values()))\n", "                continue\n", "            else:\n", "                now = set(filter(lambda x: x.startswith('68'), value.values()))\n", "                if len(now) - len(last) > 0:\n", "                    incr = len(now) - len(last)\n", "                    print(key, incr, now)\n", "                    data[index].append((key, len(now)))\n", "                last = now\n", "        except Exception:\n", "            pass\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x176e9f640>"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["from cProfile import label\n", "index_names = {\n", "    \"50\": \"SZ50\",\n", "    \"300\": \"HS300\",\n", "    \"500\": \"ZZ500\",\n", "    \"1000\": \"ZZ1000\",\n", "}\n", "\n", "for key in index_names.keys():\n", "    name = index_names[key]\n", "    df = pd.DataFrame(data[key], columns=['date', 'count'])\n", "    df = df.set_index('date')\n", "    plt.plot(df.index, df[\"count\"], label=name)\n", "\n", "plt.legend()"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["defaultdict(list,\n", "            {'300': [(Timestamp('2020-12-14 00:00:00'), 4),\n", "              (Timestamp('2021-06-15 00:00:00'), 9),\n", "              (Timestamp('2021-12-13 00:00:00'), 12),\n", "              (Timestamp('2022-06-13 00:00:00'), 13),\n", "              (Timestamp('2022-12-12 00:00:00'), 15),\n", "              (Timestamp('2023-12-11 00:00:00'), 17)],\n", "             '1000': [(Timestamp('2020-12-14 00:00:00'), 19),\n", "              (Timestamp('2021-06-15 00:00:00'), 41),\n", "              (Timestamp('2021-12-13 00:00:00'), 58),\n", "              (Timestamp('2022-02-14 00:00:00'), 59),\n", "              (Timestamp('2022-06-13 00:00:00'), 67),\n", "              (Timestamp('2022-12-12 00:00:00'), 83),\n", "              (Timestamp('2023-06-12 00:00:00'), 98),\n", "              (Timestamp('2023-07-17 00:00:00'), 99),\n", "              (Timestamp('2023-12-11 00:00:00'), 111),\n", "              (Timestamp('2024-06-18 00:00:00'), 124)],\n", "             '50': [(Timestamp('2022-12-12 00:00:00'), 1),\n", "              (Timestamp('2023-06-12 00:00:00'), 2),\n", "              (Timestamp('2023-12-11 00:00:00'), 4)],\n", "             '500': [(Timestamp('2020-12-14 00:00:00'), 5),\n", "              (Timestamp('2021-06-15 00:00:00'), 6),\n", "              (Timestamp('2021-12-13 00:00:00'), 13),\n", "              (Timestamp('2022-06-13 00:00:00'), 19),\n", "              (Timestamp('2022-12-12 00:00:00'), 26),\n", "              (Timestamp('2023-06-12 00:00:00'), 40),\n", "              (Timestamp('2023-12-11 00:00:00'), 50),\n", "              (Timestamp('2024-06-18 00:00:00'), 53)]})"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 纳入指数的影响\n", "\n", "收益rank排名"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["df_1000 = pd.read_excel(\"/tmp/zz1000stocks.xlsx\", index_col=0)\n", "df_1000.to_csv(\"/tmp/zz1000.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "dates = [datetime.date(2022, 12, 12), datetime.date(2023, 7, 17), datetime.date(2023, 12, 11)]\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "alpha", "language": "python", "name": "alpha"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.11"}}, "nbformat": 4, "nbformat_minor": 2}