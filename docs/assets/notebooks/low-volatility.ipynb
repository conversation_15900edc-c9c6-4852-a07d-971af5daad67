{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "returns = np.random.normal(0, 0.02, size=100)\n", "\n", "fig, axes = plt.subplots(1, 3, figsize=(12,4))\n", "c0 = np.random.randint(5, 50)\n", "\n", "for i, alpha in enumerate((-0.01, 0, 0.01)):\n", "    r = returns + alpha\n", "    close = np.cumprod(1 + r) * c0\n", "    vol = round(np.std(r), 3)\n", "    axes[i].set_title(f\"vol={vol}\")\n", "    axes[i].plot(close)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-04-21 23:02:30,635 I 914170 cfg4py.core:update_config:280 | configuration is\n", "alpha: {data_home: ~/zillionare/alpha/data, tts_server: 'http://127.0.0.1:5002/api/tts?'}\n", "backtest: {url: 'http://192.168.100.114:7080/backtest/api/trade/v0.5/'}\n", "influxdb: {bucket_name: zillionare, enable_compress: true, max_query_size: 5000, org: zillionare,\n", "  token: hwxHycJfp_t6bCOYe2MhEDW4QBOO4FDtgeBWnPR6bGZJGEZ_41m_OHtTJFZKyD2HsbVqkZM8rJNkMvjyoXCG6Q==,\n", "  url: 'http://192.168.100.101:58086'}\n", "notify: {dingtalk_access_token: 58df072143b52368086736cb38236753073ccde6537650cad1d5567747803563,\n", "  keyword: trader}\n", "pluto: {store: ~/zillionare/pluto/store}\n", "redis: {dsn: 'redis://192.168.100.101:56379'}\n", "tasks: {pooling: false, wr: false}\n", "\n", "2024-04-21 23:02:30,643 I 914170 /home/<USER>/miniconda3/envs/coursea/lib/python3.8/site-packages/omicron/dal/cache.py:init:94 | init redis cache...\n", "2024-04-21 23:02:30,654 I 914170 /home/<USER>/miniconda3/envs/coursea/lib/python3.8/site-packages/omicron/dal/cache.py:init:124 | redis cache is inited\n", "2024-04-21 23:02:30,756 I 914170 omicron.models.security:load_securities:333 | 7102 securities loaded, types: {'fjm', 'etf', 'stock', 'mmf', 'index', 'lof', 'reits'}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["init securities done\n"]}, {"ename": "KeyError", "evalue": "\"None of [Index(['frame', 'open', 'high', 'low', 'close', 'volume', 'amount', 'turnover',\\n       'pct'],\\n      dtype='object')] are in the [columns]\"", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 12\u001b[0m\n\u001b[1;32m     10\u001b[0m result \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m     11\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m symbol, name, _ \u001b[38;5;129;01min\u001b[39;00m get_secs():\n\u001b[0;32m---> 12\u001b[0m     bars \u001b[38;5;241m=\u001b[39m \u001b[43mget_bars\u001b[49m\u001b[43m(\u001b[49m\u001b[43msymbol\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m24\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mFrameType\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mMONTH\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mend\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdatetime\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdate\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m2024\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m4\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m18\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     13\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(bars) \u001b[38;5;241m<\u001b[39m \u001b[38;5;241m24\u001b[39m:\n\u001b[1;32m     14\u001b[0m         \u001b[38;5;28;01mcontinue\u001b[39;00m\n", "File \u001b[0;32m/apps/zillionare/docs/assets/notebooks/utils.py:170\u001b[0m, in \u001b[0;36mget_bars\u001b[0;34m(symbol, n, frame_type, end, fq)\u001b[0m\n\u001b[1;32m    167\u001b[0m     df[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mframe\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mto_datetime(df\u001b[38;5;241m.\u001b[39mframe)\u001b[38;5;241m.\u001b[39mastype(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mO\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    168\u001b[0m     df \u001b[38;5;241m=\u001b[39m df[(df\u001b[38;5;241m.\u001b[39mframe \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m start) \u001b[38;5;241m&\u001b[39m (df\u001b[38;5;241m.\u001b[39mframe \u001b[38;5;241m<\u001b[39m\u001b[38;5;241m=\u001b[39m end)]\n\u001b[0;32m--> 170\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mframe\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mopen\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mhigh\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mlow\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mclose\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mvolume\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mamount\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mturnover\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mpct\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mto_records(index\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n", "File \u001b[0;32m~/miniconda3/envs/coursea/lib/python3.8/site-packages/pandas/core/frame.py:3813\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   3811\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m is_iterator(key):\n\u001b[1;32m   3812\u001b[0m         key \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(key)\n\u001b[0;32m-> 3813\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_indexer_strict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcolumns\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m[\u001b[38;5;241m1\u001b[39m]\n\u001b[1;32m   3815\u001b[0m \u001b[38;5;66;03m# take() does not accept boolean indexers\u001b[39;00m\n\u001b[1;32m   3816\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(indexer, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdtype\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mbool\u001b[39m:\n", "File \u001b[0;32m~/miniconda3/envs/coursea/lib/python3.8/site-packages/pandas/core/indexes/base.py:6070\u001b[0m, in \u001b[0;36mIndex._get_indexer_strict\u001b[0;34m(self, key, axis_name)\u001b[0m\n\u001b[1;32m   6067\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[1;32m   6068\u001b[0m     keyarr, indexer, new_indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_reindex_non_unique(keyarr)\n\u001b[0;32m-> 6070\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_raise_if_missing\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkeyarr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis_name\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   6072\u001b[0m keyarr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtake(indexer)\n\u001b[1;32m   6073\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(key, Index):\n\u001b[1;32m   6074\u001b[0m     \u001b[38;5;66;03m# GH 42790 - Preserve name from an Index\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/coursea/lib/python3.8/site-packages/pandas/core/indexes/base.py:6130\u001b[0m, in \u001b[0;36mIndex._raise_if_missing\u001b[0;34m(self, key, indexer, axis_name)\u001b[0m\n\u001b[1;32m   6128\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m use_interval_msg:\n\u001b[1;32m   6129\u001b[0m         key \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(key)\n\u001b[0;32m-> 6130\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNone of [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mkey\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m] are in the [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00maxis_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m]\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m   6132\u001b[0m not_found \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(ensure_index(key)[missing_mask\u001b[38;5;241m.\u001b[39mnonzero()[\u001b[38;5;241m0\u001b[39m]]\u001b[38;5;241m.\u001b[39munique())\n\u001b[1;32m   6133\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnot_found\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m not in index\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: \"None of [Index(['frame', 'open', 'high', 'low', 'close', 'volume', 'amount', 'turnover',\\n       'pct'],\\n      dtype='object')] are in the [columns]\""]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from utils import get_secs, get_bars\n", "from coretypes import FrameType\n", "import datetime\n", "\n", "from coursea import *\n", "await init()\n", "\n", "result = []\n", "for symbol, name, _ in get_secs():\n", "    bars = get_bars(symbol, 24, FrameType.MONTH, end=datetime.date(2024, 4, 18))\n", "    if len(bars) < 24:\n", "        continue\n", "\n", "    close = bars[\"close\"][-24:]\n", "    returns = close[1:]/close[:-1] - 1\n", "    vol = np.std(returns)\n", "    result.append((name, symbol, vol))\n", "\n", "df = pd.DataFrame(result, columns=[\"code\", \"vol\"])\n", "df.<PERSON><PERSON><PERSON><PERSON>(20, \"vol\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "coursea", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 2}