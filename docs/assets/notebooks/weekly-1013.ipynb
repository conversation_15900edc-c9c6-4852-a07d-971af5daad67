{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2., 3., 1., 2., 3., 1., 2., 1., 1., 3., 1., 1., 1., 1., 3., 3., 1.,\n", "       3., 1., 2., 3., 0.])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "from numpy.typing import ArrayLike\n", "\n", "def mad_clip(arr: <PERSON><PERSON><PERSON><PERSON><PERSON>, k: int = 3):\n", "    med = np.median(arr)\n", "    mad = np.median(np.abs(arr - med))\n", "    \n", "    return np.clip(arr, med - k * mad, med + k * mad)\n", "\n", "np.random.seed(78)\n", "arr = np.append(np.random.randint(1, 4, 20), [15, -10])\n", "mad_clip(arr, 3)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>asset</th>\n", "      <th>002095.XSHE</th>\n", "      <th>003042.XSHE</th>\n", "      <th>300099.XSHE</th>\n", "      <th>301060.XSHE</th>\n", "      <th>601689.XSHG</th>\n", "      <th>603255.XSHG</th>\n", "      <th>688669.XSHG</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-12-25</th>\n", "      <td>23.400000</td>\n", "      <td>18.090000</td>\n", "      <td>6.10</td>\n", "      <td>13.00</td>\n", "      <td>73.910004</td>\n", "      <td>36.799999</td>\n", "      <td>18.080000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-26</th>\n", "      <td>21.059999</td>\n", "      <td>17.520000</td>\n", "      <td>5.94</td>\n", "      <td>12.83</td>\n", "      <td>72.879997</td>\n", "      <td>37.000000</td>\n", "      <td>18.080000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-27</th>\n", "      <td>20.070000</td>\n", "      <td>17.590000</td>\n", "      <td>6.04</td>\n", "      <td>12.84</td>\n", "      <td>72.000000</td>\n", "      <td>36.840000</td>\n", "      <td>18.049999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-28</th>\n", "      <td>20.010000</td>\n", "      <td>18.139999</td>\n", "      <td>6.11</td>\n", "      <td>13.14</td>\n", "      <td>72.199997</td>\n", "      <td>38.150002</td>\n", "      <td>18.440001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-29</th>\n", "      <td>20.270000</td>\n", "      <td>18.580000</td>\n", "      <td>6.19</td>\n", "      <td>13.29</td>\n", "      <td>73.500000</td>\n", "      <td>37.299999</td>\n", "      <td>18.740000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["asset       002095.XSHE  003042.XSHE  300099.XSHE  301060.XSHE  601689.XSHG  \\\n", "date                                                                          \n", "2023-12-25    23.400000    18.090000         6.10        13.00    73.910004   \n", "2023-12-26    21.059999    17.520000         5.94        12.83    72.879997   \n", "2023-12-27    20.070000    17.590000         6.04        12.84    72.000000   \n", "2023-12-28    20.010000    18.139999         6.11        13.14    72.199997   \n", "2023-12-29    20.270000    18.580000         6.19        13.29    73.500000   \n", "\n", "asset       603255.XSHG  688669.XSHG  \n", "date                                  \n", "2023-12-25    36.799999    18.080000  \n", "2023-12-26    37.000000    18.080000  \n", "2023-12-27    36.840000    18.049999  \n", "2023-12-28    38.150002    18.440001  \n", "2023-12-29    37.299999    18.740000  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["def mad_clip(df: Union[NDArray, pd.DataFrame], k: int = 3, axis=1):\n", "    \"\"\"使用 MAD 3 倍截断法去极值\n", "    \n", "    Args:\n", "        df: 输入数据，要求索引为日期，资产名为列，单元格值为因子的宽表\n", "        k: 截断倍数。\n", "        axis: 截断方向\n", "    \"\"\"\n", "\n", "    med = np.median(df, axis=axis).reshape(df.shape[0], -1)\n", "    mad = np.median(np.abs(df - med), axis=axis)\n", "\n", "    return np.clip(df.T, med.flatten() - k * 1.4826 * mad, med.flatten() + k * 1.4826 * mad).T\n", "\n", "start = datetime.date(2023, 1, 1)\n", "end = datetime.date(2023, 12, 29)\n", "barss = load_bars(start, end, 7)\n", "\n", "closes = barss[\"close\"].unstack(\"asset\").iloc[-5:]\n", "closes"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>asset</th>\n", "      <th>002095.XSHE</th>\n", "      <th>003042.XSHE</th>\n", "      <th>300099.XSHE</th>\n", "      <th>301060.XSHE</th>\n", "      <th>601689.XSHG</th>\n", "      <th>603255.XSHG</th>\n", "      <th>688669.XSHG</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-12-25</th>\n", "      <td>23.400000</td>\n", "      <td>18.090000</td>\n", "      <td>10.217396</td>\n", "      <td>13.00</td>\n", "      <td>25.962605</td>\n", "      <td>25.962605</td>\n", "      <td>18.080000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-26</th>\n", "      <td>21.059999</td>\n", "      <td>17.520000</td>\n", "      <td>10.296350</td>\n", "      <td>12.83</td>\n", "      <td>25.863649</td>\n", "      <td>25.863649</td>\n", "      <td>18.080000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-27</th>\n", "      <td>20.070000</td>\n", "      <td>17.590000</td>\n", "      <td>10.325655</td>\n", "      <td>12.84</td>\n", "      <td>25.774343</td>\n", "      <td>25.774343</td>\n", "      <td>18.049999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-28</th>\n", "      <td>20.010000</td>\n", "      <td>18.139999</td>\n", "      <td>10.582220</td>\n", "      <td>13.14</td>\n", "      <td>26.297781</td>\n", "      <td>26.297781</td>\n", "      <td>18.440001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-29</th>\n", "      <td>20.270000</td>\n", "      <td>18.580000</td>\n", "      <td>10.659830</td>\n", "      <td>13.29</td>\n", "      <td>26.820169</td>\n", "      <td>26.820169</td>\n", "      <td>18.740000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["asset       002095.XSHE  003042.XSHE  300099.XSHE  301060.XSHE  601689.XSHG  \\\n", "date                                                                          \n", "2023-12-25    23.400000    18.090000    10.217396        13.00    25.962605   \n", "2023-12-26    21.059999    17.520000    10.296350        12.83    25.863649   \n", "2023-12-27    20.070000    17.590000    10.325655        12.84    25.774343   \n", "2023-12-28    20.010000    18.139999    10.582220        13.14    26.297781   \n", "2023-12-29    20.270000    18.580000    10.659830        13.29    26.820169   \n", "\n", "asset       603255.XSHG  688669.XSHG  \n", "date                                  \n", "2023-12-25    25.962605    18.080000  \n", "2023-12-26    25.863649    18.080000  \n", "2023-12-27    25.774343    18.049999  \n", "2023-12-28    26.297781    18.440001  \n", "2023-12-29    26.820169    18.740000  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["mad_clip(closes,k=1)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 0, 0, 2, 0, 0])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["def LOWRANGE(S):                       \n", "    # LOWRANGE(LOW)表示当前最低价是近多少周期内最低价的最小值 by jqz1226\n", "    rt = np.zeros(len(S))\n", "    for i in range(1,len(S)):  rt[i] = np.argmin(np.flipud(S[:i]>S[i]))\n", "    return rt.astype('int')\n", "\n", "s = [ 1, 2, 2, 1, 3, 0]\n", "\n", "LOWRANGE(np.array(s))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 1, 2, 3, 4, 1, 6])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["def min_range(s):\n", "    \"\"\"计算序列s中，元素i是此前多少个周期以来的最小值\n", "\n", "    此方法在个别数字上有bug\n", "\n", "    Example:\n", "        >>> s = np.array([5, 7, 7, 6, 5, 8, 2])\n", "        >>> min_range(s)\n", "        array([1, 2, 1, 2, 3, 1, 6])\n", "    \"\"\"\n", "    n = len(s)\n", "\n", "    # handle nan\n", "    filled = np.where(np.isnan(s), -np.inf, s)\n", "    diff = filled[:,None] - filled\n", "    mask = np.triu(np.ones((n, n), dtype=bool), k=1)\n", "    masked = np.ma.array(diff, mask=mask)\n", "\n", "    rng = np.arange(n)\n", "    ret = rng - np.argmax(np.ma.where(masked > 0, rng, -1), axis=1)\n", "    ret[0] = 1\n", "    if filled[1] <= filled[0]:\n", "        ret[1] = 2\n", "    return ret\n", "\n", "s = np.array([5, 7, 7, 6, 5, 8, 2])\n", "min_range(s)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([nan, nan,  1.,  2.,  3.])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["def moving_average(ts, win: int, padding=True)->np.ndarray:\n", "    kernel = np.ones(win) / win\n", "\n", "    arr = np.convolve(ts, kernel, 'valid')\n", "    if padding:\n", "        return np.insert(arr, 0, [np.nan] * (win - 1))\n", "    else:\n", "        return arr\n", "\n", "moving_average(np.arange(5), 3)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["date        asset      \n", "2023-01-03  002457.XSHE    8.310465\n", "2023-01-04  002457.XSHE    8.361222\n", "2023-01-05  002457.XSHE    8.381415\n", "2023-01-06  002457.XSHE    8.386454\n", "2023-01-09  002457.XSHE    8.382696\n", "                             ...   \n", "2023-12-25  002457.XSHE    9.559288\n", "2023-12-26  002457.XSHE    9.566473\n", "2023-12-27  002457.XSHE    9.566579\n", "2023-12-28  002457.XSHE    9.563042\n", "2023-12-29  002457.XSHE    9.561609\n", "Length: 241, dtype: float64"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["def intraday_moving_average(bars: pd.DataFrame)->np.ndarray:\n", "    acc_vol = bars[\"volume\"].cumsum()\n", "    acc_money = barss[\"amount\"].cumsum()\n", "\n", "    return acc_money / acc_vol\n", "\n", "start = datetime.date(2023, 1, 1)\n", "end = datetime.date(2023, 12, 29)\n", "barss = load_bars(start, end, 1)\n", "\n", "intraday_moving_average(barss)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["104 μs ± 2.73 μs per loop (mean ± std. dev. of 7 runs, 10,000 loops each)\n"]}, {"data": {"image/png": "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********************************/fi/fff9+tZ8llLbdu2DR999BHWrFmD+Ph4rF+/Hv/4xz/w4IMP8mdHEATimQb7uY/6f7msrCwA8Eh67e3tGDt2bDhuKaoZjUbce++9aG5uxoYNG8Rv4MzMTHR0dEiO1Wq1UCqV0Gg0yMzMBGB77vHx8eIx7e3tSE9PD9n9R7qXX34ZdXV1Hs2pDz/8MB5++GGMHz/e43tZq9WKzzAzMxPd3d0wm83iDxCz2Yyuri4+ZzdqtRoAkJeXJ9mel5eHU6dOITMzE1VVVZJ9jr84MzIyIJfbGqb5s2VgnZ2duP/++/HAAw/g1ltvBQBceumluPrqqzF58mT+7AiCQDzTwc7hr1HfrZOZmYm0tDSUlpaK2/R6PQ4ePIipU6eG8c6ij9FoxLp169DU1IQNGzZImvwKCgokzxgA9uzZg0mTJkEmk2HKlClQKpWSY2pqalBXV8d/BxcvvPAC3nvvPcn/MjMzsXbtWrz33nv9PucpU6YAsP07mM1mHDhwQNxfWloKi8XC5+wmJSUF+fn52Lt3r2T76dOnMXbsWBQUFODw4cMwGo3ivj179iA5ORlZWVn82eKj2tpa9Pb2Ys6cOeK2iRMnIi8vDwcPHuTPjiAIxDMd7Bx+C8iw2ij3/PPPCwsWLBC2b98unD59WnjggQeE+fPnC21tbeG+tajR19cn/PjHPxaWL18uVFVVCW1tbeL/ent7hYqKCmH27NnC008/LVRVVQnvvPOOMHPmTOGNN94Qz/GLX/xCuOiii4TS0lKhvLxcuP3224WlS5dKZkOQJ9fZOnv27BEKCwuFjRs3ClVVVcKLL74oTJ8+XdixY4d4/O233y5ce+21wpEjR4RDhw4JV155pXDzzTeH6/Yj2qZNm8Tv0/r6euG1114TZs6cKZw4cULo6OgQSkpKhIcfflg4c+aMsHXrVmHu3LnC008/LX6eP1sG19vbKyxevFi46667hBMnTgi1tbXC+vXrhenTpwtfffUVf3YEgPtsnUA8U1/O4Q+GE0EQLBaL8OyzzwpLliwRZs2aJdx0003CwYMHw31bUeXbb78Vpk2b5vV/zz33nCAIgrBr1y7h6quvFmbOnClccMEFwvr16yXn0Ov1wmOPPSYsWLBAOOuss4TVq1cLlZWV4fhyooprOBEEQfjoo4+E733ve8LMmTOFSy+9VHjvvfckx2u1WuH+++8XiouLheLiYuHee+8VWltbQ33bUeOVV14RLrzwQmHmzJnC8uXLha+//lrcd+zYMWHFihXCrFmzhMWLFwtPPvmkYDKZxP382eKbY8eOCXfccYf43/7VV18tfPjhh+J+/uzwj3s4EYTAPNPBzuEPmSC4lDMkIiIiCrNRP+aEiIiIIgvDCREREUUUhhMiIiKKKAwnREREFFEYToiIiCiiMJwQERFRRGE4ISIioojCcEJEREQRheGEiIiIIgrDCREREUUUhhMiIiKKKAwnREREFFH+P0O54hUo0p5SAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from numpy.lib.stride_tricks import as_strided\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def windowed_view(x, window_size):\n", "    \"\"\"Creat a 2d windowed view of a 1d array.\n", "\n", "    `x` must be a 1d numpy array.\n", "\n", "    `numpy.lib.stride_tricks.as_strided` is used to create the view.\n", "    The data is not copied.\n", "\n", "    Example:\n", "\n", "    >>> x = np.array([1, 2, 3, 4, 5, 6])\n", "    >>> windowed_view(x, 3)\n", "    array([[1, 2, 3],\n", "           [2, 3, 4],\n", "           [3, 4, 5],\n", "           [4, 5, 6]])\n", "    \"\"\"\n", "    y = as_strided(x, shape=(x.size - window_size + 1, window_size),\n", "                   strides=(x.strides[0], x.strides[0]))\n", "    return y\n", "\n", "\n", "def rolling_max_dd(x, window_size, min_periods=1):\n", "    \"\"\"Compute the rolling maximum drawdown of `x`.\n", "\n", "    `x` must be a 1d numpy array.\n", "    `min_periods` should satisfy `1 <= min_periods <= window_size`.\n", "\n", "    Returns an 1d array with length `len(x) - min_periods + 1`.\n", "    \"\"\"\n", "    if min_periods < window_size:\n", "        pad = np.empty(window_size - min_periods)\n", "        pad.fill(x[0])\n", "        x = np.concatenate((pad, x))\n", "    y = windowed_view(x, window_size)\n", "    running_max_y = np.maximum.accumulate(y, axis=1)\n", "    dd = y - running_max_y\n", "    return dd.min(axis=1)\n", "\n", "\n", "np.random.seed(0)\n", "n = 100\n", "s = np.random.randn(n).cumsum()\n", "win = 20\n", "\n", "mdd = rolling_max_dd(s, win, min_periods=1)\n", "\n", "plt.plot(s, 'b')\n", "plt.plot(mdd, 'g.')\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["'%.2f'"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}, {"data": {"text/plain": ["array([2.09, 2.27, 2.21, 2.12, 2.19])"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}, {"data": {"text/plain": ["array([-1.68, -2.4 , -1.97, -1.7 , -1.46])"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["%precision 2\n", "\n", "from IPython.core.interactiveshell import InteractiveShell\n", "InteractiveShell.ast_node_interactivity = \"all\"\n", "\n", "np.random.seed(78)\n", "s = np.random.randn(100)\n", "\n", "hbound = np.percentile(s, 95)\n", "lbound = np.percentile(s, 5)\n", "\n", "s[s> hbound]\n", "s[s< lbound]"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2.52636824, 1.76992139, 1.64850401, 1.63159743, 1.8219151 ,\n", "       1.90506364])"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["s[s>1.63]"]}, {"cell_type": "code", "execution_count": 115, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 1, 2, 1, 4, 6]"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["def min_range_loop(s):\n", "    minranges = [1]\n", "    for i in range(1, len(s)):\n", "        for j in range(i-1, -1, -1):\n", "            if s[j] < s[i]:\n", "                minranges.append(i - j)\n", "                break\n", "        else:\n", "            minranges.append(i+1)\n", "    return minranges\n", "\n", "# s = np.random.randn(10000)\n", "\n", "min_range_loop(s)\n"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["60.4 ms ± 2.02 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["def LOWRANGE(S):                       \n", "    # LOWRANGE(LOW)表示当前最低价是近多少周期内最低价的最小值 by jqz1226\n", "    rt = np.zeros(len(S))\n", "    for i in range(1,len(S)):  rt[i] = np.argmin(np.flipud(S[:i]>S[i]))\n", "    return rt.astype('int')\n", "\n", "%timeit LOWRANGE(s)\n"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 2, 3, 2, 2, 1])"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["s = [1, 2, 2, 3, 2, 0]\n", "np.flipud(s)"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["s = [1, 2, 2, 3, 2, 0]\n", "np.all(np.flipud(s) == s[::-1])"]}, {"cell_type": "code", "execution_count": 118, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["def min_range(s):\n", "    \"\"\"计算序列s中，元素i是此前多少个周期以来的最小值\n", "\n", "    此方法在个别数字上有bug\n", "\n", "    Example:\n", "        >>> s = np.array([5, 7, 7, 6, 5, 8, 2])\n", "        >>> min_range(s)\n", "        array([1, 2, 1, 2, 3, 1, 6])\n", "    \"\"\"\n", "    n = len(s)\n", "\n", "    diff = s[:,None] - s\n", "    mask = np.triu(np.ones((n, n), dtype=bool), k=1)\n", "    masked = np.ma.array(diff, mask=mask)\n", "\n", "    rng = np.arange(n)\n", "    ret = rng - np.argmax(np.ma.where(masked > 0, rng, -1), axis=1)\n", "    ret[0] = 1\n", "    if s[1] <= s[0]:\n", "        ret[1] = 2\n", "    return ret\n", "\n", "s = np.random.randn(100000)\n", "%timeit min_range(s)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def min_range_loop(s):\n", "    minranges = [1]\n", "    for i in range(1, len(s)):\n", "        for j in range(i-1, -1, -1):\n", "            if s[j] < s[i]:\n", "                minranges.append(i - j)\n", "                break\n", "        else:\n", "            minranges.append(i+1)\n", "    return minranges\n", "\n", "# s = np.random.randn(10000)\n", "\n"]}, {"cell_type": "code", "execution_count": 101, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0, -1, -1, -2, -1,  1],\n", "       [ 1,  0,  0, -1,  0,  2],\n", "       [ 1,  0,  0, -1,  0,  2],\n", "       [ 2,  1,  1,  0,  1,  3],\n", "       [ 1,  0,  0, -1,  0,  2],\n", "       [-1, -2, -2, -3, -2,  0]])"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["s[:,None] - s"]}, {"cell_type": "code", "execution_count": 106, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1, 2, 3, 4, 5])"]}, "execution_count": 106, "metadata": {}, "output_type": "execute_result"}], "source": ["s = np.array([1, 2, 2, 3, 2, 0])\n", "diff = s[-1] - s\n", "rng = np.arange(len(diff))\n", "rng - np.argmax(np.ma.where(diff > 0, rng, -1))\n"]}, {"cell_type": "code", "execution_count": 110, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1, 2])"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}], "source": ["s = np.array([1, 2, 2])\n", "diff = s[-1] - s\n", "rng = np.arange(len(diff))\n", "rng - np.argmax(np.ma.where(diff > 0, rng, -1))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import akshare as ak\n", "df = ak.stock_zh_index_daily(symbol=\"sh000001\")\n", "\n", "df_one_year = df.tail(250)\n", "df_one_year[\"minrange\"] = min_range_loop(df_one_year[\"volume\"].to_numpy())\n", "\n", "ax = df_one_year.plot(x='date', y='close', label='close', color='blue', secondary_y=False)\n", "df_one_year.plot(x='date', y='minrange', label='Min Range', color='red', secondary_y=True, ax=ax)\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ax = df_one_year.plot(x='date', y='close', label='close', color='blue', secondary_y=False)\n", "df_one_year.plot(x='date', y='minrange', label='Min Range', color='red', secondary_y=True, ax=ax)\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 生成模拟数据\n", "np.random.seed(42)\n", "dates = pd.date_range(start='2023-01-01', periods=100, freq='D')\n", "prices = np.random.normal(0, 1, 100).cumsum() + 100\n", "volumes = np.abs(np.random.normal(0, 10, 100)) * 1000\n", "\n", "# 创建DataFrame\n", "df = pd.DataFrame({'Date': dates, 'Close': prices, 'Volume': volumes})\n", "\n", "# 模拟老鸭头形态\n", "# 鸭颈部\n", "df.loc[0:20, 'Close'] += 20\n", "df.loc[0:20, 'Volume'] += 5000\n", "\n", "# 鸭顶部\n", "df.loc[21:40, 'Close'] -= 10\n", "df.loc[21:40, 'Volume'] -= 2000\n", "\n", "# 鸭头部\n", "df.loc[41:60, 'Close'] += 10\n", "df.loc[41:60, 'Volume'] += 3000\n", "\n", "# 鸭嘴部\n", "df.loc[61:80, 'Close'] -= 5\n", "df.loc[61:80, 'Volume'] += 1000\n", "\n", "# 突破鸭嘴部\n", "df.loc[81:100, 'Close'] += 15\n", "df.loc[81:100, 'Volume'] += 4000\n", "\n", "# 绘制图形\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10), sharex=True)\n", "\n", "# 绘制股价\n", "ax1.plot(df['Date'], df['Close'], label='Close Price', color='blue')\n", "ax1.axhline(y=df['Close'][20], color='red', linestyle='--', label='Neckline')\n", "ax1.axhline(y=df['Close'][60], color='green', linestyle='--', label='Mouthline')\n", "ax1.fill_between(df['Date'], df['Close'][20], df['Close'][60], where=(df['Close'] > df['Close'][60]) & (df['Close'] < df['Close'][20]), alpha=0.3, color='yellow')\n", "ax1.set_title('Old Duck Head Pattern')\n", "ax1.set_ylabel('Price')\n", "ax1.legend()\n", "\n", "# 绘制成交量\n", "ax2.bar(df['Date'], df['Volume'], color='gray', alpha=0.5)\n", "ax2.set_xlabel('Date')\n", "ax2.set_ylabel('Volume')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "coursea", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}