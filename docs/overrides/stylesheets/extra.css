img {
    width: calc(100%);
    margin: 0 auto;
    display: block;
}

img[alt="25%"] {
    width: 25%;
    margin-left: auto;
    margin-right: auto;
    display: block;
}

img[alt="33%"] {
    width: 33%;
    margin-left: auto;
    margin-right: auto;
    display: block;
}

img[alt="50%"] {
    width: 50%;
    margin-left: auto;
    margin-right: auto;
    display: block;
}

img[alt="66%"] {
    width: 66%;
    margin-left: auto;
    margin-right: auto;
    display: block;
}

img[alt="75%"] {
    width: 75%;
    margin-left: auto;
    margin-right: auto;
    display: block;
}


img[alt="100%"] {
    width: 75%;
    margin-left: auto;
    margin-right: auto;
    display: block;
}

img[alt="R50"] {
    position: relative;
    float: right;
    width: 45%;
    margin-left: 2vw !important;
}

img[alt="R33"] {
    position: relative;
    float: right;
    width: 33%;
    margin-left: 2vw !important;
}

img[alt="L50"] {
    position: relative;
    float: left;
    width: 45%;
    margin-right: 2vw !important;
}

img[alt="L33"] {
    position: relative;
    float: left;
    width: 33%;
    margin-right: 2vw !important;
}

h2 {
    padding: 0.2rem 0.5rem;
    background-color: rgba(232, 248, 246, 0.2);
    border-left: .4vw solid rgba(85, 188, 165, 0.5);
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .01), 0 1px 2px 0 rgba(0, 0, 0, .01), 0 3px 1px -2px rgba(0, 0, 0, .01);
    text-transform: uppercase;
}

p {
    text-align: justify;
    line-height: 1.8;
    font-size: 14px !important;
}

li {
    font-size: 13px !important;
}

text-right {
    text-align: right;
    display: block;
}

claimer {
    color: #a8a8a8;
    font-style: italic;
    font-size: 0.8em;
    display: block;
    text-align: center;
}

red {
    color: color-mix(in srgb, #e60000, black 15%);
    display: inline;
}

cap {
    /*图片标题*/
    display: inline-block;
    width: 100%;
    text-align: center;
    font-size: .8rem;
    color: grey;
    font-style: italic;
    position: relative;
}

/* .header-img-desc {
    margin: 2.5em 0;

    p {
        color: #939393;
        font-size: 1.1em;
        line-height: 1.6em;
    }
} */
