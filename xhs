cmd=$1
file=$2
home="$HOME"

if [ "$#" == 3 ]; then
    theme="$home/workspace/slidev_themes/themes/$3"
else
    theme="$home/workspace/slidev_themes/themes/portrait-flora"
    echo $theme
fi

project_dir=`pwd`
cd ~/workspace/slidev_themes
echo "进入目录：`pwd`"
if [ $cmd = "serve" ]; then
  npx --verbose slidev $project_dir/$file -t $theme --remote 
elif [ $cmd = "tunnel" ]; then
  npx slidev $project_dir/$file -t $theme --remote --tunnel --log info
elif [ $cmd = "pdf" ]; then
  mkdir -p /tmp/xhs
  mkdir -p $project_dir/xhs_output/
  rm -rf /tmp/xhs/*
  npx slidev export --format pdf -t $theme --output /tmp/quantide.pdf $project_dir/$file --with-toc
  rm -f ~/workspace/zillionare/xhs_output/*
  cp -r /tmp/quantide.pdf $project_dir/xhs_output/
else
    mkdir -p /tmp/xhs
    rm -rf /tmp/xsh/*
    npx slidev export --format png -t $theme --output /tmp/xhs $project_dir/$file --wait 10000 --timeout 60000 --per-slide
fi
