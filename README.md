I'm a software developer, quantitative trader and entrepreneur。 Teaching machine learning, trading and software development. Author of 'Best Practices for Python'. 

我是一名软件工程师、量化交易人和创业者。《Python高效编程最佳实践指南》的作者。我也是一系列开源软件的开发者或者维护者。
>[!tip]
>我们教授《匡醍.量化24课》、《匡醍.因子分析与机器学习策略》和《匡醍.量化人的Numpy和Pandas》等系列课程，帮助你从入门到精通，完全掌握量化交易。课程都配有视频、在线运行的Notebook、习题和答疑。请前往公众号 Quantide 咨询

## 最新文章

<div class="as-grid m-t-md">
<div class="card-columns">
    
<div>
<h3>PDF is all you need(3)</h3>
<img src="https://cdn.jsdelivr.net/gh/zillionare/imgbed2@main/images/2025/07/haley-phelps-S-llxYh3GzI-unsplash.jpg" style="height: 200px" align="right"/>
<p><span>内容摘要:<br></span>当小球在高尔顿板上穿梭，神奇地绘出正态分布曲线时，你是否想过这背后隐藏着怎样的数学奥秘？从概率密度到期望值，从离散到连续，这篇文章将带你揭开随机世界的神秘面纱。想知道如何用积分计算抄底成功率吗？准备好，数学的魔法即将开始！</p>

<p><span style="margin-right:20px">发表于 2025-08-08 人气 934 </span><span><a href="https://www.jieyu.ai/blog/posts/algo/pdf-is-all-you-need-3/">点击阅读</a></span></p>

</div><!--end-article-->
<br/>
<br/>


<div>
<h3>『Moonshot is all you need』 01 - 5分钟上手极简量化回测框架</h3>
<img src="https://images.jieyu.ai/images/hot/mybook/by-swimming-pool.jpg" style="height: 200px" align="right"/>
<p><span>内容摘要:<br></span>中金的一个基本面策略，年化收益率达到了29%。但是，复现并不容易。它集合了事件选股和因子选股两种框架 -- 这导致没有现成的回测框架可用，同时，各种周期的数据对齐也让人如陷迷宫。在这个系列的视频里，你将和我一起，从零打造一个名为 Moonshot 的回测框架：清晰、模块化、易上手、易拓展。本期为第一期。我们将介绍关键思想，核心数据结构，链式调用技巧</p>

<p><span style="margin-right:20px">发表于 2025-08-06 人气 198 </span><span><a href="https://www.jieyu.ai/blog/posts/tools/moonshot/moonshot-is-all-you-need-1/">点击阅读</a></span></p>

</div><!--end-article-->
<br/>
<br/>


<div>
<h3>除了编程，量化人还能怎么用AI？</h3>
<img src="https://cdn.jsdelivr.net/gh/zillionare/imgbed2@main/images/2025/08/brooke-lark-8beTH4VkhLI-unsplash.jpg" style="height: 200px" align="right"/>
<p><span>内容摘要:<br></span>当量化人面临信息过载的困境时，AI悄然成为破局关键。从Grok的私人定制资讯，到豆包和NotebookLM将研报秒变播客，再到TradingAgents项目——一个能模拟真实交易团队、年化收益提升30%的神秘框架。这个由UC伯克利开发的多智能体系统，究竟如何让一个人拥有整个交易团队的决策能力？</p>

<p><span style="margin-right:20px">发表于 2025-08-05 人气 407 </span><span><a href="https://www.jieyu.ai/blog/posts/tools/AI-tools/how-else-can-quant-professionals-use-ai-besides-programming/">点击阅读</a></span></p>

</div><!--end-article-->
<br/>
<br/>

</div>
</div>

更多精彩好文，请访问[匡醍量化](https://www.jieyu.ai)

