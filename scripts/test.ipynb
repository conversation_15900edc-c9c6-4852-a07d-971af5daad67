{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9379f5e5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>asset</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-01-26</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.299999</td>\n", "      <td>23.320000</td>\n", "      <td>22.299999</td>\n", "      <td>22.370001</td>\n", "      <td>112672055.0</td>\n", "      <td>2.558576e+09</td>\n", "      <td>22.309999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-01-27</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.309999</td>\n", "      <td>23.469999</td>\n", "      <td>22.309999</td>\n", "      <td>23.080000</td>\n", "      <td>129415272.0</td>\n", "      <td>2.976801e+09</td>\n", "      <td>22.780001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-01-28</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.780001</td>\n", "      <td>23.180000</td>\n", "      <td>22.450001</td>\n", "      <td>22.809999</td>\n", "      <td>85747696.0</td>\n", "      <td>1.948881e+09</td>\n", "      <td>22.809999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-01-29</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.809999</td>\n", "      <td>23.540001</td>\n", "      <td>22.709999</td>\n", "      <td>23.090000</td>\n", "      <td>124025841.0</td>\n", "      <td>2.864101e+09</td>\n", "      <td>23.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-02-01</th>\n", "      <th>000001.XSHE</th>\n", "      <td>23.000000</td>\n", "      <td>24.990000</td>\n", "      <td>22.700001</td>\n", "      <td>24.549999</td>\n", "      <td>147523930.0</td>\n", "      <td>3.529557e+09</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                             open       high        low      close  \\\n", "date       asset                                                     \n", "2021-01-26 000001.XSHE  22.299999  23.320000  22.299999  22.370001   \n", "2021-01-27 000001.XSHE  22.309999  23.469999  22.309999  23.080000   \n", "2021-01-28 000001.XSHE  22.780001  23.180000  22.450001  22.809999   \n", "2021-01-29 000001.XSHE  22.809999  23.540001  22.709999  23.090000   \n", "2021-02-01 000001.XSHE  23.000000  24.990000  22.700001  24.549999   \n", "\n", "                             volume        amount      price  \n", "date       asset                                              \n", "2021-01-26 000001.XSHE  112672055.0  2.558576e+09  22.309999  \n", "2021-01-27 000001.XSHE  129415272.0  2.976801e+09  22.780001  \n", "2021-01-28 000001.XSHE   85747696.0  1.948881e+09  22.809999  \n", "2021-01-29 000001.XSHE  124025841.0  2.864101e+09  23.000000  \n", "2021-02-01 000001.XSHE  147523930.0  3.529557e+09        NaN  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.path.insert(0, \"scripts\")\n", "\n", "start = datetime.date(2021, 1, 1)\n", "end = datetime.date(2021, 2,1)\n", "lb = load_bars(start, end, 1)\n", "lb.tail()"]}, {"cell_type": "code", "execution_count": 2, "id": "3d4d8e5e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>asset</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1970-01-01 05:36:50.126</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.30</td>\n", "      <td>23.32</td>\n", "      <td>22.30</td>\n", "      <td>22.37</td>\n", "      <td>1126720.55</td>\n", "      <td>2558575.511</td>\n", "      <td>22.31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1970-01-01 05:36:50.127</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.31</td>\n", "      <td>23.47</td>\n", "      <td>22.31</td>\n", "      <td>23.08</td>\n", "      <td>1294152.72</td>\n", "      <td>2976800.955</td>\n", "      <td>22.78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1970-01-01 05:36:50.128</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.78</td>\n", "      <td>23.18</td>\n", "      <td>22.45</td>\n", "      <td>22.81</td>\n", "      <td>857476.96</td>\n", "      <td>1948881.146</td>\n", "      <td>22.81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1970-01-01 05:36:50.129</th>\n", "      <th>000001.XSHE</th>\n", "      <td>22.81</td>\n", "      <td>23.54</td>\n", "      <td>22.71</td>\n", "      <td>23.09</td>\n", "      <td>1240258.41</td>\n", "      <td>2864101.419</td>\n", "      <td>23.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1970-01-01 05:36:50.201</th>\n", "      <th>000001.XSHE</th>\n", "      <td>23.00</td>\n", "      <td>24.99</td>\n", "      <td>22.70</td>\n", "      <td>24.55</td>\n", "      <td>1475239.30</td>\n", "      <td>3529556.986</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      open   high    low  close      volume  \\\n", "date                    asset                                                 \n", "1970-01-01 05:36:50.126 000001.XSHE  22.30  23.32  22.30  22.37  1126720.55   \n", "1970-01-01 05:36:50.127 000001.XSHE  22.31  23.47  22.31  23.08  1294152.72   \n", "1970-01-01 05:36:50.128 000001.XSHE  22.78  23.18  22.45  22.81   857476.96   \n", "1970-01-01 05:36:50.129 000001.XSHE  22.81  23.54  22.71  23.09  1240258.41   \n", "1970-01-01 05:36:50.201 000001.XSHE  23.00  24.99  22.70  24.55  1475239.30   \n", "\n", "                                          amount  price  \n", "date                    asset                            \n", "1970-01-01 05:36:50.126 000001.XSHE  2558575.511  22.31  \n", "1970-01-01 05:36:50.127 000001.XSHE  2976800.955  22.78  \n", "1970-01-01 05:36:50.128 000001.XSHE  1948881.146  22.81  \n", "1970-01-01 05:36:50.129 000001.XSHE  2864101.419  23.00  \n", "1970-01-01 05:36:50.201 000001.XSHE  3529556.986    NaN  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["lbt = load_bars_tushare(start, end, [\"000001.XSHE\"])\n", "lbt.tail()"]}, {"cell_type": "code", "execution_count": 5, "id": "bdc814e4", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "Can only compare identically-labeled (both index and columns) DataFrame objects", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[32m/var/folders/b5/73vzvtdn4pn_8wt6rpd2tb_w0000gn/T/ipykernel_80538/565329465.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m lbt.compare(lb)\n", "\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/frame.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m(self, other, align_axis, keep_shape, keep_equal, result_names)\u001b[39m\n\u001b[32m   8596\u001b[39m         keep_shape: bool = \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m   8597\u001b[39m         keep_equal: bool = \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m   8598\u001b[39m         result_names: Suffixes = (\u001b[33m\"self\"\u001b[39m, \u001b[33m\"other\"\u001b[39m),\n\u001b[32m   8599\u001b[39m     ) -> DataFrame:\n\u001b[32m-> \u001b[39m\u001b[32m8600\u001b[39m         return super().compare(\n\u001b[32m   8601\u001b[39m             other=other,\n\u001b[32m   8602\u001b[39m             align_axis=align_axis,\n\u001b[32m   8603\u001b[39m             keep_shape=keep_shape,\n", "\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/generic.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m(self, other, align_axis, keep_shape, keep_equal, result_names)\u001b[39m\n\u001b[32m  10136\u001b[39m             raise TypeError(\n\u001b[32m  10137\u001b[39m                 f\"can only compare '{cls_self}' (not '{cls_other}') with '{cls_self}'\"\n\u001b[32m  10138\u001b[39m             )\n\u001b[32m  10139\u001b[39m \n\u001b[32m> \u001b[39m\u001b[32m10140\u001b[39m         mask = ~((self == other) | (self.isna() & other.isna()))\n\u001b[32m  10141\u001b[39m         mask.fillna(\u001b[38;5;28;01mTrue\u001b[39;00m, inplace=\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m  10142\u001b[39m \n\u001b[32m  10143\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28;01mnot\u001b[39;00m keep_equal:\n", "\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/ops/common.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m(self, other)\u001b[39m\n\u001b[32m     72\u001b[39m                     \u001b[38;5;28;01mreturn\u001b[39;00m NotImplemented\n\u001b[32m     73\u001b[39m \n\u001b[32m     74\u001b[39m         other = item_from_zerodim(other)\n\u001b[32m     75\u001b[39m \n\u001b[32m---> \u001b[39m\u001b[32m76\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m method(self, other)\n", "\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/arraylike.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m(self, other)\u001b[39m\n\u001b[32m     38\u001b[39m     @unpack_zerodim_and_defer(\u001b[33m\"__eq__\"\u001b[39m)\n\u001b[32m     39\u001b[39m     \u001b[38;5;28;01mdef\u001b[39;00m __eq__(self, other):\n\u001b[32m---> \u001b[39m\u001b[32m40\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m self._cmp_method(other, operator.eq)\n", "\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/frame.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m(self, other, op)\u001b[39m\n\u001b[32m   7901\u001b[39m     \u001b[38;5;28;01mdef\u001b[39;00m _cmp_method(self, other, op):\n\u001b[32m   7902\u001b[39m         axis: Literal[\u001b[32m1\u001b[39m] = \u001b[32m1\u001b[39m  \u001b[38;5;66;03m# only relevant for Series other case\u001b[39;00m\n\u001b[32m   7903\u001b[39m \n\u001b[32m-> \u001b[39m\u001b[32m7904\u001b[39m         self, other = self._align_for_op(other, axis, flex=\u001b[38;5;28;01mFalse\u001b[39;00m, level=\u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m   7905\u001b[39m \n\u001b[32m   7906\u001b[39m         \u001b[38;5;66;03m# See GH#4537 for discussion of scalar op behavior\u001b[39;00m\n\u001b[32m   7907\u001b[39m         new_data = self._dispatch_frame_op(other, op, axis=axis)\n", "\u001b[32m~/miniforge3/envs/zillionare/lib/python3.12/site-packages/pandas/core/frame.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m(self, other, axis, flex, level)\u001b[39m\n\u001b[32m   8199\u001b[39m                     left, right = left.align(\n\u001b[32m   8200\u001b[39m                         right, join=\u001b[33m\"outer\"\u001b[39m, level=level, copy=\u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m   8201\u001b[39m                     )\n\u001b[32m   8202\u001b[39m                 \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m8203\u001b[39m                     raise ValueError(\n\u001b[32m   8204\u001b[39m                         \u001b[33m\"Can only compare identically-labeled (both index and columns) \"\u001b[39m\n\u001b[32m   8205\u001b[39m                         \u001b[33m\"DataFrame objects\"\u001b[39m\n\u001b[32m   8206\u001b[39m                     )\n", "\u001b[31mValueError\u001b[39m: Can only compare identically-labeled (both index and columns) DataFrame objects"]}], "source": ["lbt.compare(lb)"]}, {"cell_type": "code", "execution_count": 12, "id": "737171d3", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.stats import norm\n", "\n", "# 设置中文显示\n", "plt.rcParams[\"font.family\"] = [\"WenQuanYi Micro Hei\"]\n", "plt.rcParams[\"axes.unicode_minus\"] = False  # 正确显示负号\n", "\n", "def plot_normal_histogram(sample_size=100, bin_width=0.3, x_range=(-3.6, 3.6), \n", "                          seed=42, show_counts=True):\n", "    \"\"\"\n", "    绘制标准正态分布随机样本的直方图\n", "    \n", "    参数:\n", "    sample_size: 样本数量\n", "    bin_width: 区间宽度\n", "    x_range: 元组，指定x轴范围 (start, end)\n", "    seed: 随机种子，用于结果复现\n", "    show_counts: 是否在柱子上方显示频数\n", "    \"\"\"\n", "    # 生成标准正态分布随机数\n", "    np.random.seed(seed)\n", "    X = norm.rvs(size=sample_size)\n", "    \n", "    # 生成区间分割点\n", "    start, end = x_range\n", "    bins = np.arange(start, end + bin_width, bin_width)\n", "    \n", "    # 创建画布\n", "    plt.figure(figsize=(12, 6))\n", "    \n", "    # 绘制直方图\n", "    n, bins, patches = plt.hist(\n", "        X, \n", "        bins=bins, \n", "        edgecolor='black', \n", "        alpha=0.7, \n", "        color='skyblue'\n", "    )\n", "    \n", "    # 添加标题和标签\n", "    plt.title(f'标准正态分布随机数直方图 (区间宽度: {bin_width})', fontsize=14)\n", "    plt.xlabel('数值区间', fontsize=12)\n", "    plt.ylabel('频数', fontsize=12)\n", "    \n", "    # 设置x轴刻度\n", "    # 为避免刻度过于密集，根据区间宽度动态调整显示间隔\n", "    tick_interval = 1 if bin_width >= 0.5 else 2 if bin_width >= 0.2 else 3\n", "    plt.xticks(bins[::tick_interval], rotation=45, fontsize=10)\n", "    \n", "    # 添加网格线\n", "    plt.grid(axis='y', alpha=0.3)\n", "    \n", "    # 在每个柱子上方显示频数\n", "    if show_counts:\n", "        for i, count in enumerate(n):\n", "            if count > 0:\n", "                plt.text(\n", "                    bins[i] + bin_width/2, \n", "                    count + 0.5, \n", "                    f'{int(count)}', \n", "                    ha='center', \n", "                    fontsize=9\n", "                )\n", "    \n", "    plt.tight_layout()\n", "    return plt  # 返回plt对象，方便进一步处理或保存\n", "\n", "plot_normal_histogram(sample_size=100_000, bin_width=0.3)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 13, "id": "875ef303", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_normal_histogram(sample_size=100_000, bin_width=0.1)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 21, "id": "8da41e8b", "metadata": {}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_cefa8 th {\n", "  background-color: black;\n", "  color: white;\n", "  font-weight: bold;\n", "  padding: 10px 20px;\n", "}\n", "#T_cefa8 table {\n", "  width: 100%;\n", "  min-width: 800px;\n", "}\n", "#T_cefa8 td {\n", "  padding: 0px 60px;\n", "  text-align: center;\n", "}\n", "#T_cefa8_row1_col0, #T_cefa8_row1_col1, #T_cefa8_row1_col2, #T_cefa8_row1_col3, #T_cefa8_row2_col0, #T_cefa8_row2_col1, #T_cefa8_row2_col2, #T_cefa8_row2_col3, #T_cefa8_row4_col0, #T_cefa8_row4_col1, #T_cefa8_row4_col2, #T_cefa8_row4_col3 {\n", "  color: red;\n", "}\n", "</style>\n", "<table id=\"T_cefa8\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_cefa8_level0_col0\" class=\"col_heading level0 col0\" >序号</th>\n", "      <th id=\"T_cefa8_level0_col1\" class=\"col_heading level0 col1\" >结果1</th>\n", "      <th id=\"T_cefa8_level0_col2\" class=\"col_heading level0 col2\" >结果2</th>\n", "      <th id=\"T_cefa8_level0_col3\" class=\"col_heading level0 col3\" >结果3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_cefa8_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_cefa8_row0_col0\" class=\"data row0 col0\" >1</td>\n", "      <td id=\"T_cefa8_row0_col1\" class=\"data row0 col1\" >H</td>\n", "      <td id=\"T_cefa8_row0_col2\" class=\"data row0 col2\" >H</td>\n", "      <td id=\"T_cefa8_row0_col3\" class=\"data row0 col3\" >H</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_cefa8_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_cefa8_row1_col0\" class=\"data row1 col0\" >2</td>\n", "      <td id=\"T_cefa8_row1_col1\" class=\"data row1 col1\" >H</td>\n", "      <td id=\"T_cefa8_row1_col2\" class=\"data row1 col2\" >H</td>\n", "      <td id=\"T_cefa8_row1_col3\" class=\"data row1 col3\" >T</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_cefa8_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_cefa8_row2_col0\" class=\"data row2 col0\" >3</td>\n", "      <td id=\"T_cefa8_row2_col1\" class=\"data row2 col1\" >H</td>\n", "      <td id=\"T_cefa8_row2_col2\" class=\"data row2 col2\" >T</td>\n", "      <td id=\"T_cefa8_row2_col3\" class=\"data row2 col3\" >H</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_cefa8_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_cefa8_row3_col0\" class=\"data row3 col0\" >4</td>\n", "      <td id=\"T_cefa8_row3_col1\" class=\"data row3 col1\" >H</td>\n", "      <td id=\"T_cefa8_row3_col2\" class=\"data row3 col2\" >T</td>\n", "      <td id=\"T_cefa8_row3_col3\" class=\"data row3 col3\" >T</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_cefa8_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_cefa8_row4_col0\" class=\"data row4 col0\" >5</td>\n", "      <td id=\"T_cefa8_row4_col1\" class=\"data row4 col1\" >T</td>\n", "      <td id=\"T_cefa8_row4_col2\" class=\"data row4 col2\" >H</td>\n", "      <td id=\"T_cefa8_row4_col3\" class=\"data row4 col3\" >H</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_cefa8_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_cefa8_row5_col0\" class=\"data row5 col0\" >6</td>\n", "      <td id=\"T_cefa8_row5_col1\" class=\"data row5 col1\" >T</td>\n", "      <td id=\"T_cefa8_row5_col2\" class=\"data row5 col2\" >H</td>\n", "      <td id=\"T_cefa8_row5_col3\" class=\"data row5 col3\" >T</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_cefa8_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_cefa8_row6_col0\" class=\"data row6 col0\" >7</td>\n", "      <td id=\"T_cefa8_row6_col1\" class=\"data row6 col1\" >T</td>\n", "      <td id=\"T_cefa8_row6_col2\" class=\"data row6 col2\" >T</td>\n", "      <td id=\"T_cefa8_row6_col3\" class=\"data row6 col3\" >H</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_cefa8_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_cefa8_row7_col0\" class=\"data row7 col0\" >8</td>\n", "      <td id=\"T_cefa8_row7_col1\" class=\"data row7 col1\" >T</td>\n", "      <td id=\"T_cefa8_row7_col2\" class=\"data row7 col2\" >T</td>\n", "      <td id=\"T_cefa8_row7_col3\" class=\"data row7 col3\" >T</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x1218fe450>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "# 创建数据\n", "data = {\n", "    '序号': [1, 2, 3, 4, 5, 6, 7, 8],\n", "    '结果1': ['H', 'H', 'H', 'H', 'T', 'T', 'T', 'T'],\n", "    '结果2': ['H', 'H', 'T', 'T', 'H', 'H', 'T', 'T'],\n", "    '结果3': ['H', 'T', 'H', 'T', 'H', 'T', 'H', 'T']\n", "}\n", "\n", "# 创建DataFrame\n", "df = pd.DataFrame(data)\n", "\n", "# 定义高亮函数：有且仅有两个H的行显示为红色\n", "def highlight_two_h(row):\n", "    # 计算每行H的数量（跳过序号列）\n", "    h_count = sum(1 for value in row[1:] if value == 'H')\n", "    return ['color: red' if h_count == 2 else '' for _ in row]\n", "\n", "# 应用样式：表头黑底白字，表格宽度加大一倍，符合条件的行红色显示\n", "styled_df = df.style \\\n", "    .apply(highlight_two_h, axis=1) \\\n", "    .set_table_styles([\n", "        {\n", "            'selector': 'th',  # 表头样式\n", "            'props': [\n", "                ('background-color', 'black'),\n", "                ('color', 'white'),\n", "                ('font-weight', 'bold'),\n", "                ('padding', '10px 20px')  # 增加表头内边距\n", "            ]\n", "        },\n", "        {\n", "            'selector': 'table',  # 表格整体样式\n", "            'props': [\n", "                ('width', '100%'),  # 表格宽度占容器100%\n", "                ('min-width', '800px')  # 最小宽度设置，确保表格足够宽\n", "            ]\n", "        },\n", "        {\n", "            'selector': 'td',  # 单元格样式\n", "            'props': [\n", "                ('padding', '0px 60px'),  # 增加单元格内边距\n", "                ('text-align', 'center')  # 内容居中显示\n", "            ]\n", "        }\n", "    ])\n", "\n", "# 显示表格\n", "styled_df\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "2e0e2408", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "zillionare", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}